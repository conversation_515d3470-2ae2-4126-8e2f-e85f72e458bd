import { Request, Response } from 'express'
import { SchedulerService } from '@/services/scheduler.service'

// 获取所有任务状态
export const getTaskStatus = async (_req: Request, res: Response) => {
  try {
    const tasks = SchedulerService.getTaskStatus()

    return res.json({
      success: true,
      data: {
        tasks,
        totalTasks: tasks.length,
        activeTasks: tasks.filter(t => t.isActive).length,
        runningTasks: tasks.filter(t => t.isRunning).length
      }
    })
  } catch (error) {
    console.error('Get task status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取任务状态失败'
    })
  }
}

// 手动执行任务（管理员功能）
export const executeTask = async (req: Request, res: Response) => {
  try {
    const { taskName } = req.params

    if (!taskName) {
      return res.status(400).json({
        success: false,
        message: 'taskName参数缺失'
      })
    }

    // 这里可以添加手动执行特定任务的逻辑
    // 例如：await SchedulerService.executeTask(taskName)

    return res.json({
      success: true,
      message: `任务 "${taskName}" 已手动执行`
    })
  } catch (error) {
    console.error('Execute task error:', error)
    return res.status(500).json({
      success: false,
      message: '任务执行失败'
    })
  }
}

// 获取系统统计信息
export const getSystemStats = async (_req: Request, res: Response) => {
  try {
    // 这里可以添加更多系统统计信息
    const stats = {
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      nodeVersion: process.version,
      platform: process.platform,
      loadAverage: require('os').loadavg(),
      freeMemory: require('os').freemem(),
      totalMemory: require('os').totalmem()
    }

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get system stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统统计失败'
    })
  }
}