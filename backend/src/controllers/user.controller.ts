import { Request, Response } from 'express'
import { z } from 'zod'
import { UserService } from '@/services/user.service'
import { AuditService } from '@/services/audit.service'

// 验证Schema
const createUserSchema = z.object({
  username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6).max(50, '密码长度为6-50位'),
  fullName: z.string().min(1).max(100),
  roleId: z.string().min(1),
  department: z.string().max(100).optional(),
  phone: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确').optional(),
  isActive: z.boolean().default(true)
})

const updateUserSchema = createUserSchema.partial().omit({ password: true })

const changePasswordSchema = z.object({
  currentPassword: z.string().min(1),
  newPassword: z.string().min(6).max(50, '新密码长度为6-50位')
})

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6).max(50, '新密码长度为6-50位')
})

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  search: z.string().optional(),
  roleId: z.string().optional(),
  isActive: z.string().optional().transform(val => val ? val === 'true' : undefined),
  department: z.string().optional()
})

const importUsersSchema = z.array(createUserSchema)

// 获取用户列表
export const getUsers = async (req: Request, res: Response) => {
  try {
    const {
      page,
      limit,
      search,
      roleId,
      isActive,
      department
    } = querySchema.parse(req.query)

    const result = await UserService.getUsers(
      page,
      limit,
      search,
      roleId,
      isActive,
      department
    )

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get users error:', error)
    return res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    })
  }
}

// 获取用户详情
export const getUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const user = await UserService.getUserById(id)

    return res.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('Get user error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '获取用户详情失败'
    })
  }
}

// 创建用户
export const createUser = async (req: Request, res: Response) => {
  try {
    const data = createUserSchema.parse(req.body)
    const currentUser = (req as any).user

    const user = await UserService.createUser({
      ...data,
      department: data.department || null,
      phone: data.phone || null
    })

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'CREATE',
      user.id,
      {
        username: data.username,
        email: data.email,
        fullName: data.fullName,
        roleId: data.roleId
      },
      req.ip
    )

    return res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: user
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create user error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '用户创建失败'
    })
  }
}

// 更新用户
export const updateUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateUserSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const updateData = {
      username: data.username || null,
      email: data.email || null,
      fullName: data.fullName || null,
      roleId: data.roleId || null,
      department: data.department || null,
      phone: data.phone || null,
      isActive: data.isActive ?? null
    }
    
    const user = await UserService.updateUser(id, updateData)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'UPDATE',
      id,
      data,
      req.ip
    )

    return res.json({
      success: true,
      message: '用户更新成功',
      data: user
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update user error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '用户更新失败'
    })
  }
}

// 删除用户
export const deleteUser = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 不能删除自己
    if (id === currentUser.userId) {
      return res.status(400).json({
        success: false,
        message: '不能删除自己的账户'
      })
    }

    await UserService.deleteUser(id)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'DELETE',
      id,
      {},
      req.ip
    )

    return res.json({
      success: true,
      message: '用户删除成功'
    })
  } catch (error) {
    console.error('Delete user error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('关联数据')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '用户删除失败'
    })
  }
}

// 修改密码
export const changePassword = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = changePasswordSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 只能修改自己的密码，除非是管理员
    if (id !== currentUser.userId && !currentUser.permissions.includes('admin:all')) {
      return res.status(403).json({
        success: false,
        message: '只能修改自己的密码'
      })
    }

    await UserService.changePassword(id, data)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'UPDATE',
      id,
      { action: 'change_password' },
      req.ip
    )

    return res.json({
      success: true,
      message: '密码修改成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Change password error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不正确')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '密码修改失败'
    })
  }
}

// 重置密码（管理员功能）
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = resetPasswordSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    await UserService.resetPassword(id, data.newPassword)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'UPDATE',
      id,
      { action: 'reset_password' },
      req.ip
    )

    return res.json({
      success: true,
      message: '密码重置成功'
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Reset password error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '密码重置失败'
    })
  }
}

// 切换用户状态
export const toggleUserStatus = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 不能禁用自己
    if (id === currentUser.userId) {
      return res.status(400).json({
        success: false,
        message: '不能禁用自己的账户'
      })
    }

    const user = await UserService.toggleUserStatus(id)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'UPDATE',
      id,
      { action: 'toggle_status', newStatus: user.isActive },
      req.ip
    )

    return res.json({
      success: true,
      message: `用户已${user.isActive ? '启用' : '禁用'}`,
      data: user
    })
  } catch (error) {
    console.error('Toggle user status error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '切换用户状态失败'
    })
  }
}

// 获取用户统计信息
export const getUserStats = async (_req: Request, res: Response) => {
  try {
    const stats = await UserService.getUserStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get user stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取用户统计失败'
    })
  }
}

// 获取部门列表
export const getDepartments = async (_req: Request, res: Response) => {
  try {
    const departments = await UserService.getDepartments()

    return res.json({
      success: true,
      data: departments
    })
  } catch (error) {
    console.error('Get departments error:', error)
    return res.status(500).json({
      success: false,
      message: '获取部门列表失败'
    })
  }
}

// 批量导入用户
export const importUsers = async (req: Request, res: Response) => {
  try {
    const users = importUsersSchema.parse(req.body)
    const currentUser = (req as any).user

    if (users.length === 0) {
      return res.status(400).json({
        success: false,
        message: '用户列表不能为空'
      })
    }

    if (users.length > 100) {
      return res.status(400).json({
        success: false,
        message: '一次最多导入100个用户'
      })
    }

    const processedUsers = users.map(user => ({
      ...user,
      department: user.department || null,
      phone: user.phone || null
    }))
    
    const result = await UserService.importUsers(processedUsers)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'CREATE',
      'batch',
      {
        action: 'batch_import',
        totalUsers: users.length,
        successCount: result.success.length,
        failedCount: result.failed.length
      },
      req.ip
    )

    return res.json({
      success: true,
      message: `批量导入完成，成功: ${result.success.length}，失败: ${result.failed.length}`,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Import users error:', error)
    return res.status(500).json({
      success: false,
      message: '批量导入失败'
    })
  }
}

// 获取当前用户信息
export const getCurrentUser = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user

    if (!currentUser) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const user = await UserService.getUserById(currentUser.userId)

    return res.json({
      success: true,
      data: user
    })
  } catch (error) {
    console.error('Get current user error:', error)
    return res.status(500).json({
      success: false,
      message: '获取当前用户信息失败'
    })
  }
}

// 更新当前用户信息
export const updateCurrentUser = async (req: Request, res: Response) => {
  try {
    const currentUser = (req as any).user
    const data = updateUserSchema.omit({ roleId: true, isActive: true }).parse(req.body)

    if (!currentUser) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const updateData = {
      username: data.username || null,
      email: data.email || null,
      fullName: data.fullName || null,
      department: data.department || null,
      phone: data.phone || null
    }
    
    const user = await UserService.updateUser(currentUser.userId, updateData)

    // 记录操作日志
    await AuditService.logUserAction(
      currentUser.userId,
      'UPDATE',
      currentUser.userId,
      { action: 'update_profile', ...data },
      req.ip
    )

    return res.json({
      success: true,
      message: '个人信息更新成功',
      data: user
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update current user error:', error)
    return res.status(500).json({
      success: false,
      message: '个人信息更新失败'
    })
  }
}