import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'

// 验证Schema
const createSlaTemplateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  responseTime: z.number().min(1), // 响应时间(分钟)
  resolutionTime: z.number().min(1), // 解决时间(小时)
  availability: z.number().min(0).max(100).default(99.9) // 可用性百分比
})

const updateSlaTemplateSchema = createSlaTemplateSchema.partial()

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional()
})

// 获取SLA模板列表
export const getSlaTemplates = async (req: Request, res: Response) => {
  try {
    const { page, limit, search } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }

    const [slaTemplates, total] = await Promise.all([
      prisma.slaTemplate.findMany({
        where,
        skip,
        take: limit,
        include: {
          _count: {
            select: {
              services: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.slaTemplate.count({ where })
    ])

    const totalPages = Math.ceil(total / limit)

    return res.json({
      success: true,
      data: {
        items: slaTemplates,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get SLA templates error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA模板列表失败'
    })
  }
}

// 获取SLA模板详情
export const getSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id },
      include: {
        services: {
          select: {
            id: true,
            ticketNumber: true,
            title: true,
            status: true,
            priority: true,
            createdAt: true,
            archive: {
              select: {
                name: true,
                customer: {
                  select: {
                    name: true,
                    company: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10 // 只显示最近10个工单
        },
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    return res.json({
      success: true,
      data: slaTemplate
    })
  } catch (error) {
    console.error('Get SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA模板详情失败'
    })
  }
}

// 创建SLA模板
export const createSlaTemplate = async (req: Request, res: Response) => {
  try {
    const data = createSlaTemplateSchema.parse(req.body)

    // 检查名称是否已存在
    const existingSla = await prisma.slaTemplate.findFirst({
      where: { name: data.name }
    })

    if (existingSla) {
      return res.status(400).json({
        success: false,
        message: 'SLA模板名称已存在'
      })
    }

    const slaTemplate = await prisma.slaTemplate.create({
      data: {
        ...data,
        description: data.description || null
      },
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    return res.status(201).json({
      success: true,
      message: 'SLA模板创建成功',
      data: slaTemplate
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板创建失败'
    })
  }
}

// 更新SLA模板
export const updateSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateSlaTemplateSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const existingSla = await prisma.slaTemplate.findUnique({
      where: { id }
    })

    if (!existingSla) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 如果更新名称，检查是否与其他模板重复
    if (data.name && data.name !== existingSla.name) {
      const duplicateSla = await prisma.slaTemplate.findFirst({
        where: { 
          name: data.name,
          id: { not: id }
        }
      })

      if (duplicateSla) {
        return res.status(400).json({
          success: false,
          message: 'SLA模板名称已存在'
        })
      }
    }

    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.responseTime !== undefined) updateData.responseTime = data.responseTime
    if (data.resolutionTime !== undefined) updateData.resolutionTime = data.resolutionTime
    if (data.availability !== undefined) updateData.availability = data.availability
    if (data.description !== undefined) {
      updateData.description = data.description || null
    }

    const slaTemplate = await prisma.slaTemplate.update({
      where: { id },
      data: updateData,
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    return res.json({
      success: true,
      message: 'SLA模板更新成功',
      data: slaTemplate
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板更新失败'
    })
  }
}

// 删除SLA模板
export const deleteSlaTemplate = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id },
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 检查是否有关联的服务工单
    if (slaTemplate._count.services > 0) {
      return res.status(400).json({
        success: false,
        message: '该SLA模板已被服务工单使用，无法删除'
      })
    }

    await prisma.slaTemplate.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: 'SLA模板删除成功'
    })
  } catch (error) {
    console.error('Delete SLA template error:', error)
    return res.status(500).json({
      success: false,
      message: 'SLA模板删除失败'
    })
  }
}

// 获取SLA统计信息
export const getSlaStats = async (_req: Request, res: Response) => {
  try {
    // 获取所有SLA模板的统计信息
    const slaTemplates = await prisma.slaTemplate.findMany({
      include: {
        _count: {
          select: {
            services: true
          }
        }
      }
    })

    // 获取服务工单的SLA达成情况
    const services = await prisma.service.findMany({
      where: {
        slaTemplateId: { not: null },
        status: { in: ['RESOLVED', 'CLOSED'] }
      },
      include: {
        slaTemplate: true
      }
    })

    // 计算SLA达成率
    const slaPerformance = slaTemplates.map(template => {
      const templateServices = services.filter(s => s.slaTemplateId === template.id)

      let responseTimeCompliance = 0
      let resolutionTimeCompliance = 0

      if (templateServices.length > 0) {
        // 计算响应时间达成率
        const responseTimeCompliant = templateServices.filter(service => {
          if (!service.actualResponseTime) return false
          return service.actualResponseTime <= template.responseTime
        }).length

        responseTimeCompliance = (responseTimeCompliant / templateServices.length) * 100

        // 计算解决时间达成率
        const resolutionTimeCompliant = templateServices.filter(service => {
          if (!service.actualResolutionTime) return false
          return service.actualResolutionTime <= template.resolutionTime
        }).length

        resolutionTimeCompliance = (resolutionTimeCompliant / templateServices.length) * 100
      }

      return {
        templateId: template.id,
        templateName: template.name,
        totalServices: template._count.services,
        completedServices: templateServices.length,
        responseTimeCompliance: Math.round(responseTimeCompliance * 100) / 100,
        resolutionTimeCompliance: Math.round(resolutionTimeCompliance * 100) / 100,
        targetAvailability: template.availability
      }
    })

    // 总体统计
    const totalTemplates = slaTemplates.length
    const totalServices = slaTemplates.reduce((sum, template) => sum + template._count.services, 0)
    const avgResponseCompliance = slaPerformance.length > 0
      ? slaPerformance.reduce((sum, perf) => sum + perf.responseTimeCompliance, 0) / slaPerformance.length
      : 0
    const avgResolutionCompliance = slaPerformance.length > 0
      ? slaPerformance.reduce((sum, perf) => sum + perf.resolutionTimeCompliance, 0) / slaPerformance.length
      : 0

    return res.json({
      success: true,
      data: {
        overview: {
          totalTemplates,
          totalServices,
          avgResponseCompliance: Math.round(avgResponseCompliance * 100) / 100,
          avgResolutionCompliance: Math.round(avgResolutionCompliance * 100) / 100
        },
        templatePerformance: slaPerformance
      }
    })
  } catch (error) {
    console.error('Get SLA stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA统计信息失败'
    })
  }
}

// 获取SLA模板的详细性能报告
export const getSlaPerformanceReport = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const { startDate, endDate } = req.query

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查SLA模板是否存在
    const slaTemplate = await prisma.slaTemplate.findUnique({
      where: { id }
    })

    if (!slaTemplate) {
      return res.status(404).json({
        success: false,
        message: 'SLA模板不存在'
      })
    }

    // 构建时间范围查询条件
    const dateFilter: any = {}
    if (startDate) {
      dateFilter.gte = new Date(startDate as string)
    }
    if (endDate) {
      dateFilter.lte = new Date(endDate as string)
    }

    const whereCondition: any = {
      slaTemplateId: id
    }

    if (Object.keys(dateFilter).length > 0) {
      whereCondition.createdAt = dateFilter
    }

    // 获取该SLA模板下的所有服务工单
    const services = await prisma.service.findMany({
      where: whereCondition,
      include: {
        archive: {
          include: {
            customer: {
              select: {
                name: true,
                company: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // 计算性能指标
    const performanceMetrics = services.map(service => {
      let responseTimeMinutes: number | null = null
      let resolutionTimeHours: number | null = null
      let responseTimeCompliant = false
      let resolutionTimeCompliant = false

      // 计算响应时间
      if (service.actualResponseTime) {
        responseTimeMinutes = service.actualResponseTime
        responseTimeCompliant = responseTimeMinutes <= slaTemplate.responseTime
      }

      // 计算解决时间
      if (service.actualResolutionTime) {
        resolutionTimeHours = service.actualResolutionTime
        resolutionTimeCompliant = resolutionTimeHours <= slaTemplate.resolutionTime
      }

      return {
        serviceId: service.id,
        ticketNumber: service.ticketNumber,
        title: service.title,
        status: service.status,
        priority: service.priority,
        customerName: service.archive.customer.name,
        customerCompany: service.archive.customer.company,
        createdAt: service.createdAt,
        responseTime: responseTimeMinutes,
        resolutionTime: resolutionTimeHours,
        responseTimeCompliant,
        resolutionTimeCompliant,
        slaResponseTarget: slaTemplate.responseTime,
        slaResolutionTarget: slaTemplate.resolutionTime
      }
    })

    // 统计汇总
    const totalServices = services.length
    const responseTimeCompliantCount = performanceMetrics.filter(m => m.responseTimeCompliant).length
    const resolutionTimeCompliantCount = performanceMetrics.filter(m => m.resolutionTimeCompliant).length

    const responseTimeCompliance = totalServices > 0 ? (responseTimeCompliantCount / totalServices) * 100 : 0
    const resolutionTimeCompliance = totalServices > 0 ? (resolutionTimeCompliantCount / totalServices) * 100 : 0

    return res.json({
      success: true,
      data: {
        slaTemplate: {
          id: slaTemplate.id,
          name: slaTemplate.name,
          responseTime: slaTemplate.responseTime,
          resolutionTime: slaTemplate.resolutionTime,
          availability: slaTemplate.availability
        },
        summary: {
          totalServices,
          responseTimeCompliance: Math.round(responseTimeCompliance * 100) / 100,
          resolutionTimeCompliance: Math.round(resolutionTimeCompliance * 100) / 100,
          responseTimeCompliantCount,
          resolutionTimeCompliantCount
        },
        services: performanceMetrics
      }
    })
  } catch (error) {
    console.error('Get SLA performance report error:', error)
    return res.status(500).json({
      success: false,
      message: '获取SLA性能报告失败'
    })
  }
}
