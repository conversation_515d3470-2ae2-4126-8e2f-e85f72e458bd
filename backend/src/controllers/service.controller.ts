import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'

// 验证Schema
const createServiceSchema = z.object({
  archiveId: z.string().min(1),
  slaTemplateId: z.string().optional(),
  title: z.string().min(1).max(200),
  description: z.string().min(1),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).default('SUPPORT'),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).default('MEDIUM'),
  customerContact: z.string().max(100).optional(),
  estimatedHours: z.number().min(0).optional()
})

const updateServiceSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  description: z.string().min(1).optional(),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED']).optional(),
  assignedTo: z.string().optional(),
  customerContact: z.string().max(100).optional(),
  estimatedHours: z.number().min(0).optional(),
  actualHours: z.number().min(0).optional(),
  resolution: z.string().optional(),
  customerFeedback: z.string().optional(),
  satisfaction: z.number().min(1).max(5).optional()
})

const createWorkLogSchema = z.object({
  description: z.string().min(1),
  workHours: z.number().min(0),
  workDate: z.string().transform(val => new Date(val)),
  category: z.enum(['ANALYSIS', 'IMPLEMENTATION', 'TESTING', 'DOCUMENTATION', 'COMMUNICATION', 'MAINTENANCE', 'SUPPORT', 'OTHER']).default('MAINTENANCE')
})

const createCommentSchema = z.object({
  content: z.string().min(1),
  isInternal: z.boolean().default(false)
})

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'WAITING_CUSTOMER', 'RESOLVED', 'CLOSED']).optional(),
  category: z.enum(['MAINTENANCE', 'SUPPORT', 'UPGRADE', 'BUGFIX', 'CONSULTING', 'MONITORING']).optional(),
  priority: z.enum(['LOW', 'MEDIUM', 'HIGH', 'URGENT']).optional(),
  assignedTo: z.string().optional(),
  customerId: z.string().optional(),
  archiveId: z.string().optional()
})

// 生成工单号
function generateTicketNumber(): string {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  return `OPS-${year}${month}${day}-${random}`
}

// 获取服务工单列表
export const getServices = async (req: Request, res: Response) => {
  try {
    const { page, limit, search, status, category, priority, assignedTo, customerId, archiveId } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { ticketNumber: { contains: search } },
        { title: { contains: search } },
        { description: { contains: search } },
        { customerContact: { contains: search } }
      ]
    }
    
    if (status) {
      where.status = status
    }
    
    if (category) {
      where.category = category
    }
    
    if (priority) {
      where.priority = priority
    }
    
    if (assignedTo) {
      where.assignedTo = assignedTo
    }
    
    if (archiveId) {
      where.archiveId = archiveId
    }
    
    if (customerId) {
      where.archive = {
        customerId: customerId
      }
    }

    const [services, total] = await Promise.all([
      prisma.service.findMany({
        where,
        skip,
        take: limit,
        include: {
          archive: {
            select: {
              id: true,
              name: true,
              customer: {
                select: {
                  id: true,
                  name: true,
                  company: true,
                  level: true
                }
              }
            }
          },
          assignedUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          createdByUser: {
            select: {
              id: true,
              username: true,
              fullName: true
            }
          },
          slaTemplate: {
            select: {
              id: true,
              name: true,
              responseTime: true,
              resolutionTime: true
            }
          },
          _count: {
            select: {
              workLogs: true,
              attachments: true,
              comments: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ]
      }),
      prisma.service.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        services,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get services error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单列表失败'
    })
  }
}

// 获取服务工单详情
export const getService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const service = await prisma.service.findFirst({
      where: { id },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true,
                contactPerson: true,
                email: true,
                phone: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true,
            email: true
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true,
        workLogs: {
          include: {
            user: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { workDate: 'desc' }
        },
        attachments: {
          include: {
            uploader: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { uploadedAt: 'desc' }
        },
        comments: {
          include: {
            author: {
              select: {
                id: true,
                username: true,
                fullName: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    return res.json({
      success: true,
      data: service
    })
  } catch (error) {
    console.error('Get service error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单详情失败'
    })
  }
}

// 创建服务工单
export const createService = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createServiceSchema.parse(req.body)

    // 检查项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id: data.archiveId },
      include: {
        customer: true
      }
    })

    if (!archive) {
      return res.status(400).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    // 检查SLA模板是否存在
    if (data.slaTemplateId) {
      const slaTemplate = await prisma.slaTemplate.findUnique({
        where: { id: data.slaTemplateId }
      })

      if (!slaTemplate) {
        return res.status(400).json({
          success: false,
          message: 'SLA模板不存在'
        })
      }
    }

    // 生成唯一工单号
    let ticketNumber: string
    let isUnique = false
    let attempts = 0
    
    do {
      ticketNumber = generateTicketNumber()
      const existing = await prisma.service.findUnique({
        where: { ticketNumber }
      })
      isUnique = !existing
      attempts++
    } while (!isUnique && attempts < 10)

    if (!isUnique) {
      return res.status(500).json({
        success: false,
        message: '生成工单号失败，请重试'
      })
    }

    const service = await prisma.service.create({
      data: {
        archiveId: data.archiveId,
        title: data.title,
        description: data.description,
        category: data.category,
        priority: data.priority,
        customerContact: data.customerContact || null,
        slaTemplateId: data.slaTemplateId || null,
        ticketNumber,
        createdBy: userId,
        startTime: new Date()
      },
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true
              }
            }
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true
      }
    })

    return res.status(201).json({
      success: true,
      message: '服务工单创建成功',
      data: service
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单创建失败'
    })
  }
}

// 更新服务工单
export const updateService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateServiceSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查服务工单是否存在
    const existingService = await prisma.service.findFirst({
      where: { id }
    })

    if (!existingService) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 如果分配给用户，检查用户是否存在
    if (data.assignedTo) {
      const user = await prisma.user.findUnique({
        where: { id: data.assignedTo }
      })

      if (!user) {
        return res.status(400).json({
          success: false,
          message: '分配的用户不存在'
        })
      }
    }

    // 如果状态改为已解决或已关闭，设置结束时间
    const updateData: any = { ...data }
    if ((data.status === 'RESOLVED' || data.status === 'CLOSED') && !existingService.endTime) {
      updateData.endTime = new Date()
    }

    const service = await prisma.service.update({
      where: { id },
      data: updateData,
      include: {
        archive: {
          include: {
            customer: {
              select: {
                id: true,
                name: true,
                company: true,
                level: true
              }
            }
          }
        },
        assignedUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        createdByUser: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        },
        slaTemplate: true
      }
    })

    return res.json({
      success: true,
      message: '服务工单更新成功',
      data: service
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单更新失败'
    })
  }
}

// 删除服务工单
export const deleteService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id },
      include: {
        workLogs: true,
        attachments: true,
        comments: true
      }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    // 只有待处理状态的工单才能删除
    if (service.status !== 'PENDING') {
      return res.status(400).json({
        success: false,
        message: '只有待处理状态的工单才能删除'
      })
    }

    // 删除相关数据
    await prisma.$transaction([
      prisma.serviceComment.deleteMany({ where: { serviceId: id } }),
      prisma.serviceAttachment.deleteMany({ where: { serviceId: id } }),
      prisma.serviceWorkLog.deleteMany({ where: { serviceId: id } }),
      prisma.service.delete({ where: { id } })
    ])

    return res.json({
      success: true,
      message: '服务工单删除成功'
    })
  } catch (error) {
    console.error('Delete service error:', error)
    return res.status(500).json({
      success: false,
      message: '服务工单删除失败'
    })
  }
}

// 获取服务工单工作日志
export const getServiceWorkLogs = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const workLogs = await prisma.serviceWorkLog.findMany({
      where: { serviceId },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { workDate: 'desc' }
    })

    return res.json({
      success: true,
      data: workLogs
    })
  } catch (error) {
    console.error('Get service work logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取工作日志失败'
    })
  }
}

// 创建服务工单工作日志
export const createServiceWorkLog = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const userId = (req as any).user?.userId

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createWorkLogSchema.parse(req.body)

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const workLog = await prisma.serviceWorkLog.create({
      data: {
        description: data.description,
        workHours: data.workHours,
        workDate: data.workDate,
        category: data.category,
        serviceId,
        userId
      },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    // 更新服务工单的实际工时
    const totalHours = await prisma.serviceWorkLog.aggregate({
      where: { serviceId },
      _sum: {
        workHours: true
      }
    })

    await prisma.service.update({
      where: { id: serviceId },
      data: {
        actualHours: totalHours._sum?.workHours || 0
      }
    })

    return res.status(201).json({
      success: true,
      message: '工作日志创建成功',
      data: workLog
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service work log error:', error)
    return res.status(500).json({
      success: false,
      message: '工作日志创建失败'
    })
  }
}

// 获取服务工单评论
export const getServiceComments = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const { isInternal } = req.query

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const where: any = { serviceId }
    
    if (isInternal !== undefined) {
      where.isInternal = isInternal === 'true'
    }

    const comments = await prisma.serviceComment.findMany({
      where,
      include: {
        author: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return res.json({
      success: true,
      data: comments
    })
  } catch (error) {
    console.error('Get service comments error:', error)
    return res.status(500).json({
      success: false,
      message: '获取评论失败'
    })
  }
}

// 创建服务工单评论
export const createServiceComment = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params
    const userId = (req as any).user?.userId

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createCommentSchema.parse(req.body)

    // 检查服务工单是否存在
    const service = await prisma.service.findFirst({
      where: { id: serviceId }
    })

    if (!service) {
      return res.status(404).json({
        success: false,
        message: '服务工单不存在'
      })
    }

    const comment = await prisma.serviceComment.create({
      data: {
        content: data.content,
        isInternal: data.isInternal,
        serviceId,
        authorId: userId
      },
      include: {
        author: {
          select: {
            id: true,
            username: true,
            fullName: true
          }
        }
      }
    })

    return res.status(201).json({
      success: true,
      message: '评论创建成功',
      data: comment
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create service comment error:', error)
    return res.status(500).json({
      success: false,
      message: '评论创建失败'
    })
  }
}

// 获取服务工单统计信息
export const getServiceStats = async (_req: Request, res: Response) => {
  try {
    const [
      totalServices,
      statusStats,
      categoryStats,
      priorityStats,
      recentServices,
      avgResolutionTime
    ] = await Promise.all([
      // 总工单数
      prisma.service.count(),
      
      // 按状态统计
      prisma.service.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      }),
      
      // 按类别统计
      prisma.service.groupBy({
        by: ['category'],
        _count: {
          category: true
        }
      }),
      
      // 按优先级统计
      prisma.service.groupBy({
        by: ['priority'],
        _count: {
          priority: true
        }
      }),
      
      // 最近7天新增工单
      prisma.service.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 平均解决时间
      prisma.service.aggregate({
        where: {
          status: 'CLOSED',
          startTime: { not: null },
          endTime: { not: null }
        },
        _avg: {
          actualResolutionTime: true
        }
      })
    ])

    return res.json({
      success: true,
      data: {
        totalServices,
        recentServices,
        avgResolutionTime: avgResolutionTime._avg.actualResolutionTime || 0,
        statusDistribution: statusStats.map(stat => ({
          status: stat.status,
          count: stat._count.status
        })),
        categoryDistribution: categoryStats.map(stat => ({
          category: stat.category,
          count: stat._count.category
        })),
        priorityDistribution: priorityStats.map(stat => ({
          priority: stat.priority,
          count: stat._count.priority
        }))
      }
    })
  } catch (error) {
    console.error('Get service stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取服务工单统计信息失败'
    })
  }
}