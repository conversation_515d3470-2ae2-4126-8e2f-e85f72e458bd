import { Request, Response } from 'express'
import { UploadService } from '@/services/upload.service'
import { AuditService } from '@/services/audit.service'

interface AuthenticatedRequest extends Request {
  user?: {
    userId: string
    username: string
  }
}

// 上传服务工单附件
export const uploadServiceAttachments = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { serviceId } = req.params
    const userId = req.user?.userId

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    const files = req.files as Express.Multer.File[]
    
    if (!files || files.length === 0) {
      return res.status(400).json({
        success: false,
        message: '未选择文件'
      })
    }

    // 处理文件上传
    const attachments = await UploadService.handleServiceAttachment(
      serviceId,
      files,
      userId
    )

    // 记录操作日志
    await AuditService.log({
      userId,
      action: 'CREATE',
      resource: 'ATTACHMENT',
      resourceId: serviceId,
      details: {
        fileCount: files.length,
        filenames: files.map(f => f.originalname),
        totalSize: files.reduce((sum, f) => sum + f.size, 0)
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '文件上传成功',
      data: {
        attachments: attachments.map(att => ({
          id: att.id,
          filename: att.filename,
          originalName: att.originalName,
          fileSize: att.fileSize,
          mimeType: att.mimeType,
          uploadedAt: att.uploadedAt,
          formattedSize: UploadService.formatFileSize(att.fileSize)
        }))
      }
    })
  } catch (error) {
    console.error('Upload service attachments error:', error)
    
    // 如果是文件类型错误，返回具体错误信息
    if (error instanceof Error && error.message.includes('不支持的文件类型')) {
      return res.status(400).json({
        success: false,
        message: error.message
      })
    }

    return res.status(500).json({
      success: false,
      message: '文件上传失败'
    })
  }
}

// 获取服务工单附件列表
export const getServiceAttachments = async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params

    if (!serviceId) {
      return res.status(400).json({
        success: false,
        message: 'serviceId参数缺失'
      })
    }

    const attachments = await UploadService.getServiceAttachments(serviceId)

    return res.json({
      success: true,
      data: {
        attachments: attachments.map(att => ({
          id: att.id,
          filename: att.filename,
          originalName: att.originalName,
          fileSize: att.fileSize,
          mimeType: att.mimeType,
          uploadedAt: att.uploadedAt,
          formattedSize: UploadService.formatFileSize(att.fileSize),
          uploader: att.uploader
        }))
      }
    })
  } catch (error) {
    console.error('Get service attachments error:', error)
    return res.status(500).json({
      success: false,
      message: '获取附件列表失败'
    })
  }
}

// 下载附件
export const downloadAttachment = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { attachmentId } = req.params
    const userId = req.user?.userId

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    if (!attachmentId) {
      return res.status(400).json({
        success: false,
        message: 'attachmentId参数缺失'
      })
    }

    const attachment = await UploadService.getAttachment(attachmentId)
    const fileData = await UploadService.getAttachmentFile(attachmentId)

    // 记录下载日志
    await AuditService.log({
      userId,
      action: 'VIEW',
      resource: 'ATTACHMENT',
      resourceId: attachmentId,
      details: {
        filename: attachment.originalName,
        serviceId: attachment.serviceId
      },
      ipAddress: req.ip
    })

    // 设置响应头
    res.setHeader('Content-Type', fileData.mimetype)
    res.setHeader('Content-Length', fileData.size)
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileData.filename)}"`)

    return res.send(fileData.buffer)
  } catch (error) {
    console.error('Download attachment error:', error)
    
    if (error instanceof Error) {
      if (error.message === '附件不存在') {
        return res.status(404).json({
          success: false,
          message: '附件不存在'
        })
      }
      
      if (error.message === '文件读取失败') {
        return res.status(500).json({
          success: false,
          message: '文件读取失败'
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '文件下载失败'
    })
  }
}

// 删除附件
export const deleteAttachment = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { attachmentId } = req.params
    const userId = req.user?.userId

    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    if (!attachmentId) {
      return res.status(400).json({
        success: false,
        message: 'attachmentId参数缺失'
      })
    }

    const attachment = await UploadService.deleteAttachment(attachmentId, userId)

    // 记录操作日志
    await AuditService.log({
      userId,
      action: 'DELETE',
      resource: 'ATTACHMENT',
      resourceId: attachmentId,
      details: {
        filename: attachment.originalName,
        serviceId: attachment.serviceId
      },
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '附件删除成功'
    })
  } catch (error) {
    console.error('Delete attachment error:', error)
    
    if (error instanceof Error) {
      if (error.message === '附件不存在') {
        return res.status(404).json({
          success: false,
          message: '附件不存在'
        })
      }
      
      if (error.message === '无权限删除此附件') {
        return res.status(403).json({
          success: false,
          message: '无权限删除此附件'
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '附件删除失败'
    })
  }
}

// 获取存储统计信息（管理员功能）
export const getStorageStats = async (_req: Request, res: Response) => {
  try {
    const stats = await UploadService.getStorageStats()

    return res.json({
      success: true,
      data: {
        ...stats,
        formattedTotalSize: UploadService.formatFileSize(stats.totalSize),
        typeBreakdown: stats.typeBreakdown.map(item => ({
          ...item,
          formattedSize: UploadService.formatFileSize(item.totalSize)
        }))
      }
    })
  } catch (error) {
    console.error('Get storage stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取存储统计失败'
    })
  }
}

// 清理过期文件（管理员功能）
export const cleanupOldFiles = async (req: Request, res: Response) => {
  try {
    const { days = '30' } = req.query
    const daysOld = parseInt(days as string) || 30

    if (daysOld < 7) {
      return res.status(400).json({
        success: false,
        message: '清理天数不能少于7天'
      })
    }

    const result = await UploadService.cleanupOldFiles(daysOld)

    return res.json({
      success: true,
      message: '文件清理完成',
      data: result
    })
  } catch (error) {
    console.error('Cleanup old files error:', error)
    return res.status(500).json({
      success: false,
      message: '文件清理失败'
    })
  }
}