import { Request, Response } from 'express'
import { z } from 'zod'
import { RoleService, ROLE_TEMPLATES } from '@/services/role.service'
import { AuditService } from '@/services/audit.service'

// 验证Schema
const createRoleSchema = z.object({
  name: z.string().min(1).max(50).regex(/^[a-zA-Z0-9_-]+$/, '角色名只能包含字母、数字、下划线和横线'),
  description: z.string().min(1).max(200),
  permissions: z.array(z.string()).min(1, '至少需要一个权限')
})

const updateRoleSchema = createRoleSchema.partial()

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 20),
  search: z.string().optional()
})

const createFromTemplateSchema = z.object({
  templateName: z.enum(Object.keys(ROLE_TEMPLATES) as [keyof typeof ROLE_TEMPLATES]),
  customName: z.string().optional(),
  customDescription: z.string().optional()
})

const duplicateRoleSchema = z.object({
  newName: z.string().min(1).max(50).regex(/^[a-zA-Z0-9_-]+$/, '角色名只能包含字母、数字、下划线和横线'),
  newDescription: z.string().optional()
})

// 获取角色列表
export const getRoles = async (req: Request, res: Response) => {
  try {
    const { page, limit, search } = querySchema.parse(req.query)

    const result = await RoleService.getRoles(page, limit, search)

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get roles error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色列表失败'
    })
  }
}

// 获取所有角色（用于下拉选择）
export const getAllRoles = async (_req: Request, res: Response) => {
  try {
    const roles = await RoleService.getAllRoles()

    return res.json({
      success: true,
      data: roles
    })
  } catch (error) {
    console.error('Get all roles error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色列表失败'
    })
  }
}

// 获取角色详情
export const getRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.getRoleById(id)

    return res.json({
      success: true,
      data: role
    })
  } catch (error) {
    console.error('Get role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '获取角色详情失败'
    })
  }
}

// 创建角色
export const createRole = async (req: Request, res: Response) => {
  try {
    const data = createRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    const role = await RoleService.createRole(data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        roleName: data.name,
        permissions: data.permissions
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('无效的权限')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色创建失败'
    })
  }
}

// 更新角色
export const updateRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.updateRole(id, data)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'UPDATE',
      resource: 'ROLE',
      resourceId: id,
      details: data,
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '角色更新成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('无效的权限')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色更新失败'
    })
  }
}

// 删除角色
export const deleteRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    await RoleService.deleteRole(id)

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'DELETE',
      resource: 'ROLE',
      resourceId: id,
      details: {},
      ipAddress: req.ip
    })

    return res.json({
      success: true,
      message: '角色删除成功'
    })
  } catch (error) {
    console.error('Delete role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('正被') && error.message.includes('使用')) {
        return res.status(400).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色删除失败'
    })
  }
}

// 获取系统权限列表
export const getSystemPermissions = async (_req: Request, res: Response) => {
  try {
    const permissions = RoleService.getSystemPermissions()

    return res.json({
      success: true,
      data: permissions
    })
  } catch (error) {
    console.error('Get system permissions error:', error)
    return res.status(500).json({
      success: false,
      message: '获取系统权限失败'
    })
  }
}

// 获取权限分组
export const getPermissionGroups = async (_req: Request, res: Response) => {
  try {
    const groups = RoleService.getPermissionGroups()

    return res.json({
      success: true,
      data: groups
    })
  } catch (error) {
    console.error('Get permission groups error:', error)
    return res.status(500).json({
      success: false,
      message: '获取权限分组失败'
    })
  }
}

// 获取角色模板
export const getRoleTemplates = async (_req: Request, res: Response) => {
  try {
    const templates = RoleService.getRoleTemplates()

    return res.json({
      success: true,
      data: templates
    })
  } catch (error) {
    console.error('Get role templates error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色模板失败'
    })
  }
}

// 从模板创建角色
export const createRoleFromTemplate = async (req: Request, res: Response) => {
  try {
    const data = createFromTemplateSchema.parse(req.body)
    const currentUser = (req as any).user

    const role = await RoleService.createRoleFromTemplate(
      data.templateName,
      data.customName,
      data.customDescription
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        action: 'create_from_template',
        templateName: data.templateName,
        roleName: role.name
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create role from template error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '从模板创建角色失败'
    })
  }
}

// 复制角色
export const duplicateRole = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = duplicateRoleSchema.parse(req.body)
    const currentUser = (req as any).user

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const role = await RoleService.duplicateRole(
      id,
      data.newName,
      data.newDescription
    )

    // 记录操作日志
    await AuditService.log({
      userId: currentUser.userId,
      action: 'CREATE',
      resource: 'ROLE',
      resourceId: role.id,
      details: {
        action: 'duplicate_role',
        sourceRoleId: id,
        newRoleName: data.newName
      },
      ipAddress: req.ip
    })

    return res.status(201).json({
      success: true,
      message: '角色复制成功',
      data: role
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Duplicate role error:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('不存在')) {
        return res.status(404).json({
          success: false,
          message: error.message
        })
      }
      if (error.message.includes('已存在')) {
        return res.status(409).json({
          success: false,
          message: error.message
        })
      }
    }

    return res.status(500).json({
      success: false,
      message: '角色复制失败'
    })
  }
}

// 获取角色统计信息
export const getRoleStats = async (_req: Request, res: Response) => {
  try {
    const stats = await RoleService.getRoleStats()

    return res.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('Get role stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取角色统计失败'
    })
  }
}