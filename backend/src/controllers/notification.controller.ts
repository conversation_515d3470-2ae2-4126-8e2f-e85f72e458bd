import { Request, Response } from 'express'
import { z } from 'zod'
import { emailService } from '@/services/email.service'
import { smsService } from '@/services/sms.service'

// 验证Schema
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional()
})

const sendTemplateEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  templateName: z.string().min(1),
  variables: z.record(z.any()),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional()
})

const sendSmsSchema = z.object({
  phoneNumber: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  message: z.string().min(1).max(500)
})

const sendTemplateSmsSchema = z.object({
  phoneNumber: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  templateName: z.string().min(1),
  variables: z.record(z.string())
})

const sendBulkSmsSchema = z.object({
  phoneNumbers: z.array(z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确')),
  message: z.string().min(1).max(500)
})

// 发送邮件
export const sendEmail = async (req: Request, res: Response) => {
  try {
    const data = sendEmailSchema.parse(req.body)

    if (!emailService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '邮件服务未配置'
      })
    }

    const emailData = {
      to: data.to,
      subject: data.subject,
      html: data.html || '',
      text: data.text || '',
      ...(data.cc && { cc: data.cc }),
      ...(data.bcc && { bcc: data.bcc })
    }
    
    const result = await emailService.sendEmail(emailData)

    if (result) {
      return res.json({
        success: true,
        message: '邮件发送成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        message: '邮件发送失败'
      })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Send email error:', error)
    return res.status(500).json({
      success: false,
      message: '邮件发送失败'
    })
  }
}

// 发送模板邮件
export const sendTemplateEmail = async (req: Request, res: Response) => {
  try {
    const data = sendTemplateEmailSchema.parse(req.body)

    if (!emailService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '邮件服务未配置'
      })
    }

    const options: any = {}
    if (data.cc) options.cc = data.cc
    if (data.bcc) options.bcc = data.bcc
    
    const result = await emailService.sendTemplateEmail(
      data.to,
      data.templateName,
      data.variables,
      options
    )

    if (result) {
      return res.json({
        success: true,
        message: '模板邮件发送成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        message: '模板邮件发送失败'
      })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Send template email error:', error)
    return res.status(500).json({
      success: false,
      message: '模板邮件发送失败'
    })
  }
}

// 发送短信
export const sendSms = async (req: Request, res: Response) => {
  try {
    const data = sendSmsSchema.parse(req.body)

    if (!smsService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '短信服务未配置'
      })
    }

    const result = await smsService.sendSms(data)

    if (result) {
      return res.json({
        success: true,
        message: '短信发送成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        message: '短信发送失败'
      })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Send SMS error:', error)
    return res.status(500).json({
      success: false,
      message: '短信发送失败'
    })
  }
}

// 发送模板短信
export const sendTemplateSms = async (req: Request, res: Response) => {
  try {
    const data = sendTemplateSmsSchema.parse(req.body)

    if (!smsService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '短信服务未配置'
      })
    }

    const result = await smsService.sendTemplateSms(
      data.phoneNumber,
      data.templateName,
      data.variables
    )

    if (result) {
      return res.json({
        success: true,
        message: '模板短信发送成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        message: '模板短信发送失败'
      })
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Send template SMS error:', error)
    return res.status(500).json({
      success: false,
      message: '模板短信发送失败'
    })
  }
}

// 批量发送短信
export const sendBulkSms = async (req: Request, res: Response) => {
  try {
    const data = sendBulkSmsSchema.parse(req.body)

    if (!smsService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '短信服务未配置'
      })
    }

    const result = await smsService.sendBulkSms(
      data.phoneNumbers,
      data.message
    )

    return res.json({
      success: true,
      message: `批量短信发送完成，成功：${result.success}条，失败：${result.failed}条`,
      data: {
        success: result.success,
        failed: result.failed,
        total: data.phoneNumbers.length
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Send bulk SMS error:', error)
    return res.status(500).json({
      success: false,
      message: '批量短信发送失败'
    })
  }
}

// 测试邮件连接
export const testEmailConnection = async (_req: Request, res: Response) => {
  try {
    if (!emailService.isConfigured()) {
      return res.status(503).json({
        success: false,
        message: '邮件服务未配置'
      })
    }

    const result = await emailService.testConnection()

    if (result) {
      return res.json({
        success: true,
        message: '邮件连接测试成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        message: '邮件连接测试失败'
      })
    }
  } catch (error) {
    console.error('Test email connection error:', error)
    return res.status(500).json({
      success: false,
      message: '邮件连接测试失败'
    })
  }
}

// 获取通知服务状态
export const getNotificationStatus = async (_req: Request, res: Response) => {
  try {
    const emailConfigured = emailService.isConfigured()
    const smsConfigured = smsService.isConfigured()

    return res.json({
      success: true,
      data: {
        email: {
          configured: emailConfigured,
          status: emailConfigured ? 'active' : 'inactive'
        },
        sms: {
          configured: smsConfigured,
          status: smsConfigured ? 'active' : 'inactive',
          ...smsService.getConfigInfo()
        }
      }
    })
  } catch (error) {
    console.error('Get notification status error:', error)
    return res.status(500).json({
      success: false,
      message: '获取通知服务状态失败'
    })
  }
}
