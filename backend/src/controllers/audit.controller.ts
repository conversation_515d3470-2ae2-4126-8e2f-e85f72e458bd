import { Request, Response } from 'express'
import { z } from 'zod'
import { AuditService } from '@/services/audit.service'

// 验证Schema
const getLogsSchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  userId: z.string().optional(),
  resource: z.enum(['AUTH', 'CUSTOMER', 'ARCHIVE', 'SERVICE', 'CONFIGURATION', 'SLA']).optional(),
  action: z.enum(['CREATE', 'UPDATE', 'DELETE', 'VIEW', 'LOGIN', 'LOGOUT', 'STATUS_CHANGE']).optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

const getStatsSchema = z.object({
  userId: z.string().optional(),
  startDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  endDate: z.string().optional().transform(val => val ? new Date(val) : undefined)
})

// 获取操作日志列表
export const getAuditLogs = async (req: Request, res: Response) => {
  try {
    const {
      page,
      limit,
      userId,
      resource,
      action,
      startDate,
      endDate
    } = getLogsSchema.parse(req.query)

    const result = await AuditService.getLogs(
      page,
      limit,
      userId,
      resource,
      action,
      startDate,
      endDate
    )

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取操作日志失败'
    })
  }
}

// 获取用户操作统计
export const getAuditStats = async (req: Request, res: Response) => {
  try {
    const { userId, startDate, endDate } = getStatsSchema.parse(req.query)

    const stats = await AuditService.getUserActionStats(userId, startDate, endDate)

    // 按资源类型分组统计
    const resourceStats: Record<string, Record<string, number>> = {}
    let totalOperations = 0

    stats.forEach(stat => {
      if (!resourceStats[stat.resource]) {
        resourceStats[stat.resource] = {}
      }
      resourceStats[stat.resource][stat.action] = stat.count
      totalOperations += stat.count
    })

    // 计算最近活跃用户（如果没有指定userId）
    let activeUsers: any[] = []
    if (!userId) {
      // 获取最近7天最活跃的用户
      const recentDate = new Date()
      recentDate.setDate(recentDate.getDate() - 7)

      const userStats = await AuditService.getLogs(1, 100, undefined, undefined, undefined, recentDate)
      const userActivity: Record<string, number> = {}

      userStats.logs.forEach(log => {
        const userId = log.userId
        userActivity[userId] = (userActivity[userId] || 0) + 1
      })

      activeUsers = Object.entries(userActivity)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([userId, count]) => {
          const userLog = userStats.logs.find(log => log.userId === userId)
          return {
            userId,
            username: userLog?.user?.username,
            fullName: userLog?.user?.fullName,
            operationCount: count
          }
        })
    }

    return res.json({
      success: true,
      data: {
        totalOperations,
        resourceStats,
        actionBreakdown: stats,
        activeUsers: userId ? undefined : activeUsers,
        timeRange: {
          startDate,
          endDate
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get audit stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取操作统计失败'
    })
  }
}

// 获取当前用户的操作日志
export const getMyAuditLogs = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const { page = 1, limit = 20 } = req.query
    const pageNum = parseInt(page as string) || 1
    const limitNum = parseInt(limit as string) || 20

    const result = await AuditService.getLogs(
      pageNum,
      limitNum,
      userId
    )

    return res.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('Get my audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '获取个人操作日志失败'
    })
  }
}

// 导出操作日志（管理员功能）
export const exportAuditLogs = async (req: Request, res: Response) => {
  try {
    const {
      userId,
      resource,
      action,
      startDate,
      endDate
    } = getLogsSchema.parse(req.query)

    // 获取所有符合条件的日志（不分页）
    const result = await AuditService.getLogs(
      1,
      10000, // 最多导出10000条记录
      userId,
      resource,
      action,
      startDate,
      endDate
    )

    // 转换为CSV格式
    const csvHeaders = ['时间', '用户', '操作', '资源', '资源ID', 'IP地址', '详情']
    const csvRows = result.logs.map(log => [
      log.timestamp.toISOString(),
      log.user.username || log.user.fullName || log.userId,
      log.action,
      log.resource,
      log.resourceId || '',
      log.ipAddress || '',
      log.details ? JSON.stringify(log.details) : ''
    ])

    const csvContent = [csvHeaders, ...csvRows]
      .map(row => row.map(field => `"${field.toString().replace(/"/g, '""')}"`).join(','))
      .join('\n')

    const filename = `audit_logs_${new Date().toISOString().split('T')[0]}.csv`

    res.setHeader('Content-Type', 'text/csv; charset=utf-8')
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'))

    return res.send('\uFEFF' + csvContent) // 添加BOM以支持Excel中的中文
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Export audit logs error:', error)
    return res.status(500).json({
      success: false,
      message: '导出操作日志失败'
    })
  }
}