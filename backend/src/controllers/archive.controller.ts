import { Request, Response } from 'express'
import { prisma } from '@/config/database.config'
import { z } from 'zod'

// 验证Schema
const createArchiveSchema = z.object({
  customerId: z.string().min(1),
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  technology: z.string().max(200).optional(),
  environment: z.string().max(200).optional(),
  version: z.string().max(50).optional(),
  deploymentDate: z.string().optional().transform(val => val ? new Date(val) : undefined),
  status: z.enum(['ACTIVE', 'MAINTENANCE', 'DEPRECATED', 'ARCHIVED']).default('ACTIVE')
})

const updateArchiveSchema = createArchiveSchema.partial().omit({ customerId: true })

const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  status: z.enum(['ACTIVE', 'MAINTENANCE', 'DEPRECATED', 'ARCHIVED']).optional(),
  customerId: z.string().optional(),
  technology: z.string().optional()
})

// 获取项目档案列表
export const getArchives = async (req: Request, res: Response) => {
  try {
    const { page, limit, search, status, customerId, technology } = querySchema.parse(req.query)
    
    const skip = (page - 1) * limit
    
    // 构建查询条件
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { technology: { contains: search } },
        { environment: { contains: search } }
      ]
    }
    
    if (status) {
      where.status = status
    }
    
    if (customerId) {
      where.customerId = customerId
    }
    
    if (technology) {
      where.technology = { contains: technology }
    }

    const [archives, total] = await Promise.all([
      prisma.projectArchive.findMany({
        where,
        skip,
        take: limit,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              company: true,
              level: true
            }
          },
          createdByUser: {
            select: {
              username: true,
              fullName: true
            }
          },
          configurations: {
            select: {
              id: true,
              configType: true,
              title: true,
              isActive: true
            }
          },
          services: {
            select: {
              id: true,
              ticketNumber: true,
              title: true,
              status: true,
              priority: true
            },
            orderBy: { createdAt: 'desc' },
            take: 3
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.projectArchive.count({ where })
    ])

    return res.json({
      success: true,
      data: {
        archives,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Get archives error:', error)
    return res.status(500).json({
      success: false,
      message: '获取项目档案列表失败'
    })
  }
}

// 获取项目档案详情
export const getArchive = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    const archive = await prisma.projectArchive.findUnique({
      where: { id },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
            level: true,
            contactPerson: true,
            email: true,
            phone: true
          }
        },
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        configurations: {
          orderBy: { createdAt: 'desc' }
        },
        services: {
          select: {
            id: true,
            ticketNumber: true,
            title: true,
            category: true,
            status: true,
            priority: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' }
        }
      }
    })

    if (!archive) {
      return res.status(404).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    return res.json({
      success: true,
      data: archive
    })
  } catch (error) {
    console.error('Get archive error:', error)
    return res.status(500).json({
      success: false,
      message: '获取项目档案详情失败'
    })
  }
}

// 创建项目档案
export const createArchive = async (req: Request, res: Response) => {
  try {
    const userId = (req as any).user?.userId
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      })
    }

    const data = createArchiveSchema.parse(req.body)

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: data.customerId }
    })

    if (!customer) {
      return res.status(400).json({
        success: false,
        message: '客户不存在'
      })
    }

    // 检查项目名称在该客户下是否已存在
    const existingArchive = await prisma.projectArchive.findFirst({
      where: {
        customerId: data.customerId,
        name: data.name
      }
    })

    if (existingArchive) {
      return res.status(400).json({
        success: false,
        message: '该客户下已存在同名项目档案'
      })
    }

    const archive = await prisma.projectArchive.create({
      data: {
        ...data,
        description: data.description || null,
        technology: data.technology || null,
        environment: data.environment || null,
        version: data.version || null,
        deploymentDate: data.deploymentDate || null,
        createdBy: userId
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
            level: true
          }
        },
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        }
      }
    })

    return res.status(201).json({
      success: true,
      message: '项目档案创建成功',
      data: archive
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Create archive error:', error)
    return res.status(500).json({
      success: false,
      message: '项目档案创建失败'
    })
  }
}

// 更新项目档案
export const updateArchive = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    const data = updateArchiveSchema.parse(req.body)

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查项目档案是否存在
    const existingArchive = await prisma.projectArchive.findUnique({
      where: { id }
    })

    if (!existingArchive) {
      return res.status(404).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    // 如果更新名称，检查是否与同客户下其他项目冲突
    if (data.name && data.name !== existingArchive.name) {
      const nameConflict = await prisma.projectArchive.findFirst({
        where: {
          customerId: existingArchive.customerId,
          name: data.name,
          id: { not: id }
        }
      })

      if (nameConflict) {
        return res.status(400).json({
          success: false,
          message: '该客户下已存在同名项目档案'
        })
      }
    }

    const updateData: any = {}
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.status !== undefined) updateData.status = data.status
    if (data.technology !== undefined) updateData.technology = data.technology || null
    if (data.environment !== undefined) updateData.environment = data.environment || null
    if (data.version !== undefined) updateData.version = data.version || null
    if (data.deploymentDate !== undefined) updateData.deploymentDate = data.deploymentDate
    if (data.description !== undefined) {
      updateData.description = data.description || null
    }

    const archive = await prisma.projectArchive.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            company: true,
            level: true
          }
        },
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        configurations: {
          select: {
            id: true,
            configType: true,
            title: true,
            isActive: true
          }
        }
      }
    })

    return res.json({
      success: true,
      message: '项目档案更新成功',
      data: archive
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        message: '请求参数验证失败',
        errors: error.errors
      })
    }

    console.error('Update archive error:', error)
    return res.status(500).json({
      success: false,
      message: '项目档案更新失败'
    })
  }
}

// 删除项目档案
export const deleteArchive = async (req: Request, res: Response) => {
  try {
    const { id } = req.params

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }

    // 检查项目档案是否存在
    const archive = await prisma.projectArchive.findUnique({
      where: { id },
      include: {
        services: true,
        configurations: true
      }
    })

    if (!archive) {
      return res.status(404).json({
        success: false,
        message: '项目档案不存在'
      })
    }

    // 检查是否有关联的服务工单
    if (archive.services.length > 0) {
      return res.status(400).json({
        success: false,
        message: '项目档案下还有服务工单，无法删除'
      })
    }

    // 先删除相关配置
    if (archive.configurations.length > 0) {
      await prisma.projectConfiguration.deleteMany({
        where: { archiveId: id }
      })
    }

    await prisma.projectArchive.delete({
      where: { id }
    })

    return res.json({
      success: true,
      message: '项目档案删除成功'
    })
  } catch (error) {
    console.error('Delete archive error:', error)
    return res.status(500).json({
      success: false,
      message: '项目档案删除失败'
    })
  }
}

// 获取项目档案统计信息
export const getArchiveStats = async (_req: Request, res: Response) => {
  try {
    const [
      totalArchives,
      statusStats,
      recentArchives,
      topTechnologies
    ] = await Promise.all([
      // 总项目数
      prisma.projectArchive.count(),
      
      // 按状态统计
      prisma.projectArchive.groupBy({
        by: ['status'],
        _count: {
          status: true
        }
      }),
      
      // 最近7天新增项目
      prisma.projectArchive.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // 技术栈分布
      prisma.projectArchive.groupBy({
        by: ['technology'],
        _count: {
          technology: true
        },
        where: {
          technology: {
            not: null
          }
        },
        orderBy: {
          _count: {
            technology: 'desc'
          }
        },
        take: 5
      })
    ])

    return res.json({
      success: true,
      data: {
        totalArchives,
        recentArchives,
        statusDistribution: statusStats.map(stat => ({
          status: stat.status,
          count: stat._count.status
        })),
        topTechnologies: topTechnologies.map(stat => ({
          technology: stat.technology,
          count: stat._count.technology
        }))
      }
    })
  } catch (error) {
    console.error('Get archive stats error:', error)
    return res.status(500).json({
      success: false,
      message: '获取项目档案统计信息失败'
    })
  }
}

// 获取客户的项目档案列表
export const getCustomerArchives = async (req: Request, res: Response) => {
  try {
    const { customerId } = req.params
    const { status } = req.query

    if (!customerId) {
      return res.status(400).json({
        success: false,
        message: 'customerId参数缺失'
      })
    }

    // 检查客户是否存在
    const customer = await prisma.customer.findUnique({
      where: { id: customerId }
    })

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: '客户不存在'
      })
    }

    const where: any = { customerId }
    
    if (status && typeof status === 'string') {
      where.status = status
    }

    const archives = await prisma.projectArchive.findMany({
      where,
      include: {
        createdByUser: {
          select: {
            username: true,
            fullName: true
          }
        },
        configurations: {
          select: {
            id: true,
            configType: true,
            title: true,
            isActive: true
          }
        },
        _count: {
          select: {
            services: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return res.json({
      success: true,
      data: archives
    })
  } catch (error) {
    console.error('Get customer archives error:', error)
    return res.status(500).json({
      success: false,
      message: '获取客户项目档案失败'
    })
  }
}