import crypto from 'crypto'
import bcrypt from 'bcryptjs'

const ALGORITHM = 'aes-256-cbc'
const KEY_LENGTH = 32
const IV_LENGTH = 16

// 获取加密密钥
function getEncryptionKey(): Buffer {
  const secret = process.env['CRYPTO_SECRET']
  if (!secret || secret.length < 32) {
    throw new Error('CRYPTO_SECRET must be at least 32 characters long')
  }
  return crypto.scryptSync(secret, 'salt', KEY_LENGTH)
}

// 加密数据
export function encrypt(text: string): string {
  try {
    const key = getEncryptionKey()
    const iv = crypto.randomBytes(IV_LENGTH)
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv)

    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    return iv.toString('hex') + ':' + encrypted
  } catch (error) {
    console.error('Encryption error:', error)
    throw new Error('加密失败')
  }
}

// 解密数据
export function decrypt(encryptedData: string): string {
  try {
    const key = getEncryptionKey()
    const parts = encryptedData.split(':')
    
    if (parts.length !== 2) {
      throw new Error('Invalid encrypted data format')
    }
    
    const iv = Buffer.from(parts[0]!, 'hex')
    const encrypted = parts[1]!
    
    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv)
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  } catch (error) {
    console.error('Decryption error:', error)
    throw new Error('解密失败')
  }
}

// 加密对象中的指定字段
export function encryptObjectFields(obj: any, fieldsToEncrypt: string[]): any {
  const result = { ...obj }
  
  for (const field of fieldsToEncrypt) {
    if (result[field] && typeof result[field] === 'string') {
      result[field] = encrypt(result[field])
    }
  }
  
  return result
}

// 解密对象中的指定字段
export function decryptObjectFields(obj: any, fieldsToDecrypt: string[]): any {
  const result = { ...obj }
  
  for (const field of fieldsToDecrypt) {
    if (result[field] && typeof result[field] === 'string') {
      try {
        result[field] = decrypt(result[field])
      } catch (error) {
        console.error(`Failed to decrypt field ${field}:`, error)
        // 如果解密失败，保持原值
      }
    }
  }
  
  return result
}

// 密码哈希
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12
  return bcrypt.hash(password, saltRounds)
}

// 验证密码
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

// 生成随机字符串
export function generateRandomString(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex')
}

// 生成安全的随机密码
export function generateSecurePassword(length: number = 16): string {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*'
  let password = ''
  
  for (let i = 0; i < length; i++) {
    const randomIndex = crypto.randomInt(0, charset.length)
    password += charset[randomIndex]
  }
  
  return password
}

// 创建哈希值
export function createHash(data: string, algorithm: string = 'sha256'): string {
  return crypto.createHash(algorithm).update(data).digest('hex')
}

// 创建HMAC
export function createHMAC(data: string, secret: string, algorithm: string = 'sha256'): string {
  return crypto.createHmac(algorithm, secret).update(data).digest('hex')
}

// 验证HMAC
export function verifyHMAC(data: string, signature: string, secret: string, algorithm: string = 'sha256'): boolean {
  const expectedSignature = createHMAC(data, secret, algorithm)
  return crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))
}