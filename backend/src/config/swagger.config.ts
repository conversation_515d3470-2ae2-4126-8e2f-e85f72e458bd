import { Application } from 'express'
import swagger<PERSON>SDoc from 'swagger-jsdoc'
import swaggerUi from 'swagger-ui-express'

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '运维服务管理系统 API',
      version: '1.0.0',
      description: '运维服务管理系统的 RESTful API 文档',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: process.env['API_BASE_URL'] || 'http://localhost:3001',
        description: '开发环境'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      responses: {
        UnauthorizedError: {
          description: '认证失败',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: '认证失败' },
                  code: { type: 'string', example: 'UNAUTHORIZED' }
                }
              }
            }
          }
        },
        ForbiddenError: {
          description: '权限不足',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: '权限不足' },
                  code: { type: 'string', example: 'FORBIDDEN' }
                }
              }
            }
          }
        },
        ValidationError: {
          description: '参数验证失败',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: '参数验证失败' },
                  code: { type: 'string', example: 'VALIDATION_ERROR' },
                  errors: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        field: { type: 'string' },
                        message: { type: 'string' }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        NotFoundError: {
          description: '资源不存在',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'boolean', example: false },
                  message: { type: 'string', example: '资源不存在' },
                  code: { type: 'string', example: 'NOT_FOUND' }
                }
              }
            }
          }
        }
      },
      schemas: {
        ApiResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              description: '请求是否成功'
            },
            message: {
              type: 'string',
              description: '响应消息'
            },
            data: {
              description: '响应数据'
            }
          }
        },
        PaginatedResponse: {
          allOf: [
            { $ref: '#/components/schemas/ApiResponse' },
            {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    items: {
                      type: 'array',
                      description: '数据列表'
                    },
                    pagination: {
                      type: 'object',
                      properties: {
                        page: { type: 'number', description: '当前页码' },
                        limit: { type: 'number', description: '每页数量' },
                        total: { type: 'number', description: '总数量' },
                        totalPages: { type: 'number', description: '总页数' }
                      }
                    }
                  }
                }
              }
            }
          ]
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts',
    './src/types/*.ts'
  ]
}

const specs = swaggerJSDoc(options)

export function setupSwagger(app: Application): void {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: '运维服务管理系统 API 文档'
  }))

  // 提供 JSON 格式的 API 文档
  app.get('/api-docs.json', (_req, res) => {
    res.setHeader('Content-Type', 'application/json')
    res.send(specs)
  })
}