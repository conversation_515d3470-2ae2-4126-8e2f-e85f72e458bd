import Redis from 'ioredis'

const redisUrl = process.env['REDIS_URL'] || 'redis://localhost:6379'

// 内存缓存作为降级方案
const memoryCache = new Map<string, { value: any; expireAt: number }>()

export const redis = new Redis(redisUrl, {
  maxRetriesPerRequest: null,
  lazyConnect: true,
  enableReadyCheck: false
})

let redisAvailable = false

redis.on('connect', () => {
  console.log('✅ Redis connected successfully')
  redisAvailable = true
})

redis.on('error', (error) => {
  console.error('❌ Redis connection error:', error)
  redisAvailable = false
})

redis.on('close', () => {
  console.log('📦 Redis disconnected')
  redisAvailable = false
})

// 清理过期的内存缓存
setInterval(() => {
  const now = Date.now()
  for (const [key, data] of memoryCache.entries()) {
    if (data.expireAt < now) {
      memoryCache.delete(key)
    }
  }
}, 60000) // 每分钟清理一次

// Redis 缓存工具类
export class CacheService {
  // 设置缓存
  static async set(key: string, value: any, expireInSeconds = 3600): Promise<void> {
    try {
      // 暂时禁用 Redis，直接使用内存缓存
      const expireAt = Date.now() + (expireInSeconds * 1000)
      memoryCache.set(key, { value, expireAt })
      console.log('📝 Using memory cache for:', key)
    } catch (error) {
      console.error('Cache set error:', error)
      // 降级到内存缓存
      const expireAt = Date.now() + (expireInSeconds * 1000)
      memoryCache.set(key, { value, expireAt })
    }
  }

  // 获取缓存
  static async get<T>(key: string): Promise<T | null> {
    try {
      // 暂时禁用 Redis，直接从内存缓存获取
      const cached = memoryCache.get(key)
      if (cached && cached.expireAt > Date.now()) {
        return cached.value
      }
      return null
    } catch (error) {
      console.error('Cache get error:', error)
      return null
    }
  }

  // 删除缓存
  static async del(key: string): Promise<void> {
    try {
      // 暂时禁用 Redis，直接从内存缓存删除
      memoryCache.delete(key)
    } catch (error) {
      console.error('Cache delete error:', error)
      memoryCache.delete(key)
    }
  }

  // 删除匹配的缓存
  static async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await redis.keys(pattern)
      if (keys.length > 0) {
        await redis.del(...keys)
      }
    } catch (error) {
      console.error('Cache delete pattern error:', error)
      throw error
    }
  }

  // 断开连接
  static async disconnect(): Promise<void> {
    try {
      await redis.disconnect()
    } catch (error) {
      console.error('Redis disconnect error:', error)
    }
  }

  // 检查缓存是否存在
  static async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key)
      return result === 1
    } catch (error) {
      console.error('Cache exists error:', error)
      return false
    }
  }

  // 设置哈希缓存
  static async hset(key: string, field: string, value: any): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value)
      await redis.hset(key, field, serializedValue)
    } catch (error) {
      console.error('Cache hset error:', error)
      throw error
    }
  }

  // 获取哈希缓存
  static async hget<T>(key: string, field: string): Promise<T | null> {
    try {
      const value = await redis.hget(key, field)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('Cache hget error:', error)
      return null
    }
  }

  // 获取所有哈希缓存
  static async hgetall<T>(key: string): Promise<Record<string, T>> {
    try {
      const hash = await redis.hgetall(key)
      const result: Record<string, T> = {}
      
      for (const [field, value] of Object.entries(hash)) {
        result[field] = JSON.parse(value)
      }
      
      return result
    } catch (error) {
      console.error('Cache hgetall error:', error)
      return {}
    }
  }
}