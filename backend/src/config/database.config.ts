import { PrismaClient } from '@prisma/client'

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env['NODE_ENV'] === 'development' ? ['query', 'error', 'warn'] : ['error'],
  errorFormat: 'pretty'
})

if (process.env['NODE_ENV'] !== 'production') {
  globalForPrisma.prisma = prisma
}

// 数据库连接测试
export async function connectDatabase() {
  try {
    await prisma.$connect()
    console.log('✅ Database connected successfully')
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    process.exit(1)
  }
}

// 优雅关闭数据库连接
export async function disconnectDatabase() {
  await prisma.$disconnect()
  console.log('📦 Database disconnected')
}