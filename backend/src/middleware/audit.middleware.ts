import { Request, Response, NextFunction } from 'express'
import { AuditService } from '@/services/audit.service'

interface AuditableRequest extends Request {
  user?: {
    userId: string
    username: string
  }
}

/**
 * 审计日志中间件配置
 */
interface AuditConfig {
  resource: string
  action?: string
  getResourceId?: (req: Request) => string | undefined
  getDetails?: (req: Request, res: Response) => Record<string, any> | undefined
  skipIf?: (req: Request) => boolean
}

/**
 * 创建审计日志中间件
 */
export function createAuditMiddleware(config: AuditConfig) {
  return async (req: AuditableRequest, res: Response, next: NextFunction) => {
    // 如果配置了跳过条件且满足，则跳过审计
    if (config.skipIf && config.skipIf(req)) {
      return next()
    }

    // 保存原始的 res.json 方法
    const originalJson = res.json

    // 重写 res.json 方法以在响应后记录日志
    res.json = function(body: any) {
      // 恢复原始方法
      res.json = originalJson

      // 只在成功响应时记录日志
      if (res.statusCode >= 200 && res.statusCode < 300 && req.user?.userId) {
        // 异步记录日志，不阻塞响应
        setImmediate(async () => {
          try {
            const action = config.action || getActionFromMethod(req.method)
            const resourceId = config.getResourceId ? config.getResourceId(req) : req.params['id']
            const details = config.getDetails ? config.getDetails(req, res) : undefined
            const ipAddress = getClientIpAddress(req)
            const userAgent = req.get('User-Agent')

            await AuditService.log({
              userId: req.user!.userId,
              action,
              resource: config.resource,
              resourceId: resourceId || null,
              details: details || null,
              ipAddress: ipAddress || null,
              userAgent: userAgent || null
            })
          } catch (error) {
            console.error('Failed to record audit log:', error)
          }
        })
      }

      // 调用原始的 json 方法
      return originalJson.call(this, body)
    }

    next()
  }
}

/**
 * 根据 HTTP 方法推断操作类型
 */
function getActionFromMethod(method: string): string {
  switch (method.toUpperCase()) {
    case 'POST':
      return 'CREATE'
    case 'PUT':
    case 'PATCH':
      return 'UPDATE'
    case 'DELETE':
      return 'DELETE'
    case 'GET':
      return 'VIEW'
    default:
      return 'UNKNOWN'
  }
}

/**
 * 获取客户端 IP 地址
 */
function getClientIpAddress(req: Request): string | undefined {
  return (
    req.ip ||
    req.connection.remoteAddress ||
    (req.headers['x-forwarded-for'] as string)?.split(',')[0]?.trim() ||
    (req.headers['x-real-ip'] as string) ||
    undefined
  )
}

/**
 * 预定义的审计中间件
 */
export const auditMiddleware = {
  // 客户管理审计
  customer: createAuditMiddleware({
    resource: 'CUSTOMER',
    getResourceId: (req) => req.params['id'],
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      params: req.params,
      body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined
    })
  }),

  // 项目档案审计
  archive: createAuditMiddleware({
    resource: 'ARCHIVE',
    getResourceId: (req) => req.params['id'],
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      params: req.params,
      body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined
    })
  }),

  // 服务工单审计
  service: createAuditMiddleware({
    resource: 'SERVICE',
    getResourceId: (req) => req.params['id'],
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      params: req.params,
      body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined
    })
  }),

  // 配置管理审计（敏感操作）
  configuration: createAuditMiddleware({
    resource: 'CONFIGURATION',
    getResourceId: (req) => req.params['id'],
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      params: req.params,
      configType: req.body?.configType
      // 注意：不记录敏感的配置数据
    })
  }),

  // SLA 管理审计
  sla: createAuditMiddleware({
    resource: 'SLA',
    getResourceId: (req) => req.params['id'],
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      params: req.params,
      body: req.method !== 'GET' ? sanitizeBody(req.body) : undefined
    })
  }),

  // 认证相关审计
  auth: createAuditMiddleware({
    resource: 'AUTH',
    getDetails: (req) => ({
      endpoint: req.path,
      method: req.method,
      username: req.body?.username
    }),
    skipIf: (req) => req.path.includes('refresh') // 跳过刷新令牌的记录
  })
}

/**
 * 清理请求体中的敏感信息
 */
function sanitizeBody(body: any): any {
  if (!body || typeof body !== 'object') {
    return body
  }

  const sensitiveFields = ['password', 'token', 'secret', 'key', 'configData']
  const sanitized = { ...body }

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]'
    }
  }

  return sanitized
}