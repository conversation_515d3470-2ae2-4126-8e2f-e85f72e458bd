import { Request, Response, NextFunction } from 'express'
import { ZodSchema, ZodError } from 'zod'

/**
 * 数据验证中间件
 */
export function validateRequest(schema: {
  body?: ZodSchema
  query?: ZodSchema
  params?: ZodSchema
}) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // 验证请求体
      if (schema.body) {
        req.body = schema.body.parse(req.body)
      }

      // 验证查询参数
      if (schema.query) {
        req.query = schema.query.parse(req.query)
      }

      // 验证路径参数
      if (schema.params) {
        req.params = schema.params.parse(req.params)
      }

      next()
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
            code: err.code
          }))
        })
      }
      next(error)
    }
  }
}

/**
 * 业务逻辑验证装饰器
 */
export function BusinessValidation(validationFn: (data: any) => Promise<string | null>) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      const [req, res] = args
      
      try {
        // 执行业务验证
        const validationError = await validationFn(req.body || req.query || req.params)
        
        if (validationError) {
          return res.status(400).json({
            success: false,
            message: validationError
          })
        }

        // 验证通过，执行原方法
        return await method.apply(this, args)
      } catch (error) {
        throw error
      }
    }
  }
}

/**
 * 常用的验证函数
 */
export class ValidationUtils {
  /**
   * 验证ID是否存在
   */
  static async validateEntityExists(
    model: any,
    id: string,
    entityName: string = '记录'
  ): Promise<string | null> {
    const entity = await model.findUnique({ where: { id } })
    if (!entity) {
      return `${entityName}不存在`
    }
    return null
  }

  /**
   * 验证唯一性
   */
  static async validateUniqueness(
    model: any,
    field: string,
    value: any,
    excludeId?: string,
    entityName: string = '记录'
  ): Promise<string | null> {
    const where: any = { [field]: value }
    if (excludeId) {
      where.id = { not: excludeId }
    }

    const existing = await model.findFirst({ where })
    if (existing) {
      return `${entityName}已存在`
    }
    return null
  }

  /**
   * 验证关联关系
   */
  static async validateRelationship(
    parentModel: any,
    parentId: string,
    childModel: any,
    relationField: string,
    parentName: string = '父记录',
    childName: string = '子记录'
  ): Promise<string | null> {
    // 检查父记录是否存在
    const parent = await parentModel.findUnique({ where: { id: parentId } })
    if (!parent) {
      return `${parentName}不存在`
    }

    // 检查是否有关联的子记录
    const children = await childModel.findMany({
      where: { [relationField]: parentId }
    })
    
    if (children.length > 0) {
      return `${parentName}下还有${children.length}个${childName}，无法删除`
    }

    return null
  }

  /**
   * 验证权限
   */
  static async validatePermission(
    userId: string,
    resourceId: string,
    permission: 'read' | 'write' | 'delete',
    checkOwnership: (userId: string, resourceId: string) => Promise<boolean>
  ): Promise<string | null> {
    const hasPermission = await checkOwnership(userId, resourceId)
    if (!hasPermission) {
      return '无权限执行此操作'
    }
    return null
  }

  /**
   * 验证状态转换
   */
  static validateStatusTransition(
    currentStatus: string,
    newStatus: string,
    allowedTransitions: Record<string, string[]>
  ): string | null {
    const allowed = allowedTransitions[currentStatus] || []
    if (!allowed.includes(newStatus)) {
      return `不允许从状态 "${currentStatus}" 转换到 "${newStatus}"`
    }
    return null
  }

  /**
   * 验证日期范围
   */
  static validateDateRange(startDate: Date, endDate: Date): string | null {
    if (startDate >= endDate) {
      return '开始日期必须早于结束日期'
    }
    return null
  }

  /**
   * 验证文件类型
   */
  static validateFileType(
    filename: string,
    allowedExtensions: string[]
  ): string | null {
    const ext = filename.split('.').pop()?.toLowerCase()
    if (!ext || !allowedExtensions.includes(ext)) {
      return `不支持的文件类型，允许的格式: ${allowedExtensions.join(', ')}`
    }
    return null
  }

  /**
   * 验证工作时间
   */
  static validateWorkingHours(date: Date): string | null {
    const hour = date.getHours()
    const day = date.getDay()
    
    // 工作日 9:00-18:00
    if (day >= 1 && day <= 5) {
      if (hour < 9 || hour >= 18) {
        return '只能在工作时间（9:00-18:00）内操作'
      }
    } else {
      return '只能在工作日内操作'
    }
    
    return null
  }

  /**
   * 验证SLA响应时间
   */
  static validateSlaResponse(
    createdAt: Date,
    respondedAt: Date,
    slaMinutes: number
  ): string | null {
    const diffMinutes = (respondedAt.getTime() - createdAt.getTime()) / (1000 * 60)
    if (diffMinutes > slaMinutes) {
      return `响应时间超出SLA要求（${slaMinutes}分钟）`
    }
    return null
  }
}

/**
 * 异步验证中间件
 */
export function asyncValidate(
  validationFn: (req: Request) => Promise<string | null>
) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const error = await validationFn(req)
      if (error) {
        return res.status(400).json({
          success: false,
          message: error
        })
      }
      next()
    } catch (error) {
      next(error)
    }
  }
}

/**
 * 组合验证器
 */
export class CompositeValidator {
  private validators: Array<(req: Request) => Promise<string | null>> = []

  add(validator: (req: Request) => Promise<string | null>): this {
    this.validators.push(validator)
    return this
  }

  build() {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        for (const validator of this.validators) {
          const error = await validator(req)
          if (error) {
            return res.status(400).json({
              success: false,
              message: error
            })
          }
        }
        next()
      } catch (error) {
        next(error)
      }
    }
  }
}

/**
 * 条件验证
 */
export function conditionalValidation(
  condition: (req: Request) => boolean,
  validator: (req: Request, res: Response, next: NextFunction) => void
) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (condition(req)) {
      return validator(req, res, next)
    }
    next()
  }
}