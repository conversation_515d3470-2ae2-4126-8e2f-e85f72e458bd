import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'
import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { UnauthorizedError, ForbiddenError } from './error.middleware'

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string
    username: string
    email: string
    roles: string[]
    permissions: string[]
  }
}

interface JWTPayload {
  userId: string
  username: string
  email: string
  role: string
  permissions: string[]
  iat: number
  exp: number
}

// JWT 验证中间件
export async function authenticateToken(
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (!token) {
      throw new UnauthorizedError('访问令牌不存在')
    }

    // 检查token是否在黑名单中
    const isBlacklisted = await CacheService.exists(`blacklist:${token}`)
    if (isBlacklisted) {
      throw new UnauthorizedError('访问令牌已失效')
    }

    // 验证 JWT
    const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as JWTPayload

    // 尝试从缓存获取用户信息
    let userInfo = await CacheService.get<any>(`user:${decoded.userId}`)

    if (!userInfo) {
      // 从数据库获取用户信息
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: {
          userRoles: {
            include: {
              role: true
            }
          }
        }
      })

      if (!user || user.status !== 'ACTIVE') {
        throw new UnauthorizedError('用户不存在或已被禁用')
      }

      // 提取角色和权限
      const roles = user.userRoles.map(ur => ur.role.name)
      const permissions = user.userRoles.reduce<string[]>((acc, ur) => {
        const rolePermissions = typeof ur.role.permissions === 'string' 
          ? JSON.parse(ur.role.permissions) 
          : ur.role.permissions
        return [...acc, ...rolePermissions]
      }, [])

      userInfo = {
        id: user.id,
        username: user.username,
        email: user.email,
        roles,
        permissions: [...new Set(permissions)] // 去重
      }

      // 缓存用户信息 (5分钟)
      await CacheService.set(`user:${decoded.userId}`, userInfo, 300)
    } else {
      // 如果从缓存获取，但JWT中有权限信息，优先使用JWT中的权限
      if (decoded.permissions && decoded.permissions.length > 0) {
        userInfo.permissions = decoded.permissions
      }
    }

    req.user = userInfo
    next()
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new UnauthorizedError('访问令牌无效'))
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new UnauthorizedError('访问令牌已过期'))
    } else {
      next(error)
    }
  }
}

// 权限检查中间件工厂
export function requirePermissions(...permissions: string[]) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userPermissions = req.user.permissions
      const hasPermission = permissions.every(permission => 
        userPermissions.includes(permission) || userPermissions.includes('admin:all')
      )

      if (!hasPermission) {
        throw new ForbiddenError(`需要权限: ${permissions.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 角色检查中间件工厂
export function requireRoles(...roles: string[]) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const userRoles = req.user.roles
      const hasRole = roles.some(role => 
        userRoles.includes(role) || userRoles.includes('admin')
      )

      if (!hasRole) {
        throw new ForbiddenError(`需要角色: ${roles.join(', ')}`)
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}

// 可选认证中间件（不强制要求认证）
export async function optionalAuth(
  req: AuthenticatedRequest,
  _res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization
    const token = authHeader && authHeader.split(' ')[1]

    if (token) {
      const decoded = jwt.verify(token, process.env['JWT_SECRET']!) as JWTPayload
      
      const userInfo = await CacheService.get<any>(`user:${decoded.userId}`)
      if (userInfo) {
        req.user = userInfo
      }
    }

    next()
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next()
  }
}

// 导出别名
export const authMiddleware = authenticateToken

// 用户自己或管理员权限检查
export function requireOwnershipOrAdmin(getUserId: (req: Request) => string) {
  return async (req: AuthenticatedRequest, _res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        throw new UnauthorizedError('用户未认证')
      }

      const targetUserId = getUserId(req)
      const isOwner = req.user.id === targetUserId
      const isAdmin = req.user.roles.includes('admin')

      if (!isOwner && !isAdmin) {
        throw new ForbiddenError('只能访问自己的资源')
      }

      next()
    } catch (error) {
      next(error)
    }
  }
}