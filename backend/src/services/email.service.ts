import nodemailer from 'nodemailer'
import { z } from 'zod'

// 邮件配置验证
const emailConfigSchema = z.object({
  host: z.string(),
  port: z.number(),
  user: z.string().email(),
  pass: z.string(),
  fromEmail: z.string().email(),
  fromName: z.string()
})

// 邮件发送参数验证
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().optional(),
  text: z.string().optional(),
  cc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  bcc: z.union([z.string().email(), z.array(z.string().email())]).optional(),
  attachments: z.array(z.object({
    filename: z.string(),
    path: z.string().optional(),
    content: z.any().optional(),
    contentType: z.string().optional()
  })).optional()
})

export interface EmailOptions {
  to: string | string[]
  subject: string
  html?: string
  text?: string
  cc?: string | string[]
  bcc?: string | string[]
  attachments?: Array<{
    filename: string
    path?: string
    content?: any
    contentType?: string
  }>
}

export interface EmailTemplate {
  subject: string
  html: string
  text?: string
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null
  private config: {
    host: string
    port: number
    user: string
    pass: string
    fromEmail: string
    fromName: string
  } | null = null

  constructor() {
    this.initializeTransporter()
  }

  private initializeTransporter() {
    try {
      const config = {
        host: process.env['SMTP_HOST'] || '',
        port: parseInt(process.env['SMTP_PORT'] || '587'),
        user: process.env['SMTP_USER'] || '',
        pass: process.env['SMTP_PASS'] || '',
        fromEmail: process.env['FROM_EMAIL'] || '',
        fromName: process.env['FROM_NAME'] || '运维服务管理系统'
      }

      // 检查是否所有必需的配置都已设置
      const hasValidConfig = config.host && 
                           config.user && 
                           config.pass && 
                           config.fromEmail && 
                           config.user !== '<EMAIL>' && 
                           config.fromEmail !== '<EMAIL>'

      if (!hasValidConfig) {
        console.log('⚠️  Email service not configured - using placeholder values. Email functionality will be disabled.')
        this.transporter = null
        this.config = null
        return
      }

      // 验证配置
      const validatedConfig = emailConfigSchema.parse(config)
      this.config = validatedConfig

      // 创建传输器
      this.transporter = nodemailer.createTransport({
        host: validatedConfig.host,
        port: validatedConfig.port,
        secure: validatedConfig.port === 465, // true for 465, false for other ports
        auth: {
          user: validatedConfig.user,
          pass: validatedConfig.pass
        },
        tls: {
          rejectUnauthorized: false
        }
      })

      console.log('✅ Email service initialized successfully')
    } catch (error) {
      console.error('❌ Email service initialization failed:', error)
      this.transporter = null
      this.config = null
    }
  }

  // 发送邮件
  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter || !this.config) {
        console.log('⚠️  Email service not configured - email not sent')
        return false
      }

      // 验证参数
      const validatedOptions = sendEmailSchema.parse(options)

      const mailOptions = {
        from: `"${this.config.fromName}" <${this.config.fromEmail}>`,
        to: Array.isArray(validatedOptions.to) ? validatedOptions.to.join(', ') : validatedOptions.to,
        subject: validatedOptions.subject,
        html: validatedOptions.html,
        text: validatedOptions.text || undefined,
        cc: validatedOptions.cc ? (Array.isArray(validatedOptions.cc) ? validatedOptions.cc.join(', ') : validatedOptions.cc) : undefined,
        bcc: validatedOptions.bcc ? (Array.isArray(validatedOptions.bcc) ? validatedOptions.bcc.join(', ') : validatedOptions.bcc) : undefined,
        attachments: validatedOptions.attachments
      }

      const result = await this.transporter.sendMail(mailOptions)
      console.log('📧 Email sent successfully:', result.messageId)
      return true
    } catch (error) {
      console.error('❌ Email send failed:', error)
      return false
    }
  }

  // 发送模板邮件
  async sendTemplateEmail(
    to: string | string[],
    templateName: string,
    variables: Record<string, any>,
    options?: Partial<EmailOptions>
  ): Promise<boolean> {
    try {
      const template = this.getEmailTemplate(templateName, variables)
      
      const emailOptions: EmailOptions = {
        to,
        subject: template.subject,
        html: template.html,
        ...options
      }
      
      if (template.text) {
        emailOptions.text = template.text
      }
      
      return await this.sendEmail(emailOptions)
    } catch (error) {
      console.error('❌ Template email send failed:', error)
      return false
    }
  }

  // 获取邮件模板
  private getEmailTemplate(templateName: string, variables: Record<string, any>): EmailTemplate {
    const templates: Record<string, (vars: Record<string, any>) => EmailTemplate> = {
      // 服务工单创建通知
      'service-created': (vars) => ({
        subject: `新服务工单 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">新服务工单已创建</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>优先级：</strong>${vars['priority']}</p>
              <p><strong>类别：</strong>${vars['category']}</p>
              <p><strong>描述：</strong></p>
              <p style="background: white; padding: 10px; border-radius: 3px;">${vars['description']}</p>
            </div>
            <p>请及时处理该工单。</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `新服务工单已创建\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n优先级：${vars['priority']}\n类别：${vars['category']}\n描述：${vars['description']}\n\n请及时处理该工单。`
      }),

      // 服务工单状态更新通知
      'service-status-updated': (vars) => ({
        subject: `工单状态更新 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">工单状态已更新</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>原状态：</strong>${vars['oldStatus']}</p>
              <p><strong>新状态：</strong><span style="color: #007bff;">${vars['newStatus']}</span></p>
              ${vars['comment'] ? `<p><strong>备注：</strong>${vars['comment']}</p>` : ''}
            </div>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `工单状态已更新\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n原状态：${vars['oldStatus']}\n新状态：${vars['newStatus']}${vars['comment'] ? `\n备注：${vars['comment']}` : ''}`
      }),

      // 服务工单分配通知
      'service-assigned': (vars) => ({
        subject: `工单已分配给您 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #333;">工单已分配给您</h2>
            <div style="background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>优先级：</strong>${vars['priority']}</p>
              <p><strong>分配人：</strong>${vars['assignedBy']}</p>
              <p><strong>描述：</strong></p>
              <p style="background: white; padding: 10px; border-radius: 3px;">${vars['description']}</p>
            </div>
            <p>请及时处理该工单。</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `工单已分配给您\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n优先级：${vars['priority']}\n分配人：${vars['assignedBy']}\n描述：${vars['description']}\n\n请及时处理该工单。`
      }),

      // SLA违规警告
      'sla-violation-warning': (vars) => ({
        subject: `SLA违规警告 - ${vars['ticketNumber']}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <h2 style="color: #dc3545;">SLA违规警告</h2>
            <div style="background: #f8d7da; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #dc3545;">
              <p><strong>工单号：</strong>${vars['ticketNumber']}</p>
              <p><strong>标题：</strong>${vars['title']}</p>
              <p><strong>客户：</strong>${vars['customerName']}</p>
              <p><strong>SLA要求：</strong>${vars['slaRequirement']}</p>
              <p><strong>当前状态：</strong>${vars['currentStatus']}</p>
              <p><strong>超时时间：</strong>${vars['overdueTime']}</p>
            </div>
            <p style="color: #dc3545; font-weight: bold;">请立即处理该工单以避免SLA违规！</p>
            <p style="color: #666; font-size: 12px;">此邮件由运维服务管理系统自动发送</p>
          </div>
        `,
        text: `SLA违规警告\n\n工单号：${vars['ticketNumber']}\n标题：${vars['title']}\n客户：${vars['customerName']}\n SLA要求：${vars['slaRequirement']}\n当前状态：${vars['currentStatus']}\n超时时间：${vars['overdueTime']}\n\n请立即处理该工单以避免SLA违规！`
      })
    }

    const templateFunction = templates[templateName]
    if (!templateFunction) {
      throw new Error(`Email template '${templateName}' not found`)
    }

    return templateFunction(variables)
  }

  // 测试邮件连接
  async testConnection(): Promise<boolean> {
    try {
      if (!this.transporter) {
        return false
      }

      await this.transporter.verify()
      console.log('✅ Email connection test successful')
      return true
    } catch (error) {
      console.error('❌ Email connection test failed:', error)
      return false
    }
  }

  // 获取服务状态
  isConfigured(): boolean {
    return this.transporter !== null && this.config !== null
  }
}

// 导出单例实例
export const emailService = new EmailService()
export default emailService
