import { z } from 'zod'

// 短信发送参数验证
const sendSmsSchema = z.object({
  phoneNumber: z.string().regex(/^1[3-9]\d{9}$/, '手机号格式不正确'),
  message: z.string().min(1).max(500),
  templateCode: z.string().optional(),
  templateParams: z.record(z.string()).optional()
})

export interface SmsOptions {
  phoneNumber: string
  message: string
  templateCode?: string
  templateParams?: Record<string, string>
}

export interface SmsTemplate {
  code: string
  content: string
  params: string[]
}

class SmsService {
  private config: {
    accessKeyId: string
    accessKeySecret: string
    region: string
    signName: string
  } | null = null

  constructor() {
    this.initializeConfig()
  }

  private initializeConfig() {
    try {
      const config = {
        accessKeyId: process.env['ALI_SMS_ACCESS_KEY_ID'] || '',
        accessKeySecret: process.env['ALI_SMS_ACCESS_KEY_SECRET'] || '',
        region: process.env['ALI_SMS_REGION'] || 'cn-hangzhou',
        signName: process.env['ALI_SMS_SIGN_NAME'] || ''
      }

      // 检查必要的配置
      if (!config.accessKeyId || !config.accessKeySecret || !config.signName) {
        console.warn('⚠️  SMS service not configured properly')
        this.config = null
        return
      }

      this.config = config
      console.log('✅ SMS service initialized successfully')
    } catch (error) {
      console.error('❌ SMS service initialization failed:', error)
      this.config = null
    }
  }

  // 发送短信
  async sendSms(options: SmsOptions): Promise<boolean> {
    try {
      if (!this.config) {
        console.warn('SMS service not configured, skipping SMS send')
        return false
      }

      // 验证参数
      const validatedOptions = sendSmsSchema.parse(options)

      // 这里应该集成实际的短信服务提供商API
      // 由于这是示例代码，我们只是模拟发送
      console.log('📱 SMS would be sent:', {
        to: validatedOptions.phoneNumber,
        message: validatedOptions.message,
        templateCode: validatedOptions.templateCode,
        templateParams: validatedOptions.templateParams
      })

      // 在实际环境中，这里应该调用阿里云短信服务API
      // 示例代码：
      /*
      const Core = require('@alicloud/pop-core')
      
      const client = new Core({
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
        endpoint: 'https://dysmsapi.aliyuncs.com',
        apiVersion: '2017-05-25'
      })

      const params = {
        PhoneNumbers: validatedOptions.phoneNumber,
        SignName: this.config.signName,
        TemplateCode: validatedOptions.templateCode || 'SMS_TEMPLATE_CODE',
        TemplateParam: JSON.stringify(validatedOptions.templateParams || {})
      }

      const result = await client.request('SendSms', params, {})
      return result.Code === 'OK'
      */

      return true
    } catch (error) {
      console.error('❌ SMS send failed:', error)
      return false
    }
  }

  // 发送模板短信
  async sendTemplateSms(
    phoneNumber: string,
    templateName: string,
    variables: Record<string, string>
  ): Promise<boolean> {
    try {
      const template = this.getSmsTemplate(templateName)
      
      // 替换模板变量
      let message = template.content
      for (const [key, value] of Object.entries(variables)) {
        message = message.replace(new RegExp(`\\{${key}\\}`, 'g'), value)
      }

      return await this.sendSms({
        phoneNumber,
        message,
        templateCode: template.code,
        templateParams: variables
      })
    } catch (error) {
      console.error('❌ Template SMS send failed:', error)
      return false
    }
  }

  // 获取短信模板
  private getSmsTemplate(templateName: string): SmsTemplate {
    const templates: Record<string, SmsTemplate> = {
      // 服务工单创建通知
      'service-created': {
        code: 'SMS_SERVICE_CREATED',
        content: '您有新的服务工单{ticketNumber}需要处理，优先级：{priority}，请及时查看。',
        params: ['ticketNumber', 'priority']
      },

      // 服务工单分配通知
      'service-assigned': {
        code: 'SMS_SERVICE_ASSIGNED',
        content: '工单{ticketNumber}已分配给您，客户：{customerName}，请及时处理。',
        params: ['ticketNumber', 'customerName']
      },

      // SLA违规警告
      'sla-violation-warning': {
        code: 'SMS_SLA_WARNING',
        content: '紧急：工单{ticketNumber}即将违反SLA，请立即处理！',
        params: ['ticketNumber']
      },

      // 服务工单完成通知
      'service-completed': {
        code: 'SMS_SERVICE_COMPLETED',
        content: '工单{ticketNumber}已完成，请查看处理结果并确认。',
        params: ['ticketNumber']
      },

      // 验证码短信
      'verification-code': {
        code: 'SMS_VERIFICATION_CODE',
        content: '您的验证码是{code}，5分钟内有效，请勿泄露。',
        params: ['code']
      }
    }

    const template = templates[templateName]
    if (!template) {
      throw new Error(`SMS template '${templateName}' not found`)
    }

    return template
  }

  // 批量发送短信
  async sendBulkSms(
    phoneNumbers: string[],
    message: string,
    templateCode?: string,
    templateParams?: Record<string, string>
  ): Promise<{ success: number; failed: number; results: boolean[] }> {
    const results: boolean[] = []
    let success = 0
    let failed = 0

    for (const phoneNumber of phoneNumbers) {
      try {
        const result = await this.sendSms({
          phoneNumber,
          message,
          templateCode,
          templateParams
        })
        
        results.push(result)
        if (result) {
          success++
        } else {
          failed++
        }
      } catch (error) {
        console.error(`Failed to send SMS to ${phoneNumber}:`, error)
        results.push(false)
        failed++
      }
    }

    return { success, failed, results }
  }

  // 验证手机号格式
  validatePhoneNumber(phoneNumber: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phoneNumber)
  }

  // 获取服务状态
  isConfigured(): boolean {
    return this.config !== null
  }

  // 获取配置信息（隐藏敏感信息）
  getConfigInfo(): any {
    if (!this.config) {
      return { configured: false }
    }

    return {
      configured: true,
      region: this.config.region,
      signName: this.config.signName,
      accessKeyId: this.config.accessKeyId.substring(0, 8) + '***'
    }
  }
}

// 导出单例实例
export const smsService = new SmsService()
export default smsService
