import { prisma } from '@/config/database.config'
import { CacheService } from '@/config/redis.config'
import { ErrorFactory } from '@/utils/errors.util'

export interface CreateRoleInput {
  name: string
  description: string
  permissions: string[]
}

export interface UpdateRoleInput {
  name?: string
  description?: string
  permissions?: string[]
}

export interface RoleWithStats {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount: number
  createdAt: Date
  updatedAt: Date
}

// 系统预定义权限
export const SYSTEM_PERMISSIONS = {
  // 管理员权限
  'admin:all': '系统管理员（所有权限）',
  
  // 用户管理
  'user:read': '查看用户',
  'user:write': '创建/编辑用户',
  'user:delete': '删除用户',
  'user:manage': '用户管理（包含所有用户相关权限）',
  
  // 角色管理
  'role:read': '查看角色',
  'role:write': '创建/编辑角色',
  'role:delete': '删除角色',
  'role:manage': '角色管理（包含所有角色相关权限）',
  
  // 客户管理
  'customer:read': '查看客户',
  'customer:write': '创建/编辑客户',
  'customer:delete': '删除客户',
  'customer:manage': '客户管理（包含所有客户相关权限）',
  
  // 项目档案
  'archive:read': '查看项目档案',
  'archive:write': '创建/编辑项目档案',
  'archive:delete': '删除项目档案',
  'archive:manage': '项目档案管理（包含所有档案相关权限）',
  
  // 服务工单
  'service:read': '查看服务工单',
  'service:write': '创建/编辑服务工单',
  'service:delete': '删除服务工单',
  'service:assign': '分配服务工单',
  'service:manage': '服务工单管理（包含所有工单相关权限）',
  
  // 配置管理
  'config:read': '查看配置',
  'config:write': '创建/编辑配置',
  'config:delete': '删除配置',
  'config:manage': '配置管理（包含所有配置相关权限）',
  
  // SLA管理
  'sla:read': '查看SLA',
  'sla:write': '创建/编辑SLA',
  'sla:delete': '删除SLA',
  'sla:manage': 'SLA管理（包含所有SLA相关权限）',
  
  // 通知管理
  'notification:read': '查看通知设置',
  'notification:send': '发送通知',
  'notification:manage': '通知管理（包含所有通知相关权限）',
  
  // 审计日志
  'audit:read': '查看审计日志',
  'audit:export': '导出审计日志',
  'audit:manage': '审计管理（包含所有审计相关权限）',
  
  // 文件管理
  'file:upload': '上传文件',
  'file:download': '下载文件',
  'file:delete': '删除文件',
  'file:manage': '文件管理（包含所有文件相关权限）',
  
  // 系统设置
  'system:read': '查看系统设置',
  'system:write': '修改系统设置',
  'system:backup': '系统备份',
  'system:monitor': '系统监控',
  'system:manage': '系统管理（包含所有系统相关权限）'
} as const

// 预定义角色模板
export const ROLE_TEMPLATES = {
  'super-admin': {
    name: 'super-admin',
    description: '超级管理员',
    permissions: ['admin:all']
  },
  'admin': {
    name: 'admin',
    description: '系统管理员',
    permissions: [
      'user:manage',
      'role:manage',
      'customer:manage',
      'archive:manage',
      'service:manage',
      'config:manage',
      'sla:manage',
      'notification:manage',
      'audit:manage',
      'file:manage',
      'system:manage'
    ]
  },
  'manager': {
    name: 'manager',
    description: '项目经理',
    permissions: [
      'customer:read',
      'customer:write',
      'archive:manage',
      'service:manage',
      'config:read',
      'sla:read',
      'notification:send',
      'audit:read',
      'file:manage'
    ]
  },
  'engineer': {
    name: 'engineer',
    description: '运维工程师',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'service:write',
      'config:read',
      'config:write',
      'sla:read',
      'notification:send',
      'file:upload',
      'file:download'
    ]
  },
  'support': {
    name: 'support',
    description: '技术支持',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'service:write',
      'sla:read',
      'notification:send',
      'file:upload',
      'file:download'
    ]
  },
  'viewer': {
    name: 'viewer',
    description: '只读用户',
    permissions: [
      'customer:read',
      'archive:read',
      'service:read',
      'config:read',
      'sla:read',
      'file:download'
    ]
  }
} as const

export class RoleService {
  /**
   * 创建角色
   */
  static async createRole(data: CreateRoleInput): Promise<RoleWithStats> {
    // 检查角色名唯一性
    const existingRole = await prisma.role.findUnique({
      where: { name: data.name }
    })

    if (existingRole) {
      throw ErrorFactory.conflict('角色名已存在')
    }

    // 验证权限
    this.validatePermissions(data.permissions)

    // 创建角色
    const role = await prisma.role.create({
      data: {
        name: data.name,
        description: data.description,
        permissions: JSON.stringify(data.permissions)
      }
    })

    return this.formatRoleWithStats(role, 0)
  }

  /**
   * 获取角色列表
   */
  static async getRoles(
    page: number = 1,
    limit: number = 20,
    search?: string
  ) {
    const skip = (page - 1) * limit
    
    const where: any = {}
    
    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } }
      ]
    }

    const [roles, total] = await Promise.all([
      prisma.role.findMany({
        where,
        skip,
        take: limit,
        include: {
          userRoles: {
            include: {
              user: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),
      prisma.role.count({ where })
    ])

    return {
      roles: roles.map(role => this.formatRoleWithStats(role, role.userRoles.length)),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }

  /**
   * 获取所有角色（用于下拉选择）
   */
  static async getAllRoles(): Promise<Array<{
    id: string
    name: string
    description: string | null
  }>> {
    const roles = await prisma.role.findMany({
      select: {
        id: true,
        name: true,
        description: true
      },
      orderBy: { name: 'asc' }
    })

    return roles
  }

  /**
   * 获取角色详情
   */
  static async getRoleById(id: string): Promise<RoleWithStats> {
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    return this.formatRoleWithStats(role, role.userRoles.length)
  }

  /**
   * 更新角色
   */
  static async updateRole(id: string, data: UpdateRoleInput): Promise<RoleWithStats> {
    // 检查角色是否存在
    const existingRole = await prisma.role.findUnique({
      where: { id }
    })

    if (!existingRole) {
      throw ErrorFactory.notFound('角色')
    }

    // 检查角色名唯一性
    if (data.name && data.name !== existingRole.name) {
      const nameConflict = await prisma.role.findUnique({
        where: { name: data.name }
      })

      if (nameConflict) {
        throw ErrorFactory.conflict('角色名已存在')
      }
    }

    // 验证权限
    if (data.permissions) {
      this.validatePermissions(data.permissions)
    }

    // 更新角色
    const updateData: any = { updatedAt: new Date() }
    
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.permissions !== undefined) {
      updateData.permissions = JSON.stringify(data.permissions)
    }

    const role = await prisma.role.update({
      where: { id },
      data: updateData,
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    // 清除相关用户的缓存
    await this.clearUserCaches(id)

    return this.formatRoleWithStats(role, role.userRoles.length)
  }

  /**
   * 删除角色
   */
  static async deleteRole(id: string): Promise<void> {
    // 检查角色是否存在
    const role = await prisma.role.findUnique({
      where: { id },
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    if (!role) {
      throw ErrorFactory.notFound('角色')
    }

    // 检查是否有用户使用此角色
    if (role.userRoles.length > 0) {
      throw ErrorFactory.business(`该角色正被 ${role.userRoles.length} 个用户使用，无法删除`)
    }

    // 删除角色
    await prisma.role.delete({
      where: { id }
    })
  }

  /**
   * 获取系统权限列表
   */
  static getSystemPermissions(): Array<{
    key: string
    description: string
    category: string
  }> {
    return Object.entries(SYSTEM_PERMISSIONS).map(([key, description]) => {
      const category = key.split(':')[0] || 'other'
      return {
        key,
        description,
        category
      }
    })
  }

  /**
   * 获取权限分组
   */
  static getPermissionGroups(): Record<string, Array<{
    key: string
    description: string
  }>> {
    const groups: Record<string, Array<{ key: string, description: string }>> = {}

    Object.entries(SYSTEM_PERMISSIONS).forEach(([key, description]) => {
      const category = key.split(':')[0] || 'other'
      if (!groups[category]) {
        groups[category] = []
      }
      groups[category].push({ key, description })
    })

    return groups
  }

  /**
   * 获取角色模板
   */
  static getRoleTemplates(): Array<{
    name: string
    description: string
    permissions: string[]
  }> {
    return Object.values(ROLE_TEMPLATES).map(template => ({
      name: template.name,
      description: template.description,
      permissions: [...template.permissions]
    }))
  }

  /**
   * 从模板创建角色
   */
  static async createRoleFromTemplate(
    templateName: keyof typeof ROLE_TEMPLATES,
    customName?: string,
    customDescription?: string
  ): Promise<RoleWithStats> {
    const template = ROLE_TEMPLATES[templateName]
    
    if (!template) {
      throw ErrorFactory.notFound('角色模板')
    }

    return this.createRole({
      name: customName || template.name,
      description: customDescription || template.description,
      permissions: [...template.permissions]
    })
  }

  /**
   * 复制角色
   */
  static async duplicateRole(
    id: string,
    newName: string,
    newDescription?: string
  ): Promise<RoleWithStats> {
    const sourceRole = await this.getRoleById(id)
    
    return this.createRole({
      name: newName,
      description: newDescription || `${sourceRole.description} (副本)`,
      permissions: sourceRole.permissions
    })
  }

  /**
   * 获取角色统计信息
   */
  static async getRoleStats() {
    const [
      totalRoles,
      rolesWithUsers,
      permissionUsage
    ] = await Promise.all([
      prisma.role.count(),
      prisma.role.findMany({
        include: {
          userRoles: {
            include: {
              user: true
            }
          }
        }
      }),
      this.getPermissionUsageStats()
    ])

    return {
      totalRoles,
      rolesWithUsers: rolesWithUsers.filter(role => role.userRoles.length > 0).length,
      emptyRoles: rolesWithUsers.filter(role => role.userRoles.length === 0).length,
      roleUserDistribution: rolesWithUsers.map(role => ({
        roleName: role.name,
        userCount: role.userRoles.length
      })),
      permissionUsage
    }
  }

  /**
   * 获取权限使用统计
   */
  private static async getPermissionUsageStats() {
    const roles = await prisma.role.findMany({
      include: {
        userRoles: {
          include: {
            user: true
          }
        }
      }
    })

    const permissionCount: Record<string, number> = {}

    roles.forEach(role => {
      const permissions = typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions

      permissions.forEach((permission: string) => {
        permissionCount[permission] = (permissionCount[permission] || 0) + role.userRoles.length
      })
    })

    return Object.entries(permissionCount).map(([permission, userCount]) => ({
      permission,
      userCount,
      description: SYSTEM_PERMISSIONS[permission as keyof typeof SYSTEM_PERMISSIONS] || '未知权限'
    }))
  }

  /**
   * 验证权限列表
   */
  private static validatePermissions(permissions: string[]): void {
    const validPermissions = Object.keys(SYSTEM_PERMISSIONS)
    const invalidPermissions = permissions.filter(
      permission => !validPermissions.includes(permission)
    )

    if (invalidPermissions.length > 0) {
      throw ErrorFactory.validation(
        `无效的权限: ${invalidPermissions.join(', ')}`
      )
    }
  }

  /**
   * 清除用户缓存
   */
  private static async clearUserCaches(roleId: string): Promise<void> {
    const userRoles = await prisma.userRole.findMany({
      where: { roleId },
      select: { userId: true }
    })

    const cacheKeys = userRoles.map(userRole => `user:${userRole.userId}`)
    if (cacheKeys.length > 0) {
      for (const key of cacheKeys) {
        await CacheService.del(key)
      }
    }
  }

  /**
   * 格式化角色数据
   */
  private static formatRoleWithStats(role: any, userCount: number): RoleWithStats {
    return {
      id: role.id,
      name: role.name,
      description: role.description,
      permissions: typeof role.permissions === 'string' 
        ? JSON.parse(role.permissions) 
        : role.permissions,
      userCount,
      createdAt: role.createdAt,
      updatedAt: role.updatedAt
    }
  }
}