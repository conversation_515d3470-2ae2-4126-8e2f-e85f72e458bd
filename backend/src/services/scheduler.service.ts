import cron from 'node-cron'
import { prisma } from '@/config/database.config'
import { emailService } from '@/services/email.service'
import { smsService } from '@/services/sms.service'
import { UploadService } from '@/services/upload.service'

export interface ScheduledTask {
  name: string
  schedule: string
  description: string
  lastRun?: Date
  nextRun?: Date
  isActive: boolean
  task: () => Promise<void>
}

export class SchedulerService {
  private static tasks: Map<string, cron.ScheduledTask> = new Map()
  private static taskConfigs: Map<string, ScheduledTask> = new Map()

  /**
   * 初始化调度器
   */
  static init() {
    console.log('🕐 初始化任务调度器...')

    // 定义所有定时任务
    const tasks: ScheduledTask[] = [
      {
        name: 'daily-report',
        schedule: '0 8 * * 1-5', // 工作日早上8点
        description: '发送每日报告',
        isActive: true,
        task: this.sendDailyReport
      },
      {
        name: 'weekly-summary',
        schedule: '0 9 * * 1', // 每周一上午9点
        description: '发送周报',
        isActive: true,
        task: this.sendWeeklySummary
      },
      {
        name: 'monthly-report',
        schedule: '0 10 1 * *', // 每月1号上午10点
        description: '发送月报',
        isActive: true,
        task: this.sendMonthlyReport
      },
      {
        name: 'sla-alert',
        schedule: '*/30 * * * *', // 每30分钟
        description: 'SLA超时预警',
        isActive: true,
        task: this.checkSlaAlerts
      },
      {
        name: 'overdue-tickets',
        schedule: '0 */4 * * *', // 每4小时
        description: '逾期工单提醒',
        isActive: true,
        task: this.notifyOverdueTickets
      },
      {
        name: 'cleanup-files',
        schedule: '0 2 * * 0', // 每周日凌晨2点
        description: '清理过期文件',
        isActive: true,
        task: this.cleanupOldFiles
      },
      {
        name: 'system-health',
        schedule: '*/15 * * * *', // 每15分钟
        description: '系统健康检查',
        isActive: true,
        task: this.systemHealthCheck
      },
      {
        name: 'backup-reminder',
        schedule: '0 18 * * 5', // 每周五下午6点
        description: '数据备份提醒',
        isActive: true,
        task: this.sendBackupReminder
      }
    ]

    // 注册所有任务
    tasks.forEach(taskConfig => {
      this.registerTask(taskConfig)
    })

    console.log(`✅ 已注册 ${tasks.length} 个定时任务`)
  }

  /**
   * 注册任务
   */
  static registerTask(taskConfig: ScheduledTask) {
    if (!taskConfig.isActive) {
      console.log(`⏸️  任务 "${taskConfig.name}" 已禁用`)
      return
    }

    try {
      const task = cron.schedule(taskConfig.schedule, async () => {
        console.log(`🔄 执行任务: ${taskConfig.name}`)
        const startTime = Date.now()

        try {
          await taskConfig.task()
          const duration = Date.now() - startTime
          console.log(`✅ 任务 "${taskConfig.name}" 执行成功，耗时: ${duration}ms`)
        } catch (error) {
          console.error(`❌ 任务 "${taskConfig.name}" 执行失败:`, error)
        }
      }, {
        scheduled: false
      })

      this.tasks.set(taskConfig.name, task)
      this.taskConfigs.set(taskConfig.name, taskConfig)

      // 启动任务
      task.start()
      console.log(`📅 任务 "${taskConfig.name}" 已启动，计划: ${taskConfig.schedule}`)
    } catch (error) {
      console.error(`❌ 注册任务 "${taskConfig.name}" 失败:`, error)
    }
  }

  /**
   * 停止所有任务
   */
  static stopAll() {
    this.tasks.forEach((task, name) => {
      task.stop()
      console.log(`⏹️  任务 "${name}" 已停止`)
    })
    this.tasks.clear()
    this.taskConfigs.clear()
  }

  /**
   * 获取任务状态
   */
  static getTaskStatus() {
    const status = Array.from(this.taskConfigs.entries()).map(([name, config]) => {
      const task = this.tasks.get(name)
      return {
        name,
        description: config.description,
        schedule: config.schedule,
        isActive: config.isActive,
        isRunning: task ? task.running : false
      }
    })

    return status
  }

  // ======================== 具体任务实现 ========================

  /**
   * 发送每日报告
   */
  private static async sendDailyReport() {
    const today = new Date()
    const yesterday = new Date(today)
    yesterday.setDate(yesterday.getDate() - 1)

    // 获取昨日数据
    const [
      newServices,
      closedServices,
      pendingServices,
      overdueServices
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: yesterday,
            lt: today
          }
        }
      }),
      prisma.service.count({
        where: {
          status: 'CLOSED',
          updatedAt: {
            gte: yesterday,
            lt: today
          }
        }
      }),
      prisma.service.count({
        where: {
          status: {
            in: ['OPEN', 'IN_PROGRESS', 'PENDING']
          }
        }
      }),
      prisma.service.count({
        where: {
          status: {
            in: ['OPEN', 'IN_PROGRESS', 'PENDING']
          },
          createdAt: {
            lt: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      })
    ])

    // 获取管理员邮箱
    const admins = await prisma.user.findMany({
      where: {
        role: {
          name: 'admin'
        }
      },
      select: {
        email: true,
        fullName: true
      }
    })

    if (admins.length === 0) return

    const emailContent = `
      <h2>运维服务日报 - ${yesterday.toLocaleDateString()}</h2>
      <div style="font-family: Arial, sans-serif;">
        <h3>📊 统计数据</h3>
        <ul>
          <li><strong>新增工单:</strong> ${newServices} 个</li>
          <li><strong>已关闭工单:</strong> ${closedServices} 个</li>
          <li><strong>待处理工单:</strong> ${pendingServices} 个</li>
          <li><strong>逾期工单:</strong> ${overdueServices} 个</li>
        </ul>
        
        ${overdueServices > 0 ? `
        <div style="background-color: #ffebee; padding: 10px; border-radius: 5px; margin-top: 20px;">
          <h4 style="color: #c62828;">⚠️ 注意：有 ${overdueServices} 个工单已逾期，请及时处理！</h4>
        </div>
        ` : ''}
        
        <hr style="margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">
          这是系统自动发送的邮件，请勿回复。
        </p>
      </div>
    `

    // 发送邮件给所有管理员
    for (const admin of admins) {
      if (admin.email) {
        try {
          await emailService.sendEmail({
            to: admin.email,
            subject: `运维服务日报 - ${yesterday.toLocaleDateString()}`,
            html: emailContent
          })
        } catch (error) {
          console.error(`发送日报邮件失败 (${admin.email}):`, error)
        }
      }
    }
  }

  /**
   * 发送周报
   */
  private static async sendWeeklySummary() {
    const today = new Date()
    const lastWeek = new Date(today)
    lastWeek.setDate(lastWeek.getDate() - 7)

    // 获取上周数据
    const [
      weeklyServices,
      weeklyCustomers,
      avgResponseTime,
      customerSatisfaction
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: lastWeek,
            lt: today
          }
        }
      }),
      prisma.customer.count({
        where: {
          createdAt: {
            gte: lastWeek,
            lt: today
          }
        }
      }),
      prisma.service.aggregate({
        where: {
          actualResponseTime: { not: null },
          updatedAt: {
            gte: lastWeek,
            lt: today
          }
        },
        _avg: {
          actualResponseTime: true
        }
      }),
      // 这里可以添加客户满意度计算逻辑
      Promise.resolve(4.2) // 示例数据
    ])

    const emailContent = `
      <h2>运维服务周报 - ${lastWeek.toLocaleDateString()} 至 ${today.toLocaleDateString()}</h2>
      <div style="font-family: Arial, sans-serif;">
        <h3>📈 本周亮点</h3>
        <ul>
          <li><strong>服务工单:</strong> ${weeklyServices} 个</li>
          <li><strong>新增客户:</strong> ${weeklyCustomers} 个</li>
          <li><strong>平均响应时间:</strong> ${Math.round(avgResponseTime._avg.actualResponseTime || 0)} 分钟</li>
          <li><strong>客户满意度:</strong> ${customerSatisfaction}/5.0</li>
        </ul>
        
        <h3>🎯 下周重点</h3>
        <ul>
          <li>继续优化响应时间</li>
          <li>加强客户服务质量</li>
          <li>完善运维流程</li>
        </ul>
      </div>
    `

    // 发送给管理员
    const admins = await prisma.user.findMany({
      where: { role: { name: 'admin' } },
      select: { email: true }
    })

    for (const admin of admins) {
      if (admin.email) {
        await emailService.sendEmail({
          to: admin.email,
          subject: '运维服务周报',
          html: emailContent
        })
      }
    }
  }

  /**
   * 发送月报
   */
  private static async sendMonthlyReport() {
    const today = new Date()
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1)

    // 获取上月数据
    const [
      monthlyServices,
      monthlyRevenue,
      topCustomers,
      slaCompliance
    ] = await Promise.all([
      prisma.service.count({
        where: {
          createdAt: {
            gte: lastMonth,
            lt: thisMonth
          }
        }
      }),
      // 这里可以添加收入计算逻辑
      Promise.resolve(0),
      prisma.service.groupBy({
        by: ['customerId'],
        where: {
          createdAt: {
            gte: lastMonth,
            lt: thisMonth
          }
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 5
      }),
      // SLA 合规性计算
      Promise.resolve(95.8) // 示例数据
    ])

    // 生成月报内容...
    console.log('生成月报:', {
      monthlyServices,
      monthlyRevenue,
      topCustomers: topCustomers.length,
      slaCompliance
    })
  }

  /**
   * SLA超时预警
   */
  private static async checkSlaAlerts() {
    // 获取接近SLA超时的工单
    const criticalServices = await prisma.service.findMany({
      where: {
        status: {
          in: ['OPEN', 'IN_PROGRESS']
        },
        slaTemplate: {
          isNot: null
        }
      },
      include: {
        slaTemplate: true,
        customer: true,
        assignedUser: true
      }
    })

    const now = new Date()
    const alerts = []

    for (const service of criticalServices) {
      if (!service.slaTemplate) continue

      const responseDeadline = new Date(service.createdAt)
      responseDeadline.setMinutes(responseDeadline.getMinutes() + service.slaTemplate.responseTime)

      const resolutionDeadline = new Date(service.createdAt)
      resolutionDeadline.setMinutes(resolutionDeadline.getMinutes() + service.slaTemplate.resolutionTime)

      // 检查是否接近超时（提前30分钟预警）
      const warningTime = 30 * 60 * 1000 // 30分钟
      
      if (now.getTime() + warningTime >= responseDeadline.getTime() && !service.firstResponseAt) {
        alerts.push({
          type: 'response',
          service,
          deadline: responseDeadline
        })
      }

      if (now.getTime() + warningTime >= resolutionDeadline.getTime() && service.status !== 'RESOLVED') {
        alerts.push({
          type: 'resolution',
          service,
          deadline: resolutionDeadline
        })
      }
    }

    // 发送预警通知
    for (const alert of alerts) {
      if (alert.service.assignedUser?.email) {
        await emailService.sendEmail({
          to: alert.service.assignedUser.email,
          subject: `SLA预警 - 工单 ${alert.service.ticketNumber}`,
          html: `
            <h3>⚠️ SLA超时预警</h3>
            <p><strong>工单号:</strong> ${alert.service.ticketNumber}</p>
            <p><strong>客户:</strong> ${alert.service.customer.name}</p>
            <p><strong>类型:</strong> ${alert.type === 'response' ? '响应时间' : '解决时间'}</p>
            <p><strong>截止时间:</strong> ${alert.deadline.toLocaleString()}</p>
            <p>请及时处理此工单以避免SLA违规。</p>
          `
        })
      }
    }

    if (alerts.length > 0) {
      console.log(`📢 发送了 ${alerts.length} 条SLA预警通知`)
    }
  }

  /**
   * 逾期工单提醒
   */
  private static async notifyOverdueTickets() {
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000)
    
    const overdueServices = await prisma.service.findMany({
      where: {
        status: {
          in: ['OPEN', 'IN_PROGRESS', 'PENDING']
        },
        createdAt: {
          lt: yesterday
        }
      },
      include: {
        customer: true,
        assignedUser: true
      }
    })

    // 发送提醒通知
    for (const service of overdueServices) {
      if (service.assignedUser?.email) {
        await emailService.sendEmail({
          to: service.assignedUser.email,
          subject: `逾期工单提醒 - ${service.ticketNumber}`,
          html: `
            <h3>⏰ 工单逾期提醒</h3>
            <p>您有一个工单已逾期，请及时处理：</p>
            <p><strong>工单号:</strong> ${service.ticketNumber}</p>
            <p><strong>客户:</strong> ${service.customer.name}</p>
            <p><strong>创建时间:</strong> ${service.createdAt.toLocaleString()}</p>
            <p><strong>当前状态:</strong> ${service.status}</p>
          `
        })
      }
    }

    if (overdueServices.length > 0) {
      console.log(`📬 发送了 ${overdueServices.length} 条逾期工单提醒`)
    }
  }

  /**
   * 清理过期文件
   */
  private static async cleanupOldFiles() {
    const result = await UploadService.cleanupOldFiles(30)
    console.log('🧹 文件清理完成:', result)
  }

  /**
   * 系统健康检查
   */
  private static async systemHealthCheck() {
    const checks = []

    try {
      // 数据库连接检查
      await prisma.$queryRaw`SELECT 1`
      checks.push({ name: 'Database', status: 'healthy' })
    } catch (error) {
      checks.push({ name: 'Database', status: 'error', error: error.message })
    }

    try {
      // 邮件服务检查
      const isConfigured = emailService.isConfigured()
      checks.push({ name: 'Email Service', status: isConfigured ? 'healthy' : 'not-configured' })
    } catch (error) {
      checks.push({ name: 'Email Service', status: 'error', error: error.message })
    }

    try {
      // 短信服务检查
      const isConfigured = smsService.isConfigured()
      checks.push({ name: 'SMS Service', status: isConfigured ? 'healthy' : 'not-configured' })
    } catch (error) {
      checks.push({ name: 'SMS Service', status: 'error', error: error.message })
    }

    // 检查是否有错误
    const errors = checks.filter(check => check.status === 'error')
    if (errors.length > 0) {
      console.warn('⚠️  系统健康检查发现问题:', errors)
      
      // 发送警告邮件给管理员
      const admins = await prisma.user.findMany({
        where: { role: { name: 'admin' } },
        select: { email: true }
      })

      for (const admin of admins) {
        if (admin.email) {
          await emailService.sendEmail({
            to: admin.email,
            subject: '系统健康检查警告',
            html: `
              <h3>⚠️ 系统健康检查发现问题</h3>
              <ul>
                ${errors.map(err => `<li><strong>${err.name}:</strong> ${err.error}</li>`).join('')}
              </ul>
              <p>请及时检查和修复相关问题。</p>
            `
          })
        }
      }
    }
  }

  /**
   * 数据备份提醒
   */
  private static async sendBackupReminder() {
    const admins = await prisma.user.findMany({
      where: { role: { name: 'admin' } },
      select: { email: true }
    })

    for (const admin of admins) {
      if (admin.email) {
        await emailService.sendEmail({
          to: admin.email,
          subject: '数据备份提醒',
          html: `
            <h3>💾 周末数据备份提醒</h3>
            <p>请记得在本周末进行数据备份操作，确保数据安全。</p>
            <p><strong>建议备份内容：</strong></p>
            <ul>
              <li>数据库完整备份</li>
              <li>上传文件备份</li>
              <li>配置文件备份</li>
              <li>日志文件归档</li>
            </ul>
          `
        })
      }
    }
  }
}