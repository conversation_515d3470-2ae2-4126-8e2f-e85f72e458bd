import { Router } from 'express'
import {
  sendEmail,
  sendTemplateEmail,
  sendSms,
  sendTemplateSms,
  sendBulkSms,
  testEmailConnection,
  getNotificationStatus
} from '@/controllers/notification.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     SendEmailRequest:
 *       type: object
 *       required:
 *         - to
 *         - subject
 *       properties:
 *         to:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 收件人邮箱
 *         subject:
 *           type: string
 *           minLength: 1
 *           description: 邮件主题
 *         html:
 *           type: string
 *           description: HTML内容
 *         text:
 *           type: string
 *           description: 纯文本内容
 *         cc:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 抄送邮箱
 *         bcc:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 密送邮箱
 *     SendTemplateEmailRequest:
 *       type: object
 *       required:
 *         - to
 *         - templateName
 *         - variables
 *       properties:
 *         to:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 收件人邮箱
 *         templateName:
 *           type: string
 *           minLength: 1
 *           description: 模板名称
 *         variables:
 *           type: object
 *           description: 模板变量
 *         cc:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 抄送邮箱
 *         bcc:
 *           oneOf:
 *             - type: string
 *               format: email
 *             - type: array
 *               items:
 *                 type: string
 *                 format: email
 *           description: 密送邮箱
 *     SendSmsRequest:
 *       type: object
 *       required:
 *         - phoneNumber
 *         - message
 *       properties:
 *         phoneNumber:
 *           type: string
 *           pattern: '^1[3-9]\d{9}$'
 *           description: 手机号码
 *         message:
 *           type: string
 *           minLength: 1
 *           maxLength: 500
 *           description: 短信内容
 *     SendTemplateSmsRequest:
 *       type: object
 *       required:
 *         - phoneNumber
 *         - templateName
 *         - variables
 *       properties:
 *         phoneNumber:
 *           type: string
 *           pattern: '^1[3-9]\d{9}$'
 *           description: 手机号码
 *         templateName:
 *           type: string
 *           minLength: 1
 *           description: 模板名称
 *         variables:
 *           type: object
 *           additionalProperties:
 *             type: string
 *           description: 模板变量
 *     SendBulkSmsRequest:
 *       type: object
 *       required:
 *         - phoneNumbers
 *         - message
 *       properties:
 *         phoneNumbers:
 *           type: array
 *           items:
 *             type: string
 *             pattern: '^1[3-9]\d{9}$'
 *           description: 手机号码列表
 *         message:
 *           type: string
 *           minLength: 1
 *           maxLength: 500
 *           description: 短信内容
 */

/**
 * @swagger
 * /api/v1/notifications/email:
 *   post:
 *     tags: [通知服务]
 *     summary: 发送邮件
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendEmailRequest'
 *     responses:
 *       200:
 *         description: 发送成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 请求参数错误
 *       503:
 *         description: 邮件服务未配置
 */
router.post('/email', authenticateToken, requirePermissions('notification:send'), sendEmail)

/**
 * @swagger
 * /api/v1/notifications/email/template:
 *   post:
 *     tags: [通知服务]
 *     summary: 发送模板邮件
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendTemplateEmailRequest'
 *     responses:
 *       200:
 *         description: 发送成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 请求参数错误
 *       503:
 *         description: 邮件服务未配置
 */
router.post('/email/template', authenticateToken, requirePermissions('notification:send'), sendTemplateEmail)

/**
 * @swagger
 * /api/v1/notifications/sms:
 *   post:
 *     tags: [通知服务]
 *     summary: 发送短信
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendSmsRequest'
 *     responses:
 *       200:
 *         description: 发送成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 请求参数错误
 *       503:
 *         description: 短信服务未配置
 */
router.post('/sms', authenticateToken, requirePermissions('notification:send'), sendSms)

/**
 * @swagger
 * /api/v1/notifications/sms/template:
 *   post:
 *     tags: [通知服务]
 *     summary: 发送模板短信
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendTemplateSmsRequest'
 *     responses:
 *       200:
 *         description: 发送成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 请求参数错误
 *       503:
 *         description: 短信服务未配置
 */
router.post('/sms/template', authenticateToken, requirePermissions('notification:send'), sendTemplateSms)

/**
 * @swagger
 * /api/v1/notifications/sms/bulk:
 *   post:
 *     tags: [通知服务]
 *     summary: 批量发送短信
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendBulkSmsRequest'
 *     responses:
 *       200:
 *         description: 发送完成
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         success:
 *                           type: number
 *                           description: 成功发送数量
 *                         failed:
 *                           type: number
 *                           description: 失败发送数量
 *                         total:
 *                           type: number
 *                           description: 总发送数量
 *       400:
 *         description: 请求参数错误
 *       503:
 *         description: 短信服务未配置
 */
router.post('/sms/bulk', authenticateToken, requirePermissions('notification:send'), sendBulkSms)

/**
 * @swagger
 * /api/v1/notifications/email/test:
 *   post:
 *     tags: [通知服务]
 *     summary: 测试邮件连接
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 连接测试成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       500:
 *         description: 连接测试失败
 *       503:
 *         description: 邮件服务未配置
 */
router.post('/email/test', authenticateToken, requirePermissions('notification:manage'), testEmailConnection)

/**
 * @swagger
 * /api/v1/notifications/status:
 *   get:
 *     tags: [通知服务]
 *     summary: 获取通知服务状态
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         email:
 *                           type: object
 *                           properties:
 *                             configured:
 *                               type: boolean
 *                               description: 是否已配置
 *                             status:
 *                               type: string
 *                               enum: [active, inactive]
 *                               description: 服务状态
 *                         sms:
 *                           type: object
 *                           properties:
 *                             configured:
 *                               type: boolean
 *                               description: 是否已配置
 *                             status:
 *                               type: string
 *                               enum: [active, inactive]
 *                               description: 服务状态
 */
router.get('/status', authenticateToken, requirePermissions('notification:read'), getNotificationStatus)

export default router
