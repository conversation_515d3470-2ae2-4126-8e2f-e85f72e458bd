import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import { UploadService } from '@/services/upload.service'
import {
  uploadServiceAttachments,
  getServiceAttachments,
  downloadAttachment,
  deleteAttachment,
  getStorageStats,
  cleanupOldFiles
} from '@/controllers/upload.controller'

const router = Router()

// 配置不同类型的上传中间件
const serviceAttachmentUpload = UploadService.createUploadMiddleware({
  destination: 'uploads/service-attachments/',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 5
})

/**
 * @swagger
 * components:
 *   schemas:
 *     Attachment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 附件ID
 *         filename:
 *           type: string
 *           description: 存储文件名
 *         originalName:
 *           type: string
 *           description: 原始文件名
 *         fileSize:
 *           type: integer
 *           description: 文件大小（字节）
 *         mimeType:
 *           type: string
 *           description: 文件MIME类型
 *         uploadedAt:
 *           type: string
 *           format: date-time
 *           description: 上传时间
 *         formattedSize:
 *           type: string
 *           description: 格式化的文件大小
 *         uploader:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *             username:
 *               type: string
 *             fullName:
 *               type: string
 *       example:
 *         id: "clp123456789"
 *         filename: "screenshot_20240115_103045_abc123.png"
 *         originalName: "screenshot.png"
 *         fileSize: 524288
 *         mimeType: "image/png"
 *         uploadedAt: "2024-01-15T10:30:45Z"
 *         formattedSize: "512 KB"
 *         uploader:
 *           id: "clp987654321"
 *           username: "engineer1"
 *           fullName: "工程师1"
 */

/**
 * @swagger
 * /api/v1/upload/service/{serviceId}/attachments:
 *   post:
 *     summary: 上传服务工单附件
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: 要上传的文件（最多5个，每个最大10MB）
 *     responses:
 *       201:
 *         description: 上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     attachments:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Attachment'
 *       400:
 *         description: 请求错误
 *       401:
 *         description: 未认证
 *       500:
 *         description: 服务器错误
 */
router.post(
  '/service/:serviceId/attachments',
  authMiddleware,
  requirePermissions(['service:write']),
  serviceAttachmentUpload.array('files', 5),
  uploadServiceAttachments
)

/**
 * @swagger
 * /api/v1/upload/service/{serviceId}/attachments:
 *   get:
 *     summary: 获取服务工单附件列表
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     attachments:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Attachment'
 */
router.get(
  '/service/:serviceId/attachments',
  authMiddleware,
  requirePermissions(['service:read']),
  getServiceAttachments
)

/**
 * @swagger
 * /api/v1/upload/attachments/{attachmentId}/download:
 *   get:
 *     summary: 下载附件
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: attachmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: 附件ID
 *     responses:
 *       200:
 *         description: 文件内容
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *       404:
 *         description: 附件不存在
 *       500:
 *         description: 下载失败
 */
router.get(
  '/attachments/:attachmentId/download',
  authMiddleware,
  requirePermissions(['service:read']),
  downloadAttachment
)

/**
 * @swagger
 * /api/v1/upload/attachments/{attachmentId}:
 *   delete:
 *     summary: 删除附件
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: attachmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: 附件ID
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       403:
 *         description: 无权限删除
 *       404:
 *         description: 附件不存在
 */
router.delete(
  '/attachments/:attachmentId',
  authMiddleware,
  requirePermissions(['service:write']),
  deleteAttachment
)

/**
 * @swagger
 * /api/v1/upload/stats:
 *   get:
 *     summary: 获取存储统计信息（管理员）
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalAttachments:
 *                       type: integer
 *                       description: 总附件数
 *                     totalSize:
 *                       type: integer
 *                       description: 总存储大小（字节）
 *                     formattedTotalSize:
 *                       type: string
 *                       description: 格式化的总大小
 *                     recentAttachments:
 *                       type: integer
 *                       description: 最近7天上传的附件数
 *                     typeBreakdown:
 *                       type: array
 *                       description: 按文件类型统计
 *                       items:
 *                         type: object
 *                         properties:
 *                           mimeType:
 *                             type: string
 *                           count:
 *                             type: integer
 *                           totalSize:
 *                             type: integer
 *                           formattedSize:
 *                             type: string
 */
router.get(
  '/stats',
  authMiddleware,
  requirePermissions(['admin:all']),
  getStorageStats
)

/**
 * @swagger
 * /api/v1/upload/cleanup:
 *   post:
 *     summary: 清理过期文件（管理员）
 *     tags: [Upload]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: days
 *         schema:
 *           type: integer
 *           default: 30
 *           minimum: 7
 *         description: 清理多少天前的文件
 *     responses:
 *       200:
 *         description: 清理完成
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     deletedCount:
 *                       type: integer
 *                       description: 成功删除的文件数
 *                     errorCount:
 *                       type: integer
 *                       description: 删除失败的文件数
 *                     totalProcessed:
 *                       type: integer
 *                       description: 总处理文件数
 */
router.post(
  '/cleanup',
  authMiddleware,
  requirePermissions(['admin:all']),
  cleanupOldFiles
)

export default router