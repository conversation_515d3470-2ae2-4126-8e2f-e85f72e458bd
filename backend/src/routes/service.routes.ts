import { Router } from 'express'
import {
  getServices,
  getService,
  createService,
  updateService,
  deleteService,
  getServiceWorkLogs,
  createServiceWorkLog,
  getServiceComments,
  createServiceComment,
  getServiceStats
} from '@/controllers/service.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Service:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 服务工单ID
 *         archiveId:
 *           type: string
 *           description: 项目档案ID
 *         slaTemplateId:
 *           type: string
 *           description: SLA模板ID
 *         ticketNumber:
 *           type: string
 *           description: 工单号
 *         title:
 *           type: string
 *           description: 工单标题
 *         description:
 *           type: string
 *           description: 工单描述
 *         category:
 *           type: string
 *           enum: [MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING]
 *           description: 服务类别
 *         priority:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *           description: 优先级
 *         status:
 *           type: string
 *           enum: [PENDING, IN_PROGRESS, WAITING_CUSTOMER, RESOLVED, CLOSED]
 *           description: 工单状态
 *         assignedTo:
 *           type: string
 *           description: 分配给用户ID
 *         customerContact:
 *           type: string
 *           description: 客户联系人
 *         startTime:
 *           type: string
 *           format: date-time
 *           description: 开始时间
 *         endTime:
 *           type: string
 *           format: date-time
 *           description: 结束时间
 *         estimatedHours:
 *           type: number
 *           description: 预估工时
 *         actualHours:
 *           type: number
 *           description: 实际工时
 *         resolution:
 *           type: string
 *           description: 解决方案
 *         customerFeedback:
 *           type: string
 *           description: 客户反馈
 *         satisfaction:
 *           type: integer
 *           minimum: 1
 *           maximum: 5
 *           description: 客户满意度
 *         createdBy:
 *           type: string
 *           description: 创建者ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 * 
 *     ServiceWorkLog:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 工作日志ID
 *         serviceId:
 *           type: string
 *           description: 服务工单ID
 *         userId:
 *           type: string
 *           description: 用户ID
 *         description:
 *           type: string
 *           description: 工作描述
 *         workHours:
 *           type: number
 *           description: 工作时长
 *         workDate:
 *           type: string
 *           format: date-time
 *           description: 工作日期
 *         category:
 *           type: string
 *           enum: [ANALYSIS, IMPLEMENTATION, TESTING, DOCUMENTATION, COMMUNICATION, MAINTENANCE, SUPPORT, OTHER]
 *           description: 工作类别
 *         createdAt:
 *           type: string
 *           format: date-time
 * 
 *     ServiceComment:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 评论ID
 *         serviceId:
 *           type: string
 *           description: 服务工单ID
 *         content:
 *           type: string
 *           description: 评论内容
 *         isInternal:
 *           type: boolean
 *           description: 是否内部评论
 *         authorId:
 *           type: string
 *           description: 作者ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 * 
 *     CreateServiceRequest:
 *       type: object
 *       properties:
 *         archiveId:
 *           type: string
 *           description: 项目档案ID
 *         slaTemplateId:
 *           type: string
 *           description: SLA模板ID
 *         title:
 *           type: string
 *           description: 工单标题
 *         description:
 *           type: string
 *           description: 工单描述
 *         category:
 *           type: string
 *           enum: [MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING]
 *           description: 服务类别
 *         priority:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *           description: 优先级
 *         customerContact:
 *           type: string
 *           description: 客户联系人
 *         estimatedHours:
 *           type: number
 *           description: 预估工时
 *       required:
 *         - archiveId
 *         - title
 *         - description
 * 
 *     CreateWorkLogRequest:
 *       type: object
 *       properties:
 *         description:
 *           type: string
 *           description: 工作描述
 *         workHours:
 *           type: number
 *           description: 工作时长
 *         workDate:
 *           type: string
 *           format: date
 *           description: 工作日期
 *         category:
 *           type: string
 *           enum: [ANALYSIS, IMPLEMENTATION, TESTING, DOCUMENTATION, COMMUNICATION, MAINTENANCE, SUPPORT, OTHER]
 *           description: 工作类别
 *       required:
 *         - description
 *         - workHours
 *         - workDate
 * 
 *     CreateCommentRequest:
 *       type: object
 *       properties:
 *         content:
 *           type: string
 *           description: 评论内容
 *         isInternal:
 *           type: boolean
 *           description: 是否内部评论
 *       required:
 *         - content
 */

/**
 * @swagger
 * /api/v1/services:
 *   get:
 *     tags: [服务工单]
 *     summary: 获取服务工单列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, IN_PROGRESS, WAITING_CUSTOMER, RESOLVED, CLOSED]
 *         description: 状态过滤
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *           enum: [MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING]
 *         description: 类别过滤
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [LOW, MEDIUM, HIGH, URGENT]
 *         description: 优先级过滤
 *       - in: query
 *         name: assignedTo
 *         schema:
 *           type: string
 *         description: 分配给用户ID过滤
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         description: 客户ID过滤
 *       - in: query
 *         name: archiveId
 *         schema:
 *           type: string
 *         description: 项目档案ID过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     services:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Service'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 */
router.get('/', authenticateToken, requirePermissions('service:read'), getServices)

/**
 * @swagger
 * /api/v1/services/stats:
 *   get:
 *     tags: [服务工单]
 *     summary: 获取服务工单统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalServices:
 *                       type: integer
 *                     recentServices:
 *                       type: integer
 *                     avgResolutionTime:
 *                       type: number
 *                     statusDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           status:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     categoryDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           category:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     priorityDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           priority:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authenticateToken, requirePermissions('service:read'), getServiceStats)

/**
 * @swagger
 * /api/v1/services/{id}:
 *   get:
 *     tags: [服务工单]
 *     summary: 获取服务工单详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *       404:
 *         description: 服务工单不存在
 */
router.get('/:id', authenticateToken, requirePermissions('service:read'), getService)

/**
 * @swagger
 * /api/v1/services:
 *   post:
 *     tags: [服务工单]
 *     summary: 创建服务工单
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateServiceRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Service'
 *       400:
 *         description: 请求参数错误或项目档案不存在
 */
router.post('/', authenticateToken, requirePermissions('service:write'), createService)

/**
 * @swagger
 * /api/v1/services/{id}:
 *   put:
 *     tags: [服务工单]
 *     summary: 更新服务工单
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *               description:
 *                 type: string
 *               category:
 *                 type: string
 *                 enum: [MAINTENANCE, SUPPORT, UPGRADE, BUGFIX, CONSULTING, MONITORING]
 *               priority:
 *                 type: string
 *                 enum: [LOW, MEDIUM, HIGH, URGENT]
 *               status:
 *                 type: string
 *                 enum: [PENDING, IN_PROGRESS, WAITING_CUSTOMER, RESOLVED, CLOSED]
 *               assignedTo:
 *                 type: string
 *               customerContact:
 *                 type: string
 *               estimatedHours:
 *                 type: number
 *               actualHours:
 *                 type: number
 *               resolution:
 *                 type: string
 *               customerFeedback:
 *                 type: string
 *               satisfaction:
 *                 type: integer
 *                 minimum: 1
 *                 maximum: 5
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 服务工单不存在
 *       400:
 *         description: 请求参数错误
 */
router.put('/:id', authenticateToken, requirePermissions('service:write'), updateService)

/**
 * @swagger
 * /api/v1/services/{id}:
 *   delete:
 *     tags: [服务工单]
 *     summary: 删除服务工单
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 服务工单不存在
 *       400:
 *         description: 只有待处理状态的工单才能删除
 */
router.delete('/:id', authenticateToken, requirePermissions('service:write'), deleteService)

/**
 * @swagger
 * /api/v1/services/{serviceId}/work-logs:
 *   get:
 *     tags: [服务工单]
 *     summary: 获取服务工单工作日志
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ServiceWorkLog'
 */
router.get('/:serviceId/work-logs', authenticateToken, requirePermissions('service:read'), getServiceWorkLogs)

/**
 * @swagger
 * /api/v1/services/{serviceId}/work-logs:
 *   post:
 *     tags: [服务工单]
 *     summary: 创建服务工单工作日志
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateWorkLogRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *       404:
 *         description: 服务工单不存在
 */
router.post('/:serviceId/work-logs', authenticateToken, requirePermissions('service:write'), createServiceWorkLog)

/**
 * @swagger
 * /api/v1/services/{serviceId}/comments:
 *   get:
 *     tags: [服务工单]
 *     summary: 获取服务工单评论
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *       - in: query
 *         name: isInternal
 *         schema:
 *           type: boolean
 *         description: 是否内部评论过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ServiceComment'
 */
router.get('/:serviceId/comments', authenticateToken, requirePermissions('service:read'), getServiceComments)

/**
 * @swagger
 * /api/v1/services/{serviceId}/comments:
 *   post:
 *     tags: [服务工单]
 *     summary: 创建服务工单评论
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: serviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: 服务工单ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCommentRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *       404:
 *         description: 服务工单不存在
 */
router.post('/:serviceId/comments', authenticateToken, requirePermissions('service:write'), createServiceComment)

export default router