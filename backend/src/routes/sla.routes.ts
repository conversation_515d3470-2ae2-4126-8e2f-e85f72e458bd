import { Router } from 'express'
import {
  getSlaTemplates,
  getSlaTemplate,
  createSlaTemplate,
  updateSlaTemplate,
  deleteSlaTemplate,
  getSlaStats,
  getSlaPerformanceReport
} from '@/controllers/sla.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     SlaTemplate:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: SLA模板ID
 *         name:
 *           type: string
 *           description: SLA模板名称
 *         description:
 *           type: string
 *           description: SLA模板描述
 *         responseTime:
 *           type: number
 *           description: 响应时间(分钟)
 *         resolutionTime:
 *           type: number
 *           description: 解决时间(小时)
 *         availability:
 *           type: number
 *           description: 可用性百分比
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         _count:
 *           type: object
 *           properties:
 *             services:
 *               type: number
 *               description: 关联的服务工单数量
 *     CreateSlaTemplateRequest:
 *       type: object
 *       required:
 *         - name
 *         - responseTime
 *         - resolutionTime
 *       properties:
 *         name:
 *           type: string
 *           minLength: 1
 *           maxLength: 100
 *           description: SLA模板名称
 *         description:
 *           type: string
 *           description: SLA模板描述
 *         responseTime:
 *           type: number
 *           minimum: 1
 *           description: 响应时间(分钟)
 *         resolutionTime:
 *           type: number
 *           minimum: 1
 *           description: 解决时间(小时)
 *         availability:
 *           type: number
 *           minimum: 0
 *           maximum: 100
 *           default: 99.9
 *           description: 可用性百分比
 */

/**
 * @swagger
 * /api/v1/sla/templates:
 *   get:
 *     tags: [SLA管理]
 *     summary: 获取SLA模板列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         items:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/SlaTemplate'
 */
router.get('/templates', authenticateToken, requirePermissions('sla:read'), getSlaTemplates)

/**
 * @swagger
 * /api/v1/sla/templates/{id}:
 *   get:
 *     tags: [SLA管理]
 *     summary: 获取SLA模板详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: SLA模板ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/SlaTemplate'
 *       404:
 *         description: SLA模板不存在
 */
router.get('/templates/:id', authenticateToken, requirePermissions('sla:read'), getSlaTemplate)

/**
 * @swagger
 * /api/v1/sla/templates:
 *   post:
 *     tags: [SLA管理]
 *     summary: 创建SLA模板
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateSlaTemplateRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/SlaTemplate'
 *       400:
 *         description: 请求参数错误或SLA模板名称已存在
 */
router.post('/templates', authenticateToken, requirePermissions('sla:write'), createSlaTemplate)

/**
 * @swagger
 * /api/v1/sla/templates/{id}:
 *   put:
 *     tags: [SLA管理]
 *     summary: 更新SLA模板
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: SLA模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateSlaTemplateRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/SlaTemplate'
 *       404:
 *         description: SLA模板不存在
 *       400:
 *         description: 请求参数错误或SLA模板名称已存在
 */
router.put('/templates/:id', authenticateToken, requirePermissions('sla:write'), updateSlaTemplate)

/**
 * @swagger
 * /api/v1/sla/templates/{id}:
 *   delete:
 *     tags: [SLA管理]
 *     summary: 删除SLA模板
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: SLA模板ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: SLA模板不存在
 *       400:
 *         description: 该SLA模板已被服务工单使用，无法删除
 */
router.delete('/templates/:id', authenticateToken, requirePermissions('sla:write'), deleteSlaTemplate)

/**
 * @swagger
 * /api/v1/sla/stats:
 *   get:
 *     tags: [SLA管理]
 *     summary: 获取SLA统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         overview:
 *                           type: object
 *                           properties:
 *                             totalTemplates:
 *                               type: number
 *                               description: 总SLA模板数
 *                             totalServices:
 *                               type: number
 *                               description: 总服务工单数
 *                             avgResponseCompliance:
 *                               type: number
 *                               description: 平均响应时间达成率
 *                             avgResolutionCompliance:
 *                               type: number
 *                               description: 平均解决时间达成率
 *                         templatePerformance:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               templateId:
 *                                 type: string
 *                               templateName:
 *                                 type: string
 *                               totalServices:
 *                                 type: number
 *                               completedServices:
 *                                 type: number
 *                               responseTimeCompliance:
 *                                 type: number
 *                               resolutionTimeCompliance:
 *                                 type: number
 *                               targetAvailability:
 *                                 type: number
 */
router.get('/stats', authenticateToken, requirePermissions('sla:read'), getSlaStats)

/**
 * @swagger
 * /api/v1/sla/templates/{id}/performance:
 *   get:
 *     tags: [SLA管理]
 *     summary: 获取SLA模板的详细性能报告
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: SLA模板ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: SLA模板不存在
 */
router.get('/templates/:id/performance', authenticateToken, requirePermissions('sla:read'), getSlaPerformanceReport)

export default router
