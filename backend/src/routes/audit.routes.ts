import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getAuditLogs,
  getAuditStats,
  getMyAuditLogs,
  exportAuditLogs
} from '@/controllers/audit.controller'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     AuditLog:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 日志ID
 *         userId:
 *           type: string
 *           description: 用户ID
 *         action:
 *           type: string
 *           enum: [CREATE, UPDATE, DELETE, VIEW, LOGIN, LOGOUT, STATUS_CHANGE]
 *           description: 操作类型
 *         resource:
 *           type: string
 *           enum: [AUTH, CUSTOMER, ARCHIVE, SERVICE, CONFIGURATION, SLA]
 *           description: 资源类型
 *         resourceId:
 *           type: string
 *           description: 资源ID
 *         details:
 *           type: object
 *           description: 操作详情
 *         ipAddress:
 *           type: string
 *           description: IP地址
 *         userAgent:
 *           type: string
 *           description: 用户代理
 *         timestamp:
 *           type: string
 *           format: date-time
 *           description: 操作时间
 *         user:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *             username:
 *               type: string
 *             fullName:
 *               type: string
 *       example:
 *         id: "clp123456789"
 *         userId: "clp987654321"
 *         action: "CREATE"
 *         resource: "CUSTOMER"
 *         resourceId: "clp555666777"
 *         details: {"name": "新客户", "company": "测试公司"}
 *         ipAddress: "*************"
 *         timestamp: "2024-01-15T10:30:00Z"
 *         user:
 *           id: "clp987654321"
 *           username: "admin"
 *           fullName: "系统管理员"
 */

/**
 * @swagger
 * /api/v1/audit/logs:
 *   get:
 *     summary: 获取操作日志列表（管理员）
 *     tags: [Audit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: 每页数量
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: 用户ID筛选
 *       - in: query
 *         name: resource
 *         schema:
 *           type: string
 *           enum: [AUTH, CUSTOMER, ARCHIVE, SERVICE, CONFIGURATION, SLA]
 *         description: 资源类型筛选
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *           enum: [CREATE, UPDATE, DELETE, VIEW, LOGIN, LOGOUT, STATUS_CHANGE]
 *         description: 操作类型筛选
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AuditLog'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/logs', authMiddleware, requirePermissions(['admin:all']), getAuditLogs)

/**
 * @swagger
 * /api/v1/audit/stats:
 *   get:
 *     summary: 获取操作统计信息（管理员）
 *     tags: [Audit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: 用户ID筛选
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalOperations:
 *                       type: integer
 *                       description: 总操作数
 *                     resourceStats:
 *                       type: object
 *                       description: 按资源类型统计
 *                     actionBreakdown:
 *                       type: array
 *                       description: 操作详细分解
 *                     activeUsers:
 *                       type: array
 *                       description: 活跃用户列表
 */
router.get('/stats', authMiddleware, requirePermissions(['admin:all']), getAuditStats)

/**
 * @swagger
 * /api/v1/audit/my-logs:
 *   get:
 *     summary: 获取当前用户的操作日志
 *     tags: [Audit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     logs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AuditLog'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/my-logs', authMiddleware, getMyAuditLogs)

/**
 * @swagger
 * /api/v1/audit/export:
 *   get:
 *     summary: 导出操作日志（管理员）
 *     tags: [Audit]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: 用户ID筛选
 *       - in: query
 *         name: resource
 *         schema:
 *           type: string
 *           enum: [AUTH, CUSTOMER, ARCHIVE, SERVICE, CONFIGURATION, SLA]
 *         description: 资源类型筛选
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *           enum: [CREATE, UPDATE, DELETE, VIEW, LOGIN, LOGOUT, STATUS_CHANGE]
 *         description: 操作类型筛选
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 开始日期
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: 结束日期
 *     responses:
 *       200:
 *         description: CSV文件
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 */
router.get('/export', authMiddleware, requirePermissions(['admin:all']), exportAuditLogs)

export default router