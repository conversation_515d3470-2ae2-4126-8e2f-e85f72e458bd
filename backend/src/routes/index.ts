import { Router } from 'express'
import authRoutes from './auth.routes'
import userRoutes from './user.routes'
import roleRoutes from './role.routes'
import customerRoutes from './customer.routes'
import archiveRoutes from './archive.routes'
import serviceRoutes from './service.routes'
import configurationRoutes from './configuration.routes'
import slaRoutes from './sla.routes'
import notificationRoutes from './notification.routes'
import auditRoutes from './audit.routes'
import uploadRoutes from './upload.routes'
import schedulerRoutes from './scheduler.routes'

const router = Router()

// API版本1路由
router.use('/v1/auth', authRoutes)
router.use('/v1/users', userRoutes)
router.use('/v1/roles', roleRoutes)
router.use('/v1/customers', customerRoutes)
router.use('/v1/archives', archiveRoutes)
router.use('/v1/services', serviceRoutes)
router.use('/v1/configurations', configurationRoutes)
router.use('/v1/sla', slaRoutes)
router.use('/v1/notifications', notificationRoutes)
router.use('/v1/audit', auditRoutes)
router.use('/v1/upload', uploadRoutes)
router.use('/v1/scheduler', schedulerRoutes)

export default router