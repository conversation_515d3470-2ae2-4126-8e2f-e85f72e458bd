import { Router } from 'express'
import {
  getArchives,
  getArchive,
  createArchive,
  updateArchive,
  deleteArchive,
  getArchiveStats,
  getCustomerArchives
} from '@/controllers/archive.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     ProjectArchive:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 项目档案ID
 *         customerId:
 *           type: string
 *           description: 客户ID
 *         name:
 *           type: string
 *           description: 项目名称
 *         description:
 *           type: string
 *           description: 项目描述
 *         technology:
 *           type: string
 *           description: 技术栈
 *         environment:
 *           type: string
 *           description: 部署环境
 *         version:
 *           type: string
 *           description: 当前版本
 *         deploymentDate:
 *           type: string
 *           format: date-time
 *           description: 部署日期
 *         status:
 *           type: string
 *           enum: [ACTIVE, MAINTENANCE, DEPRECATED, ARCHIVED]
 *           description: 档案状态
 *         createdBy:
 *           type: string
 *           description: 创建者ID
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 * 
 *     CreateArchiveRequest:
 *       type: object
 *       properties:
 *         customerId:
 *           type: string
 *           description: 客户ID
 *         name:
 *           type: string
 *           description: 项目名称
 *         description:
 *           type: string
 *           description: 项目描述
 *         technology:
 *           type: string
 *           description: 技术栈
 *         environment:
 *           type: string
 *           description: 部署环境
 *         version:
 *           type: string
 *           description: 当前版本
 *         deploymentDate:
 *           type: string
 *           format: date
 *           description: 部署日期
 *         status:
 *           type: string
 *           enum: [ACTIVE, MAINTENANCE, DEPRECATED, ARCHIVED]
 *           description: 档案状态
 *       required:
 *         - customerId
 *         - name
 */

/**
 * @swagger
 * /api/v1/archives:
 *   get:
 *     tags: [项目档案]
 *     summary: 获取项目档案列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [ACTIVE, MAINTENANCE, DEPRECATED, ARCHIVED]
 *         description: 状态过滤
 *       - in: query
 *         name: customerId
 *         schema:
 *           type: string
 *         description: 客户ID过滤
 *       - in: query
 *         name: technology
 *         schema:
 *           type: string
 *         description: 技术栈过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     archives:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ProjectArchive'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 */
router.get('/', authenticateToken, requirePermissions('archive:read'), getArchives)

/**
 * @swagger
 * /api/v1/archives/stats:
 *   get:
 *     tags: [项目档案]
 *     summary: 获取项目档案统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalArchives:
 *                       type: integer
 *                     recentArchives:
 *                       type: integer
 *                     statusDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           status:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     topTechnologies:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           technology:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authenticateToken, requirePermissions('archive:read'), getArchiveStats)

/**
 * @swagger
 * /api/v1/archives/customer/{customerId}:
 *   get:
 *     tags: [项目档案]
 *     summary: 获取客户的项目档案列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [ACTIVE, MAINTENANCE, DEPRECATED, ARCHIVED]
 *         description: 状态过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ProjectArchive'
 *       404:
 *         description: 客户不存在
 */
router.get('/customer/:customerId', authenticateToken, requirePermissions('archive:read'), getCustomerArchives)

/**
 * @swagger
 * /api/v1/archives/{id}:
 *   get:
 *     tags: [项目档案]
 *     summary: 获取项目档案详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目档案ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ProjectArchive'
 *       404:
 *         description: 项目档案不存在
 */
router.get('/:id', authenticateToken, requirePermissions('archive:read'), getArchive)

/**
 * @swagger
 * /api/v1/archives:
 *   post:
 *     tags: [项目档案]
 *     summary: 创建项目档案
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateArchiveRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/ProjectArchive'
 *       400:
 *         description: 请求参数错误或项目名称已存在
 */
router.post('/', authenticateToken, requirePermissions('archive:write'), createArchive)

/**
 * @swagger
 * /api/v1/archives/{id}:
 *   put:
 *     tags: [项目档案]
 *     summary: 更新项目档案
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目档案ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateArchiveRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 项目档案不存在
 *       400:
 *         description: 请求参数错误
 */
router.put('/:id', authenticateToken, requirePermissions('archive:write'), updateArchive)

/**
 * @swagger
 * /api/v1/archives/{id}:
 *   delete:
 *     tags: [项目档案]
 *     summary: 删除项目档案
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目档案ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 项目档案不存在
 *       400:
 *         description: 项目档案下还有服务工单，无法删除
 */
router.delete('/:id', authenticateToken, requirePermissions('archive:write'), deleteArchive)

export default router