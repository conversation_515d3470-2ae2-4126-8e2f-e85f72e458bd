import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getRoles,
  getAllRoles,
  getRole,
  createRole,
  updateRole,
  deleteRole,
  getSystemPermissions,
  getPermissionGroups,
  getRoleTemplates,
  createRoleFromTemplate,
  duplicateRole,
  getRoleStats
} from '@/controllers/role.controller'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Role:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 角色ID
 *         name:
 *           type: string
 *           description: 角色名称
 *         description:
 *           type: string
 *           description: 角色描述
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *           description: 权限列表
 *         userCount:
 *           type: integer
 *           description: 使用该角色的用户数量
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *       example:
 *         id: "clp123456789"
 *         name: "admin"
 *         description: "系统管理员"
 *         permissions: ["admin:all"]
 *         userCount: 5
 *         createdAt: "2024-01-01T00:00:00Z"
 *         updatedAt: "2024-01-15T10:30:00Z"
 */

/**
 * @swagger
 * /api/v1/roles:
 *   get:
 *     summary: 获取角色列表
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     roles:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Role'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/', authMiddleware, requirePermissions('role:read'), getRoles)

/**
 * @swagger
 * /api/v1/roles/all:
 *   get:
 *     summary: 获取所有角色（用于下拉选择）
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                       name:
 *                         type: string
 *                       description:
 *                         type: string
 */
router.get('/all', authMiddleware, requirePermissions('role:read'), getAllRoles)

/**
 * @swagger
 * /api/v1/roles/permissions:
 *   get:
 *     summary: 获取系统权限列表
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       key:
 *                         type: string
 *                       description:
 *                         type: string
 *                       category:
 *                         type: string
 */
router.get('/permissions', authMiddleware, requirePermissions('role:read'), getSystemPermissions)

/**
 * @swagger
 * /api/v1/roles/permissions/groups:
 *   get:
 *     summary: 获取权限分组
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/permissions/groups', authMiddleware, requirePermissions('role:read'), getPermissionGroups)

/**
 * @swagger
 * /api/v1/roles/templates:
 *   get:
 *     summary: 获取角色模板
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/templates', authMiddleware, requirePermissions('role:read'), getRoleTemplates)

/**
 * @swagger
 * /api/v1/roles/stats:
 *   get:
 *     summary: 获取角色统计信息
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/stats', authMiddleware, requirePermissions('role:read'), getRoleStats)

/**
 * @swagger
 * /api/v1/roles/{id}:
 *   get:
 *     summary: 获取角色详情
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Role'
 */
router.get('/:id', authMiddleware, requirePermissions('role:read'), getRole)

/**
 * @swagger
 * /api/v1/roles:
 *   post:
 *     summary: 创建角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - permissions
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               description:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 200
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *                 minItems: 1
 *     responses:
 *       201:
 *         description: 创建成功
 */
router.post('/', authMiddleware, requirePermissions('role:write'), createRole)

/**
 * @swagger
 * /api/v1/roles/from-template:
 *   post:
 *     summary: 从模板创建角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - templateName
 *             properties:
 *               templateName:
 *                 type: string
 *                 enum: [super-admin, admin, manager, engineer, support, viewer]
 *               customName:
 *                 type: string
 *               customDescription:
 *                 type: string
 *     responses:
 *       201:
 *         description: 创建成功
 */
router.post('/from-template', authMiddleware, requirePermissions('role:write'), createRoleFromTemplate)

/**
 * @swagger
 * /api/v1/roles/{id}/duplicate:
 *   post:
 *     summary: 复制角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newName
 *             properties:
 *               newName:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *               newDescription:
 *                 type: string
 *     responses:
 *       201:
 *         description: 复制成功
 */
router.post('/:id/duplicate', authMiddleware, requirePermissions('role:write'), duplicateRole)

/**
 * @swagger
 * /api/v1/roles/{id}:
 *   put:
 *     summary: 更新角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               permissions:
 *                 type: array
 *                 items:
 *                   type: string
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/:id', authMiddleware, requirePermissions('role:write'), updateRole)

/**
 * @swagger
 * /api/v1/roles/{id}:
 *   delete:
 *     summary: 删除角色
 *     tags: [Roles]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:id', authMiddleware, requirePermissions('role:delete'), deleteRole)

export default router