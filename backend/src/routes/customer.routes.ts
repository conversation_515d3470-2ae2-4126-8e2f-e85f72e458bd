import { Router } from 'express'
import {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  getCustomerContacts,
  createCustomerContact,
  updateCustomerContact,
  deleteCustomerContact,
  getCustomerStats
} from '@/controllers/customer.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     Customer:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 客户ID
 *         name:
 *           type: string
 *           description: 客户名称
 *         company:
 *           type: string
 *           description: 公司名称
 *         industry:
 *           type: string
 *           description: 行业
 *         level:
 *           type: string
 *           enum: [BASIC, STANDARD, PREMIUM, ENTERPRISE]
 *           description: 客户等级
 *         contactPerson:
 *           type: string
 *           description: 联系人
 *         email:
 *           type: string
 *           description: 邮箱
 *         phone:
 *           type: string
 *           description: 电话
 *         address:
 *           type: string
 *           description: 地址
 *         description:
 *           type: string
 *           description: 描述
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 * 
 *     CustomerContact:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 联系人ID
 *         customerId:
 *           type: string
 *           description: 客户ID
 *         name:
 *           type: string
 *           description: 姓名
 *         position:
 *           type: string
 *           description: 职位
 *         email:
 *           type: string
 *           description: 邮箱
 *         phone:
 *           type: string
 *           description: 电话
 *         isPrimary:
 *           type: boolean
 *           description: 是否主要联系人
 *         createdAt:
 *           type: string
 *           format: date-time
 * 
 *     CreateCustomerRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 客户名称
 *         company:
 *           type: string
 *           description: 公司名称
 *         industry:
 *           type: string
 *           description: 行业
 *         level:
 *           type: string
 *           enum: [BASIC, STANDARD, PREMIUM, ENTERPRISE]
 *           description: 客户等级
 *         contactPerson:
 *           type: string
 *           description: 联系人
 *         email:
 *           type: string
 *           description: 邮箱
 *         phone:
 *           type: string
 *           description: 电话
 *         address:
 *           type: string
 *           description: 地址
 *         description:
 *           type: string
 *           description: 描述
 *       required:
 *         - name
 * 
 *     CreateContactRequest:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 姓名
 *         position:
 *           type: string
 *           description: 职位
 *         email:
 *           type: string
 *           description: 邮箱
 *         phone:
 *           type: string
 *           description: 电话
 *         isPrimary:
 *           type: boolean
 *           description: 是否主要联系人
 *       required:
 *         - name
 */

/**
 * @swagger
 * /api/v1/customers:
 *   get:
 *     tags: [客户管理]
 *     summary: 获取客户列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: level
 *         schema:
 *           type: string
 *           enum: [BASIC, STANDARD, PREMIUM, ENTERPRISE]
 *         description: 客户等级过滤
 *       - in: query
 *         name: industry
 *         schema:
 *           type: string
 *         description: 行业过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     customers:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Customer'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 */
router.get('/', authenticateToken, requirePermissions('customer:read'), getCustomers)

/**
 * @swagger
 * /api/v1/customers/stats:
 *   get:
 *     tags: [客户管理]
 *     summary: 获取客户统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalCustomers:
 *                       type: integer
 *                     recentCustomers:
 *                       type: integer
 *                     levelDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           level:
 *                             type: string
 *                           count:
 *                             type: integer
 *                     topIndustries:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           industry:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authenticateToken, requirePermissions('customer:read'), getCustomerStats)

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   get:
 *     tags: [客户管理]
 *     summary: 获取客户详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *       404:
 *         description: 客户不存在
 */
router.get('/:id', authenticateToken, requirePermissions('customer:read'), getCustomer)

/**
 * @swagger
 * /api/v1/customers:
 *   post:
 *     tags: [客户管理]
 *     summary: 创建客户
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/Customer'
 *       400:
 *         description: 请求参数错误或客户名称已存在
 */
router.post('/', authenticateToken, requirePermissions('customer:write'), createCustomer)

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   put:
 *     tags: [客户管理]
 *     summary: 更新客户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCustomerRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 客户不存在
 *       400:
 *         description: 请求参数错误
 */
router.put('/:id', authenticateToken, requirePermissions('customer:write'), updateCustomer)

/**
 * @swagger
 * /api/v1/customers/{id}:
 *   delete:
 *     tags: [客户管理]
 *     summary: 删除客户
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 客户不存在
 *       400:
 *         description: 客户下还有项目档案，无法删除
 */
router.delete('/:id', authenticateToken, requirePermissions('customer:write'), deleteCustomer)

/**
 * @swagger
 * /api/v1/customers/{customerId}/contacts:
 *   get:
 *     tags: [客户管理]
 *     summary: 获取客户联系人列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/CustomerContact'
 */
router.get('/:customerId/contacts', authenticateToken, requirePermissions('customer:read'), getCustomerContacts)

/**
 * @swagger
 * /api/v1/customers/{customerId}/contacts:
 *   post:
 *     tags: [客户管理]
 *     summary: 创建客户联系人
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateContactRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *       404:
 *         description: 客户不存在
 */
router.post('/:customerId/contacts', authenticateToken, requirePermissions('customer:write'), createCustomerContact)

/**
 * @swagger
 * /api/v1/customers/{customerId}/contacts/{contactId}:
 *   put:
 *     tags: [客户管理]
 *     summary: 更新客户联系人
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *       - in: path
 *         name: contactId
 *         required: true
 *         schema:
 *           type: string
 *         description: 联系人ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateContactRequest'
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 联系人不存在
 */
router.put('/:customerId/contacts/:contactId', authenticateToken, requirePermissions('customer:write'), updateCustomerContact)

/**
 * @swagger
 * /api/v1/customers/{customerId}/contacts/{contactId}:
 *   delete:
 *     tags: [客户管理]
 *     summary: 删除客户联系人
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: customerId
 *         required: true
 *         schema:
 *           type: string
 *         description: 客户ID
 *       - in: path
 *         name: contactId
 *         required: true
 *         schema:
 *           type: string
 *         description: 联系人ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 联系人不存在
 */
router.delete('/:customerId/contacts/:contactId', authenticateToken, requirePermissions('customer:write'), deleteCustomerContact)

export default router