import { Router } from 'express'
import {
  getConfigurations,
  getConfiguration,
  createConfiguration,
  updateConfiguration,
  deleteConfiguration,
  getArchiveConfigurations,
  getConfigurationStats,
  toggleConfigurationStatus
} from '@/controllers/configuration.controller'
import { authenticateToken, requirePermissions } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     ProjectConfiguration:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 配置ID
 *         archiveId:
 *           type: string
 *           description: 项目档案ID
 *         configType:
 *           type: string
 *           enum: [SERVER, DATABASE, VPN, ACCOUNT, ENVIRONMENT, OTHER]
 *           description: 配置类型
 *         title:
 *           type: string
 *           description: 配置标题
 *         configData:
 *           type: object
 *           description: 配置数据
 *         encryptedFields:
 *           type: array
 *           items:
 *             type: string
 *           description: 需要加密的字段名
 *         description:
 *           type: string
 *           description: 配置说明
 *         isActive:
 *           type: boolean
 *           description: 是否启用
 *         lastUpdated:
 *           type: string
 *           format: date-time
 *           description: 最后更新时间
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 * 
 *     CreateConfigurationRequest:
 *       type: object
 *       properties:
 *         archiveId:
 *           type: string
 *           description: 项目档案ID
 *         configType:
 *           type: string
 *           enum: [SERVER, DATABASE, VPN, ACCOUNT, ENVIRONMENT, OTHER]
 *           description: 配置类型
 *         title:
 *           type: string
 *           description: 配置标题
 *         configData:
 *           type: object
 *           description: 配置数据
 *         encryptedFields:
 *           type: array
 *           items:
 *             type: string
 *           description: 需要加密的字段名
 *         description:
 *           type: string
 *           description: 配置说明
 *         isActive:
 *           type: boolean
 *           description: 是否启用
 *       required:
 *         - archiveId
 *         - configType
 *         - title
 *         - configData
 */

/**
 * @swagger
 * /api/v1/configurations:
 *   get:
 *     tags: [配置管理]
 *     summary: 获取配置列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: configType
 *         schema:
 *           type: string
 *           enum: [SERVER, DATABASE, VPN, ACCOUNT, ENVIRONMENT, OTHER]
 *         description: 配置类型过滤
 *       - in: query
 *         name: archiveId
 *         schema:
 *           type: string
 *         description: 项目档案ID过滤
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 是否启用过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     configurations:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/ProjectConfiguration'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         page:
 *                           type: integer
 *                         limit:
 *                           type: integer
 *                         total:
 *                           type: integer
 *                         pages:
 *                           type: integer
 */
router.get('/', authenticateToken, requirePermissions('config:read'), getConfigurations)

/**
 * @swagger
 * /api/v1/configurations/stats:
 *   get:
 *     tags: [配置管理]
 *     summary: 获取配置统计信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalConfigurations:
 *                       type: integer
 *                     recentConfigurations:
 *                       type: integer
 *                     activeConfigurations:
 *                       type: integer
 *                     typeDistribution:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           type:
 *                             type: string
 *                           count:
 *                             type: integer
 */
router.get('/stats', authenticateToken, requirePermissions('config:read'), getConfigurationStats)

/**
 * @swagger
 * /api/v1/configurations/archive/{archiveId}:
 *   get:
 *     tags: [配置管理]
 *     summary: 获取项目档案的配置列表
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: archiveId
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目档案ID
 *       - in: query
 *         name: configType
 *         schema:
 *           type: string
 *           enum: [SERVER, DATABASE, VPN, ACCOUNT, ENVIRONMENT, OTHER]
 *         description: 配置类型过滤
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 是否启用过滤
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ProjectConfiguration'
 *       404:
 *         description: 项目档案不存在
 */
router.get('/archive/:archiveId', authenticateToken, requirePermissions('config:read'), getArchiveConfigurations)

/**
 * @swagger
 * /api/v1/configurations/{id}:
 *   get:
 *     tags: [配置管理]
 *     summary: 获取配置详情
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ProjectConfiguration'
 *       404:
 *         description: 配置不存在
 */
router.get('/:id', authenticateToken, requirePermissions('config:read'), getConfiguration)

/**
 * @swagger
 * /api/v1/configurations:
 *   post:
 *     tags: [配置管理]
 *     summary: 创建配置
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateConfigurationRequest'
 *     responses:
 *       201:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   $ref: '#/components/schemas/ProjectConfiguration'
 *       400:
 *         description: 请求参数错误或项目档案不存在
 */
router.post('/', authenticateToken, requirePermissions('config:write'), createConfiguration)

/**
 * @swagger
 * /api/v1/configurations/{id}:
 *   put:
 *     tags: [配置管理]
 *     summary: 更新配置
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               configType:
 *                 type: string
 *                 enum: [SERVER, DATABASE, VPN, ACCOUNT, ENVIRONMENT, OTHER]
 *               title:
 *                 type: string
 *               configData:
 *                 type: object
 *               encryptedFields:
 *                 type: array
 *                 items:
 *                   type: string
 *               description:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: 更新成功
 *       404:
 *         description: 配置不存在
 *       400:
 *         description: 请求参数错误
 */
router.put('/:id', authenticateToken, requirePermissions('config:write'), updateConfiguration)

/**
 * @swagger
 * /api/v1/configurations/{id}:
 *   delete:
 *     tags: [配置管理]
 *     summary: 删除配置
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       404:
 *         description: 配置不存在
 */
router.delete('/:id', authenticateToken, requirePermissions('config:write'), deleteConfiguration)

/**
 * @swagger
 * /api/v1/configurations/{id}/toggle-status:
 *   patch:
 *     tags: [配置管理]
 *     summary: 切换配置状态
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置ID
 *     responses:
 *       200:
 *         description: 状态切换成功
 *       404:
 *         description: 配置不存在
 */
router.patch('/:id/toggle-status', authenticateToken, requirePermissions('config:write'), toggleConfigurationStatus)

export default router