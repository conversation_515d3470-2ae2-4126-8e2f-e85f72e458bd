import { Router } from 'express'
import { 
  register, 
  login, 
  refresh, 
  logout, 
  me, 
  changePassword 
} from '@/controllers/auth.controller'
import { authenticateToken } from '@/middleware/auth.middleware'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 用户ID
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         fullName:
 *           type: string
 *           description: 全名
 *         phone:
 *           type: string
 *           description: 电话
 *         avatar:
 *           type: string
 *           description: 头像URL
 *         status:
 *           type: string
 *           enum: [ACTIVE, INACTIVE, BANNED]
 *           description: 用户状态
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *       required:
 *         - id
 *         - username
 *         - email
 * 
 *     LoginRequest:
 *       type: object
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名或邮箱
 *         password:
 *           type: string
 *           description: 密码
 *       required:
 *         - username
 *         - password
 * 
 *     RegisterRequest:
 *       type: object
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         password:
 *           type: string
 *           description: 密码
 *         fullName:
 *           type: string
 *           description: 全名
 *         phone:
 *           type: string
 *           description: 电话
 *       required:
 *         - username
 *         - email
 *         - password
 * 
 *     AuthResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *         message:
 *           type: string
 *         data:
 *           type: object
 *           properties:
 *             user:
 *               $ref: '#/components/schemas/User'
 *             accessToken:
 *               type: string
 *               description: 访问令牌
 *             expiresIn:
 *               type: number
 *               description: 令牌过期时间（秒）
 * 
 *   securitySchemes:
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 */

/**
 * @swagger
 * /api/v1/auth/register:
 *   post:
 *     tags: [认证]
 *     summary: 用户注册
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: 注册成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: 请求参数错误或用户已存在
 *       500:
 *         description: 服务器错误
 */
router.post('/register', register)

/**
 * @swagger
 * /api/v1/auth/login:
 *   post:
 *     tags: [认证]
 *     summary: 用户登录
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       401:
 *         description: 认证失败
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器错误
 */
router.post('/login', login)

/**
 * @swagger
 * /api/v1/auth/refresh:
 *   post:
 *     tags: [认证]
 *     summary: 刷新访问令牌
 *     description: 使用刷新令牌获取新的访问令牌
 *     responses:
 *       200:
 *         description: 令牌刷新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     accessToken:
 *                       type: string
 *                     expiresIn:
 *                       type: number
 *       401:
 *         description: 刷新令牌无效或过期
 */
router.post('/refresh', refresh)

/**
 * @swagger
 * /api/v1/auth/logout:
 *   post:
 *     tags: [认证]
 *     summary: 用户登出
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 登出成功
 *       500:
 *         description: 服务器错误
 */
router.post('/logout', logout)

/**
 * @swagger
 * /api/v1/auth/me:
 *   get:
 *     tags: [认证]
 *     summary: 获取当前用户信息
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/User'
 *       401:
 *         description: 未认证
 *       404:
 *         description: 用户不存在
 */
router.get('/me', authenticateToken, me)

/**
 * @swagger
 * /api/v1/auth/change-password:
 *   post:
 *     tags: [认证]
 *     summary: 修改密码
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 description: 当前密码
 *               newPassword:
 *                 type: string
 *                 description: 新密码
 *             required:
 *               - currentPassword
 *               - newPassword
 *     responses:
 *       200:
 *         description: 密码修改成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未认证或当前密码错误
 *       404:
 *         description: 用户不存在
 */
router.post('/change-password', authenticateToken, changePassword)

export default router