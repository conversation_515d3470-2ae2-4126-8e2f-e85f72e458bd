import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getTaskStatus,
  executeTask,
  getSystemStats
} from '@/controllers/scheduler.controller'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     TaskStatus:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           description: 任务名称
 *         description:
 *           type: string
 *           description: 任务描述
 *         schedule:
 *           type: string
 *           description: 调度表达式
 *         isActive:
 *           type: boolean
 *           description: 是否激活
 *         isRunning:
 *           type: boolean
 *           description: 是否正在运行
 *       example:
 *         name: "daily-report"
 *         description: "发送每日报告"
 *         schedule: "0 8 * * 1-5"
 *         isActive: true
 *         isRunning: false
 */

/**
 * @swagger
 * /api/v1/scheduler/tasks:
 *   get:
 *     summary: 获取所有定时任务状态（管理员）
 *     tags: [Scheduler]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     tasks:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/TaskStatus'
 *                     totalTasks:
 *                       type: integer
 *                       description: 总任务数
 *                     activeTasks:
 *                       type: integer
 *                       description: 激活任务数
 *                     runningTasks:
 *                       type: integer
 *                       description: 正在运行任务数
 */
router.get('/tasks', authMiddleware, requirePermissions(['admin:all']), getTaskStatus)

/**
 * @swagger
 * /api/v1/scheduler/tasks/{taskName}/execute:
 *   post:
 *     summary: 手动执行指定任务（管理员）
 *     tags: [Scheduler]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: taskName
 *         required: true
 *         schema:
 *           type: string
 *         description: 任务名称
 *     responses:
 *       200:
 *         description: 执行成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *       400:
 *         description: 参数错误
 *       500:
 *         description: 执行失败
 */
router.post('/tasks/:taskName/execute', authMiddleware, requirePermissions(['admin:all']), executeTask)

/**
 * @swagger
 * /api/v1/scheduler/system-stats:
 *   get:
 *     summary: 获取系统统计信息（管理员）
 *     tags: [Scheduler]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     uptime:
 *                       type: number
 *                       description: 系统运行时间（秒）
 *                     memoryUsage:
 *                       type: object
 *                       description: 内存使用情况
 *                     cpuUsage:
 *                       type: object
 *                       description: CPU使用情况
 *                     nodeVersion:
 *                       type: string
 *                       description: Node.js版本
 *                     platform:
 *                       type: string
 *                       description: 操作系统平台
 *                     loadAverage:
 *                       type: array
 *                       items:
 *                         type: number
 *                       description: 负载平均值
 *                     freeMemory:
 *                       type: number
 *                       description: 空闲内存（字节）
 *                     totalMemory:
 *                       type: number
 *                       description: 总内存（字节）
 */
router.get('/system-stats', authMiddleware, requirePermissions(['admin:all']), getSystemStats)

export default router