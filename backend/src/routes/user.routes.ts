import { Router } from 'express'
import { authMiddleware, requirePermissions } from '@/middleware/auth.middleware'
import {
  getUsers,
  getUser,
  createUser,
  updateUser,
  deleteUser,
  changePassword,
  resetPassword,
  toggleUserStatus,
  getUserStats,
  getDepartments,
  importUsers,
  getCurrentUser,
  updateCurrentUser
} from '@/controllers/user.controller'

const router = Router()

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           description: 用户ID
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         fullName:
 *           type: string
 *           description: 全名
 *         department:
 *           type: string
 *           description: 部门
 *         phone:
 *           type: string
 *           description: 手机号
 *         isActive:
 *           type: boolean
 *           description: 是否激活
 *         lastLoginAt:
 *           type: string
 *           format: date-time
 *           description: 最后登录时间
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 *         role:
 *           type: object
 *           properties:
 *             id:
 *               type: string
 *             name:
 *               type: string
 *             description:
 *               type: string
 *             permissions:
 *               type: array
 *               items:
 *                 type: string
 *       example:
 *         id: "clp123456789"
 *         username: "admin"
 *         email: "<EMAIL>"
 *         fullName: "系统管理员"
 *         department: "技术部"
 *         phone: "13800138000"
 *         isActive: true
 *         lastLoginAt: "2024-01-15T10:30:00Z"
 *         createdAt: "2024-01-01T00:00:00Z"
 *         updatedAt: "2024-01-15T10:30:00Z"
 *         role:
 *           id: "clp987654321"
 *           name: "admin"
 *           description: "系统管理员"
 *           permissions: ["admin:all"]
 */

/**
 * @swagger
 * /api/v1/users:
 *   get:
 *     summary: 获取用户列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: roleId
 *         schema:
 *           type: string
 *         description: 角色ID筛选
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: 是否激活筛选
 *       - in: query
 *         name: department
 *         schema:
 *           type: string
 *         description: 部门筛选
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/User'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/', authMiddleware, requirePermissions('user:read'), getUsers)

/**
 * @swagger
 * /api/v1/users/me:
 *   get:
 *     summary: 获取当前用户信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/User'
 */
router.get('/me', authMiddleware, getCurrentUser)

/**
 * @swagger
 * /api/v1/users/me:
 *   put:
 *     summary: 更新当前用户信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               fullName:
 *                 type: string
 *               department:
 *                 type: string
 *               phone:
 *                 type: string
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/me', authMiddleware, updateCurrentUser)

/**
 * @swagger
 * /api/v1/users/stats:
 *   get:
 *     summary: 获取用户统计信息
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/stats', authMiddleware, requirePermissions('user:read'), getUserStats)

/**
 * @swagger
 * /api/v1/users/departments:
 *   get:
 *     summary: 获取部门列表
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/departments', getDepartments)

/**
 * @swagger
 * /api/v1/users/{id}:
 *   get:
 *     summary: 获取用户详情
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/User'
 */
router.get('/:id', authMiddleware, requirePermissions('user:read'), getUser)

/**
 * @swagger
 * /api/v1/users:
 *   post:
 *     summary: 创建用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - username
 *               - email
 *               - password
 *               - fullName
 *               - roleId
 *             properties:
 *               username:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 50
 *               email:
 *                 type: string
 *                 format: email
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 50
 *               fullName:
 *                 type: string
 *                 maxLength: 100
 *               roleId:
 *                 type: string
 *               department:
 *                 type: string
 *                 maxLength: 100
 *               phone:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: 创建成功
 */
router.post('/', authMiddleware, requirePermissions('user:write'), createUser)

/**
 * @swagger
 * /api/v1/users/import:
 *   post:
 *     summary: 批量导入用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - username
 *                 - email
 *                 - password
 *                 - fullName
 *                 - roleId
 *               properties:
 *                 username:
 *                   type: string
 *                 email:
 *                   type: string
 *                 password:
 *                   type: string
 *                 fullName:
 *                   type: string
 *                 roleId:
 *                   type: string
 *                 department:
 *                   type: string
 *                 phone:
 *                   type: string
 *                 isActive:
 *                   type: boolean
 *     responses:
 *       200:
 *         description: 导入完成
 */
router.post('/import', authMiddleware, requirePermissions('user:write'), importUsers)

/**
 * @swagger
 * /api/v1/users/{id}:
 *   put:
 *     summary: 更新用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *               email:
 *                 type: string
 *               fullName:
 *                 type: string
 *               roleId:
 *                 type: string
 *               department:
 *                 type: string
 *               phone:
 *                 type: string
 *               isActive:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: 更新成功
 */
router.put('/:id', authMiddleware, requirePermissions('user:write'), updateUser)

/**
 * @swagger
 * /api/v1/users/{id}:
 *   delete:
 *     summary: 删除用户
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 删除成功
 */
router.delete('/:id', authMiddleware, requirePermissions('user:delete'), deleteUser)

/**
 * @swagger
 * /api/v1/users/{id}/change-password:
 *   post:
 *     summary: 修改密码
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: 修改成功
 */
router.post('/:id/change-password', authMiddleware, changePassword)

/**
 * @swagger
 * /api/v1/users/{id}/reset-password:
 *   post:
 *     summary: 重置密码（管理员）
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - newPassword
 *             properties:
 *               newPassword:
 *                 type: string
 *                 minLength: 6
 *     responses:
 *       200:
 *         description: 重置成功
 */
router.post('/:id/reset-password', authMiddleware, requirePermissions('user:write'), resetPassword)

/**
 * @swagger
 * /api/v1/users/{id}/toggle-status:
 *   patch:
 *     summary: 切换用户状态
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 切换成功
 */
router.patch('/:id/toggle-status', authMiddleware, requirePermissions('user:write'), toggleUserStatus)

export default router