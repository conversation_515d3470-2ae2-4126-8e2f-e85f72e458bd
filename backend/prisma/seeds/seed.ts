import { PrismaClient } from '@prisma/client'
import { hashPassword } from '../../src/utils/crypto.util'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 开始播种数据...')

  // 创建默认角色
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: '系统管理员',
      permissions: JSON.stringify([
        'admin:all',
        'user:read',
        'user:write',
        'customer:read',
        'customer:write',
        'archive:read',
        'archive:write',
        'service:read',
        'service:write',
        'config:read',
        'config:write'
      ])
    }
  })

  const engineerRole = await prisma.role.upsert({
    where: { name: 'engineer' },
    update: {},
    create: {
      name: 'engineer',
      description: '运维工程师',
      permissions: JSON.stringify([
        'customer:read',
        'archive:read',
        'archive:write',
        'service:read',
        'service:write',
        'config:read',
        'config:write'
      ])
    }
  })

  const customerServiceRole = await prisma.role.upsert({
    where: { name: 'customer_service' },
    update: {},
    create: {
      name: 'customer_service',
      description: '客户服务',
      permissions: JSON.stringify([
        'customer:read',
        'customer:write',
        'archive:read',
        'service:read',
        'service:write'
      ])
    }
  })

  console.log('✅ 角色创建完成')

  // 创建默认管理员用户
  const hashedPassword = await hashPassword('admin123')
  
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: hashedPassword,
      fullName: '系统管理员',
      status: 'ACTIVE'
    }
  })

  // 分配管理员角色
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: adminUser.id,
        roleId: adminRole.id
      }
    },
    update: {},
    create: {
      userId: adminUser.id,
      roleId: adminRole.id
    }
  })

  console.log('✅ 管理员用户创建完成')

  // 创建示例客户
  const customer1 = await prisma.customer.create({
    data: {
      name: '示例科技有限公司',
      company: '示例科技有限公司',
      industry: '互联网',
      level: 'STANDARD',
      contactPerson: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      address: '北京市朝阳区示例大厦',
      description: '一家专注于互联网技术的公司',
      createdBy: adminUser.id
    }
  })

  // 创建客户联系人
  await prisma.customerContact.create({
    data: {
      customerId: customer1.id,
      name: '张三',
      position: '技术总监',
      email: '<EMAIL>',
      phone: '13800138000',
      isPrimary: true
    }
  })

  await prisma.customerContact.create({
    data: {
      customerId: customer1.id,
      name: '李四',
      position: '运维经理',
      email: '<EMAIL>',
      phone: '13800138001',
      isPrimary: false
    }
  })

  console.log('✅ 示例客户创建完成')

  // 创建示例项目档案
  const archive1 = await prisma.projectArchive.create({
    data: {
      customerId: customer1.id,
      name: '企业官网系统',
      description: '公司官方网站，包含产品展示、新闻资讯、联系我们等功能',
      technology: 'Vue.js + Node.js + MySQL',
      environment: '阿里云 ECS + RDS',
      version: 'v2.1.0',
      deploymentDate: new Date('2024-01-15'),
      status: 'ACTIVE',
      createdBy: adminUser.id
    }
  })

  // 创建项目配置
  await prisma.projectConfiguration.create({
    data: {
      archiveId: archive1.id,
      configType: 'SERVER',
      title: '生产服务器配置',
      configData: JSON.stringify({
        host: '47.xxx.xxx.xxx',
        port: 22,
        username: 'root',
        password: 'encrypted_password_here'
      }),
      encryptedFields: JSON.stringify(['password']),
      description: '生产环境服务器SSH连接配置',
      isActive: true
    }
  })

  await prisma.projectConfiguration.create({
    data: {
      archiveId: archive1.id,
      configType: 'DATABASE',
      title: '生产数据库配置',
      configData: JSON.stringify({
        host: 'rm-xxxxx.mysql.rds.aliyuncs.com',
        port: 3306,
        database: 'company_website',
        username: 'admin',
        password: 'encrypted_db_password_here'
      }),
      encryptedFields: JSON.stringify(['password']),
      description: '生产环境MySQL数据库连接配置',
      isActive: true
    }
  })

  console.log('✅ 示例项目档案创建完成')

  // 创建SLA模板
  const standardSla = await prisma.slaTemplate.create({
    data: {
      name: '标准SLA',
      description: '标准客户的服务等级协议',
      responseTime: 60, // 1小时响应
      resolutionTime: 24, // 24小时解决
      availability: 99.5
    }
  })

  const premiumSla = await prisma.slaTemplate.create({
    data: {
      name: '高级SLA',
      description: '高级客户的服务等级协议',
      responseTime: 30, // 30分钟响应
      resolutionTime: 8, // 8小时解决
      availability: 99.9
    }
  })

  console.log('✅ SLA模板创建完成')

  // 创建示例服务工单
  const service1 = await prisma.service.create({
    data: {
      archiveId: archive1.id,
      slaTemplateId: standardSla.id,
      ticketNumber: 'OPS-20240101-001',
      title: '网站首页加载缓慢问题',
      description: '用户反馈网站首页加载时间过长，影响用户体验，需要优化性能。',
      category: 'SUPPORT',
      priority: 'HIGH',
      status: 'PENDING',
      customerContact: '张三',
      estimatedHours: 4.0,
      createdBy: adminUser.id
    }
  })

  // 创建工作日志
  await prisma.serviceWorkLog.create({
    data: {
      serviceId: service1.id,
      userId: adminUser.id,
      description: '分析网站性能瓶颈，发现数据库查询效率较低',
      workHours: 2.0,
      workDate: new Date(),
      category: 'ANALYSIS'
    }
  })

  // 创建服务评论
  await prisma.serviceComment.create({
    data: {
      serviceId: service1.id,
      content: '已经定位到问题，主要是数据库查询没有使用索引，计划优化SQL语句。',
      isInternal: true,
      authorId: adminUser.id
    }
  })

  console.log('✅ 示例服务工单创建完成')

  // 创建通知模板
  await prisma.notificationTemplate.create({
    data: {
      name: '工单创建通知',
      type: 'EMAIL',
      subject: '新工单创建 - {{ticketNumber}}',
      content: `
        您好 {{customerName}}，

        您的服务工单已成功创建：

        工单号：{{ticketNumber}}
        标题：{{title}}
        优先级：{{priority}}
        创建时间：{{createdAt}}

        我们将尽快处理您的请求。

        运维服务团队
      `,
      variables: JSON.stringify([
        'customerName',
        'ticketNumber', 
        'title',
        'priority',
        'createdAt'
      ])
    }
  })

  await prisma.notificationTemplate.create({
    data: {
      name: '工单完成通知',
      type: 'EMAIL',
      subject: '工单已完成 - {{ticketNumber}}',
      content: `
        您好 {{customerName}}，

        您的服务工单已完成处理：

        工单号：{{ticketNumber}}
        标题：{{title}}
        处理结果：{{resolution}}
        完成时间：{{completedAt}}

        如有问题，请及时与我们联系。

        运维服务团队
      `,
      variables: JSON.stringify([
        'customerName',
        'ticketNumber',
        'title', 
        'resolution',
        'completedAt'
      ])
    }
  })

  console.log('✅ 通知模板创建完成')

  console.log('🎉 数据播种完成！')
  console.log(`
    默认管理员账号:
    用户名: admin
    密码: admin123
    邮箱: <EMAIL>
  `)
}

main()
  .catch((e) => {
    console.error('❌ 播种数据时出错:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })