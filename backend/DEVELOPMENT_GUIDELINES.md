# TypeScript 开发准则

基于系统错误分析总结的开发准则，避免常见的 TypeScript 和 Prisma 类型错误。

## 1. 路径参数验证准则

### 问题描述
路径参数 `req.params.id` 可能为 `undefined`，但 Prisma 查询期望明确的 `string` 类型。

### 解决方案
在每个使用路径参数的控制器函数开始时，都必须进行参数验证：

```typescript
export const getEntity = async (req: Request, res: Response) => {
  try {
    const { id } = req.params
    
    // 必须的参数验证
    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'ID参数缺失'
      })
    }
    
    // 然后才能安全使用 id
    const entity = await prisma.entity.findUnique({
      where: { id }
    })
  }
}
```

## 2. 可选属性处理准则

### 问题描述
当 TypeScript 配置了 `exactOptionalPropertyTypes: true` 时，不能将 `undefined` 值传递给期望 `null` 的 Prisma 字段。

### 解决方案

#### 2.1 创建操作
```typescript
// 错误方式
const entity = await prisma.entity.create({
  data: {
    ...data,  // 可能包含 undefined 值
    createdBy: userId
  }
})

// 正确方式
const entity = await prisma.entity.create({
  data: {
    name: data.name,
    description: data.description || null,
    email: data.email || null,
    // 显式处理每个可选字段
    createdBy: userId
  }
})
```

#### 2.2 更新操作
```typescript
// 错误方式
const entity = await prisma.entity.update({
  where: { id },
  data  // 可能包含 undefined 值
})

// 正确方式
const updateData: any = {}
if (data.name !== undefined) updateData.name = data.name
if (data.description !== undefined) updateData.description = data.description || null
if (data.email !== undefined) updateData.email = data.email || null

const entity = await prisma.entity.update({
  where: { id },
  data: updateData
})
```

## 3. JSON 字段处理准则

### 问题描述
Prisma 的 JSON 字段不接受显式的 `null` 值，应该使用 `undefined` 或省略该字段。

### 解决方案
```typescript
const createData: any = {
  // 基础字段
  title: data.title,
  configData: configData
}

// 只有在有值时才添加 JSON 字段
if (encryptedFields.length > 0) {
  createData.encryptedFields = encryptedFields
}

const entity = await prisma.entity.create({
  data: createData
})
```

## 4. 关联查询准则

### 问题描述
某些 Prisma 模型需要复合唯一键或额外的字段才能查询。

### 解决方案
```typescript
// Service 模型需要 id 或 ticketNumber
const service = await prisma.service.findUnique({
  where: { 
    id: serviceId  // 确保使用正确的唯一标识符
  }
})

// 或使用 findFirst 进行更灵活的查询
const service = await prisma.service.findFirst({
  where: { id: serviceId }
})
```

## 5. 未使用参数处理准则

### 问题描述
某些函数参数（如统计函数中的 `req`）未被使用，导致 linter 警告。

### 解决方案
```typescript
// 使用下划线前缀标记未使用的参数
export const getStats = async (_req: Request, res: Response) => {
  // 函数实现
}
```

## 6. 聚合查询空值处理准则

### 问题描述
Prisma 聚合查询的结果可能为 `undefined`，需要安全处理。

### 解决方案
```typescript
const totalHours = await prisma.serviceWorkLog.aggregate({
  where: { serviceId: id },
  _sum: { workHours: true }
})

// 安全访问聚合结果
const hours = totalHours._sum.workHours || 0
```

## 7. 错误处理标准化

### 标准错误响应格式
```typescript
// 参数验证错误
return res.status(400).json({
  success: false,
  message: '请求参数验证失败',
  errors: error.errors  // 对于 Zod 验证错误
})

// 资源不存在错误
return res.status(404).json({
  success: false,
  message: '资源不存在'
})

// 服务器内部错误
return res.status(500).json({
  success: false,
  message: '操作失败'
})
```

## 8. 开发检查清单

在编写控制器函数时，请检查以下项目：

- [ ] 是否对所有路径参数进行了 null/undefined 检查？
- [ ] 是否正确处理了可选属性（`undefined` → `null`）？
- [ ] 是否避免了将 `undefined` 传递给 Prisma 查询？
- [ ] 是否正确处理了 JSON 字段？
- [ ] 是否使用了正确的唯一标识符查询？
- [ ] 是否安全处理了聚合查询结果？
- [ ] 是否标记了未使用的参数？
- [ ] 是否有统一的错误处理？

## 9. TypeScript 配置要求

确保 `tsconfig.json` 中有以下配置：
```json
{
  "compilerOptions": {
    "exactOptionalPropertyTypes": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true
  }
}
```

这些配置会帮助在编译时发现潜在的类型问题。

---

遵循这些准则可以避免大部分常见的 TypeScript 和 Prisma 类型错误，提高代码质量和可维护性。