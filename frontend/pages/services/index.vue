<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      title="服务工单"
      description="管理系统中的所有服务工单"
      icon="i-heroicons-ticket"
      :actions="headerActions"
      :stats="headerStats"
      @action="handleHeaderAction"
    />

    <!-- 数据表格 -->
    <DataTable
      :data="items"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :filters="filterConfig"
      :badge-config="badgeConfig"
      :action-items="getActionItems"
      search-placeholder="搜索工单标题、描述或客户..."
      @search="handleSearch"
      @filter="handleFilter"
      @page-change="handlePageChange"
      @action="handleTableAction"
    >
      <!-- 自定义工单标题列 -->
      <template #title-data="{ row }">
        <div class="space-y-1">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-900">{{ row.title }}</span>
            <UBadge
              v-if="row.urgent"
              color="red"
              variant="soft"
              size="xs"
            >
              紧急
            </UBadge>
          </div>
          <div class="text-xs text-gray-500">
            #{{ row.ticketNumber }} • {{ row.customerName }}
          </div>
          <div class="text-xs text-gray-600 line-clamp-2">
            {{ row.description }}
          </div>
        </div>
      </template>

      <!-- 自定义分配信息列 -->
      <template #assignment-data="{ row }">
        <div class="space-y-1">
          <div class="text-sm text-gray-900">
            {{ row.assignedTo || '未分配' }}
          </div>
          <div class="text-xs text-gray-500">
            {{ row.department || '-' }}
          </div>
        </div>
      </template>

      <!-- 自定义SLA状态列 -->
      <template #sla-data="{ row }">
        <div class="space-y-1">
          <div class="flex items-center space-x-2">
            <div
              :class="getSlaStatusClass(row.slaStatus)"
              class="w-2 h-2 rounded-full"
            ></div>
            <span class="text-xs text-gray-600">
              {{ getSlaStatusText(row.slaStatus) }}
            </span>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatSlaTime(row.slaDeadline) }}
          </div>
        </div>
      </template>
    </DataTable>

    <!-- 创建工单模态框 -->
    <FormModal
      v-model="showCreateModal"
      title="创建工单"
      :fields="serviceFields"
      :schema="serviceSchema"
      :form-state="createForm.formState"
      :loading="creating"
      :field-options="fieldOptions"
      :modal-config="{ width: 'sm:max-w-4xl' }"
      @submit="handleCreate"
      @cancel="handleCreateCancel"
    />

    <!-- 编辑工单模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑工单"
      :fields="serviceFields"
      :schema="serviceSchema"
      :form-state="editForm.formState"
      :loading="updating"
      :field-options="fieldOptions"
      :modal-config="{ width: 'sm:max-w-4xl' }"
      @submit="handleUpdate"
      @cancel="handleEditCancel"
    />

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="确认删除"
      :message="`确定要删除工单 ${selectedItem?.title} 吗？`"
      warning-message="此操作不可撤销，相关的工作日志和附件也将被删除。"
      :loading="deleting"
      @confirm="handleDelete"
      @cancel="handleDeleteCancel"
    />

    <!-- 分配工单模态框 -->
    <UModal v-model="showAssignModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">分配工单</h3>
        </template>

        <UForm
          :schema="assignSchema"
          :state="assignForm"
          @submit="handleAssign"
          class="space-y-4"
        >
          <UFormGroup label="分配给" name="assignedTo" required>
            <USelect
              v-model="assignForm.assignedTo"
              :options="userOptions"
              placeholder="请选择处理人员"
              value-attribute="value"
              option-attribute="label"
            />
          </UFormGroup>

          <UFormGroup label="部门" name="department">
            <USelect
              v-model="assignForm.department"
              :options="departmentOptions"
              placeholder="请选择部门"
              value-attribute="value"
              option-attribute="label"
            />
          </UFormGroup>

          <UFormGroup label="备注" name="note">
            <UTextarea
              v-model="assignForm.note"
              placeholder="分配备注（可选）"
              :rows="3"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showAssignModal = false"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="assigning"
            >
              确认分配
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { z } from 'zod'
import type { Column, FormField, ActionItem, ButtonVariant } from '~/types/ui'

// 定义类型接口
interface ServiceTicket {
  id: string
  title: string
  description: string
  customerId: string
  customer: { name: string }
  projectId: string
  project: { name: string }
  type: 'bug' | 'feature' | 'support' | 'maintenance' | 'consultation' | 'other'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'processing' | 'completed' | 'closed'
  assignedTo: string
  assignedUser: { name: string }
  department: string
  expectedResolution: string
  actualResolution: string
  slaDeadline: string
  createdAt: string
  updatedAt: string
}

interface Customer {
  id: string
  name: string
}

interface Project {
  id: string
  name: string
}

interface User {
  id: string
  name: string
}

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const authStore = useAuthStore()
const route = useRoute()
const { $apiClient } = useNuxtApp()

// 设置页面标题
useHead({
  title: '服务工单'
})

// 工单验证Schema
const serviceSchema = z.object({
  title: z.string().min(1, '请输入工单标题'),
  description: z.string().min(1, '请输入工单描述'),
  customerId: z.string().min(1, '请选择客户'),
  projectId: z.string().optional(),
  type: z.enum(['incident', 'request', 'change', 'problem']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  category: z.string().min(1, '请选择分类'),
  subcategory: z.string().optional(),
  urgent: z.boolean().optional(),
  expectedResolution: z.string().optional(),
  attachments: z.array(z.string()).optional()
})

// 分配验证Schema
const assignSchema = z.object({
  assignedTo: z.string().min(1, '请选择处理人员'),
  department: z.string().optional(),
  note: z.string().optional()
})

// 使用CRUD组合函数
const {
  items,
  pagination,
  loading,
  creating,
  updating,
  deleting,
  selectedItem,
  loadItems,
  createItem,
  updateItem,
  deleteItem,
  setSelectedItem
} = useCrud({
  endpoint: '/services',
  defaultPageSize: 15,
  defaultSort: '-createdAt',
  onSuccess: (action, data) => {
    if (action === 'create') {
      showCreateModal.value = false
      createForm.resetForm()
    } else if (action === 'update') {
      showEditModal.value = false
      editForm.resetForm()
    } else if (action === 'delete') {
      showDeleteModal.value = false
      setSelectedItem(null)
    }
  }
})

// 使用搜索组合函数
const { handleSearch, handleFilter } = useSearch(loadItems)

// 使用表单组合函数
const createForm = useForm({
  title: '',
  description: '',
  customerId: '',
  projectId: '',
  type: 'request',
  priority: 'medium',
  category: '',
  subcategory: '',
  urgent: false,
  expectedResolution: '',
  attachments: []
})

const editForm = useForm({
  title: '',
  description: '',
  customerId: '',
  projectId: '',
  type: 'request',
  priority: 'medium',
  category: '',
  subcategory: '',
  urgent: false,
  expectedResolution: '',
  attachments: []
})

// 界面状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const showAssignModal = ref(false)
const assigning = ref(false)

// 分配表单
const assignForm = reactive({
  assignedTo: '',
  department: '',
  note: ''
})

// 统计数据
const stats = ref({
  total: 0,
  pending: 0,
  processing: 0,
  completed: 0
})

// 配置数据
const headerActions = computed(() => [
  {
    key: 'create',
    label: '创建工单',
    icon: 'i-heroicons-plus',
    show: authStore.hasPermission('service:create')
  },
  {
    key: 'export',
    label: '导出数据',
    icon: 'i-heroicons-arrow-down-tray',
    variant: 'soft' as ButtonVariant,
    show: authStore.hasPermission('service:export')
  }
])

const headerStats = computed(() => [
  { label: '总工单', value: stats.value.total },
  { label: '待处理', value: stats.value.pending },
  { label: '处理中', value: stats.value.processing },
  { label: '已完成', value: stats.value.completed }
])

const columns: Column[] = [
  { key: 'title', label: '工单信息', type: 'text' },
  { key: 'type', label: '类型', type: 'badge' },
  { key: 'priority', label: '优先级', type: 'badge' },
  { key: 'status', label: '状态', type: 'badge' },
  { key: 'assignment', label: '分配信息', type: 'text' },
  { key: 'sla', label: 'SLA状态', type: 'text' },
  { key: 'createdAt', label: '创建时间', type: 'date' },
  { key: 'actions', label: '操作', type: 'actions' }
]

const filterConfig = [
  {
    key: 'status',
    placeholder: '状态筛选',
    options: [
      { label: '全部状态', value: '' },
      { label: '待处理', value: 'pending' },
      { label: '处理中', value: 'processing' },
      { label: '已完成', value: 'completed' },
      { label: '已关闭', value: 'closed' }
    ]
  },
  {
    key: 'type',
    placeholder: '类型筛选',
    options: [
      { label: '全部类型', value: '' },
      { label: '事件', value: 'incident' },
      { label: '请求', value: 'request' },
      { label: '变更', value: 'change' },
      { label: '问题', value: 'problem' }
    ]
  },
  {
    key: 'priority',
    placeholder: '优先级筛选',
    options: [
      { label: '全部优先级', value: '' },
      { label: '低', value: 'low' },
      { label: '中', value: 'medium' },
      { label: '高', value: 'high' },
      { label: '紧急', value: 'urgent' }
    ]
  }
]

const badgeConfig = {
  status: {
    pending: { color: 'yellow' as const, text: '待处理' },
    processing: { color: 'blue' as const, text: '处理中' },
    completed: { color: 'green' as const, text: '已完成' },
    closed: { color: 'gray' as const, text: '已关闭' }
  },
  type: {
    incident: { color: 'red' as const, text: '事件' },
    request: { color: 'blue' as const, text: '请求' },
    change: { color: 'purple' as const, text: '变更' },
    problem: { color: 'yellow' as const, text: '问题' }
  },
  priority: {
    low: { color: 'gray' as const, text: '低' },
    medium: { color: 'blue' as const, text: '中' },
    high: { color: 'yellow' as const, text: '高' },
    urgent: { color: 'red' as const, text: '紧急' }
  }
}

// 表单字段配置
const serviceFields: FormField[] = [
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'title', label: '工单标题', type: 'text', placeholder: '请输入工单标题', required: true },
      { name: 'customerId', label: '客户', type: 'select', required: true, options: [] },
      { name: 'projectId', label: '关联项目', type: 'select', options: [] },
      { name: 'type', label: '工单类型', type: 'select', required: true, options: [] },
      { name: 'priority', label: '优先级', type: 'select', required: true, options: [] },
      { name: 'category', label: '分类', type: 'select', required: true, options: [] }
    ]
  },
  { name: 'description', label: '工单描述', type: 'textarea', placeholder: '请详细描述问题或需求', rows: 4, required: true },
  { name: 'expectedResolution', label: '期望解决时间', type: 'date' },
  { name: 'urgent', label: '紧急工单', type: 'checkbox', placeholder: '标记为紧急工单' }
]

// 字段选项配置
const fieldOptions = ref<Record<string, Array<{ label: string; value: any }>>>({
  customerId: [],
  projectId: [],
  type: [
    { label: '事件', value: 'incident' },
    { label: '请求', value: 'request' },
    { label: '变更', value: 'change' },
    { label: '问题', value: 'problem' }
  ],
  priority: [
    { label: '低', value: 'low' },
    { label: '中', value: 'medium' },
    { label: '高', value: 'high' },
    { label: '紧急', value: 'urgent' }
  ],
  category: [
    { label: '技术支持', value: 'technical' },
    { label: '系统维护', value: 'maintenance' },
    { label: '功能开发', value: 'development' },
    { label: '数据处理', value: 'data' },
    { label: '其他', value: 'other' }
  ]
})

const userOptions = ref([])
const departmentOptions = ref([
  { label: '技术部', value: 'tech' },
  { label: '运维部', value: 'ops' },
  { label: '开发部', value: 'dev' },
  { label: '测试部', value: 'qa' }
])

// 工具函数
const getSlaStatusClass = (status: string) => {
  const classes = {
    normal: 'bg-green-500',
    warning: 'bg-yellow-500',
    critical: 'bg-red-500'
  }
  return classes[status as keyof typeof classes] || 'bg-gray-500'
}

const getSlaStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    warning: '警告',
    critical: '超时'
  }
  return texts[status as keyof typeof texts] || '未知'
}

const formatSlaTime = (deadline: string) => {
  if (!deadline) return '-'
  const now = new Date()
  const deadlineDate = new Date(deadline)
  const diff = deadlineDate.getTime() - now.getTime()
  
  if (diff < 0) {
    return '已超时'
  }
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  
  if (hours > 24) {
    const days = Math.floor(hours / 24)
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取操作项
const getActionItems = (row: any): ActionItem[] => {
  const items: ActionItem[] = []
  
  if (authStore.hasPermission('service:read')) {
    items.push({
      key: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('service:update')) {
    items.push({
      key: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
    
    if (row.status !== 'closed') {
      items.push({
        key: 'assign',
        label: '分配工单',
        icon: 'i-heroicons-user-plus'
      })
    }
  }
  
  if (authStore.hasPermission('service:delete')) {
    items.push({
      key: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red'
    })
  }
  
  return items
}

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'create':
      // 如果URL中有projectId参数，自动填充
      if (route.query.projectId) {
        createForm.setFormData({ projectId: route.query.projectId as string })
      }
      showCreateModal.value = true
      break
    case 'export':
      handleExport()
      break
  }
}

const handleTableAction = (action: string, row: any) => {
  setSelectedItem(row)
  
  switch (action) {
    case 'view':
      navigateTo(`/services/${row.id}`)
      break
    case 'edit':
      editForm.setFormData(row)
      showEditModal.value = true
      break
    case 'assign':
      Object.assign(assignForm, {
        assignedTo: row.assignedTo || '',
        department: row.department || '',
        note: ''
      })
      showAssignModal.value = true
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadItems()
}

// CRUD操作
const handleCreate = async (data: any) => {
  await createItem(data)
}

const handleUpdate = async (data: any) => {
  await updateItem(selectedItem.value.id, data)
}

const handleDelete = async () => {
  await deleteItem(selectedItem.value.id)
}

const handleAssign = async () => {
  assigning.value = true
  
  try {
    await $apiClient(`/services/${selectedItem.value.id}/assign`, { method: 'PUT', body: assignForm })
    
    showAssignModal.value = false
    Object.assign(assignForm, { assignedTo: '', department: '', note: '' })
    loadItems()
    
    const toast = useToast()
    toast.add({
      title: '分配成功',
      description: '工单已分配',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Assign service error:', error)
    const toast = useToast()
    toast.add({
      title: '分配失败',
      description: error.message || '分配失败，请稍后重试',
      color: 'red'
    })
  } finally {
    assigning.value = false
  }
}

const handleExport = () => {
  const toast = useToast()
  toast.add({
    title: '功能开发中',
    description: '数据导出功能正在开发中',
    color: 'blue'
  })
}

// 取消操作
const handleCreateCancel = () => {
  showCreateModal.value = false
  createForm.resetForm()
}

const handleEditCancel = () => {
  showEditModal.value = false
  editForm.resetForm()
  setSelectedItem(null)
}

const handleDeleteCancel = () => {
  showDeleteModal.value = false
  setSelectedItem(null)
}

// 加载选项数据
const loadOptions = async () => {
  try {
    const [customersResponse, projectsResponse, usersResponse] = await Promise.all([
      $apiClient('/customers', { query: { limit: 100 } }),
      $apiClient('/archives', { query: { limit: 100 } }),
      $apiClient('/users', { query: { limit: 100 } })
    ])
    
    fieldOptions.value.customerId = customersResponse.data.items.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
    
    fieldOptions.value.projectId = [
      { label: '无关联项目', value: '' },
      ...projectsResponse.data.items.map((item: any) => ({
        label: item.name,
        value: item.id
      }))
    ]
    
    userOptions.value = usersResponse.data.items.map((item: any) => ({
      label: item.name,
      value: item.id
    }))
  } catch (error) {
    console.error('Load options error:', error)
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await $apiClient('/services/stats')
    stats.value = response.data
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadItems(),
    loadOptions(),
    loadStats()
  ])
})
</script>
