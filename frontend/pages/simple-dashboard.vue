<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">简化仪表盘</h1>
      <p class="mt-1 text-sm text-gray-500">
        测试页面布局是否正常显示
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- 客户总数 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">客户总数</dt>
              <dd class="text-lg font-medium text-gray-900">25</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 项目总数 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-folder" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">项目总数</dt>
              <dd class="text-lg font-medium text-gray-900">12</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 待处理工单 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-ticket" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">待处理工单</dt>
              <dd class="text-lg font-medium text-gray-900">8</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 配置项数量 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">配置项数量</dt>
              <dd class="text-lg font-medium text-gray-900">156</dd>
            </dl>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 两列布局测试 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 左侧内容 -->
      <UCard class="h-fit">
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">最新工单</h3>
        </template>
        
        <div class="space-y-3">
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                系统性能优化
              </p>
              <p class="text-sm text-gray-500">
                科技公司A • 2024-08-03
              </p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <UBadge color="yellow" variant="soft">
                处理中
              </UBadge>
            </div>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                数据库备份问题
              </p>
              <p class="text-sm text-gray-500">
                制造企业B • 2024-08-02
              </p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <UBadge color="red" variant="soft">
                待处理
              </UBadge>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 右侧内容 -->
      <UCard class="h-fit">
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">系统通知</h3>
        </template>
        
        <div class="space-y-3">
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="flex-shrink-0 mt-0.5">
              <UIcon name="i-heroicons-information-circle" class="w-5 h-5 text-blue-500" />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                系统维护通知
              </p>
              <p class="text-sm text-gray-500 mt-1">
                系统将于今晚22:00-24:00进行维护升级
              </p>
              <p class="text-xs text-gray-400 mt-2">
                2024-08-03 10:30
              </p>
            </div>
          </div>
          
          <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="flex-shrink-0 mt-0.5">
              <UIcon name="i-heroicons-exclamation-triangle" class="w-5 h-5 text-yellow-500" />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                SLA预警
              </p>
              <p class="text-sm text-gray-500 mt-1">
                有3个工单即将超出SLA时限
              </p>
              <p class="text-xs text-gray-400 mt-2">
                2024-08-03 09:30
              </p>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 测试按钮 -->
    <div class="flex space-x-4">
      <UButton color="blue">测试按钮</UButton>
      <NuxtLink to="/" class="text-blue-600 hover:text-blue-500">
        返回首页
      </NuxtLink>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面标题
useHead({
  title: '简化仪表盘 - 运维管理系统'
})
</script>
