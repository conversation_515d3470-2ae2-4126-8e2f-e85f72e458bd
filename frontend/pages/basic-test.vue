<template>
  <div>
    <h1>基础测试页面</h1>
    <p>如果你能看到这个页面，说明基本的页面渲染是正常的。</p>
    
    <div style="background: red; color: white; padding: 10px; margin: 10px 0;">
      这是一个红色的测试框 - 使用内联样式
    </div>
    
    <div class="bg-blue-500 text-white p-4 m-4">
      这是一个蓝色的测试框 - 使用 Tailwind CSS 类
    </div>
    
    <div class="grid grid-cols-2 gap-4 m-4">
      <div class="bg-green-500 text-white p-4">网格项目 1</div>
      <div class="bg-yellow-500 text-white p-4">网格项目 2</div>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: '基础测试'
})
</script>
