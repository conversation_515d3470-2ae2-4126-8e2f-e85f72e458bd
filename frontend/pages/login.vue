<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <!-- Logo和标题 -->
      <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-600">
          <UIcon name="i-heroicons-shield-check" class="h-8 w-8 text-white" />
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          登录到运维管理系统
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          请输入您的账号和密码
        </p>
      </div>

      <!-- 登录表单 -->
      <UForm
        :schema="loginSchema"
        :state="loginForm"
        class="mt-8 space-y-6"
        @submit="handleLogin"
      >
        <div class="space-y-4">
          <!-- 用户名 -->
          <UFormGroup label="用户名" name="username" required>
            <UInput
              v-model="loginForm.username"
              placeholder="请输入用户名"
              icon="i-heroicons-user"
              size="lg"
              :disabled="loading"
            />
          </UFormGroup>

          <!-- 密码 -->
          <UFormGroup label="密码" name="password" required>
            <UInput
              v-model="loginForm.password"
              type="password"
              placeholder="请输入密码"
              icon="i-heroicons-lock-closed"
              size="lg"
              :disabled="loading"
            />
          </UFormGroup>

          <!-- 记住我 -->
          <div class="flex items-center justify-between">
            <UCheckbox
              v-model="loginForm.remember"
              label="记住我"
              :disabled="loading"
            />
            <NuxtLink
              to="/forgot-password"
              class="text-sm text-primary-600 hover:text-primary-500"
            >
              忘记密码？
            </NuxtLink>
          </div>
        </div>

        <!-- 错误提示 -->
        <UAlert
          v-if="errorMessage"
          icon="i-heroicons-exclamation-triangle"
          color="red"
          variant="soft"
          :title="errorMessage"
          :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'gray', variant: 'link', padded: false }"
          @close="errorMessage = ''"
        />

        <!-- 登录按钮 -->
        <UButton
          type="submit"
          color="primary"
          size="lg"
          block
          :loading="loading"
          :disabled="loading"
        >
          {{ loading ? '登录中...' : '登录' }}
        </UButton>
      </UForm>

      <!-- 其他选项 -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 text-gray-500">或者</span>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            还没有账号？
            <NuxtLink
              to="/register"
              class="font-medium text-primary-600 hover:text-primary-500"
            >
              立即注册
            </NuxtLink>
          </p>
        </div>
      </div>

      <!-- 版本信息 -->
      <div class="text-center text-xs text-gray-400">
        运维服务管理系统 v{{ $config.public.appVersion }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { z } from 'zod'

// 页面配置
definePageMeta({
  layout: false,
  auth: false
})

// 设置页面标题
useHead({
  title: '登录'
})

const authStore = useAuthStore()
const route = useRoute()

// 表单验证Schema
const loginSchema = z.object({
  username: z.string().min(1, '请输入用户名'),
  password: z.string().min(1, '请输入密码'),
  remember: z.boolean().optional()
})

// 表单状态
const loginForm = reactive({
  username: '',
  password: '',
  remember: false
})

// 组件状态
const loading = ref(false)
const errorMessage = ref('')

// 处理登录
const handleLogin = async () => {
  try {
    loading.value = true
    errorMessage.value = ''

    const result = await authStore.login(loginForm)

    if (result.success) {
      // 登录成功，跳转到目标页面或首页
      const redirect = route.query.redirect as string || '/'
      await navigateTo(redirect)
    } else {
      errorMessage.value = result.message
    }
  } catch (error: any) {
    console.error('Login error:', error)
    errorMessage.value = error.message || '登录失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 如果已经登录，直接跳转
onMounted(() => {
  if (authStore.isAuthenticated) {
    const redirect = route.query.redirect as string || '/'
    navigateTo(redirect)
  }
})

// 监听Enter键
onMounted(() => {
  const handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !loading.value) {
      handleLogin()
    }
  }

  document.addEventListener('keypress', handleKeyPress)
  
  onUnmounted(() => {
    document.removeEventListener('keypress', handleKeyPress)
  })
})
</script>
