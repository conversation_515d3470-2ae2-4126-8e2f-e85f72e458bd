<template>
  <div class="p-6">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">侧边栏布局测试</h1>
    
    <div class="bg-yellow-100 border border-yellow-400 rounded-lg p-4 mb-6">
      <h2 class="text-lg font-semibold text-yellow-800 mb-2">布局检查清单：</h2>
      <ul class="text-yellow-700 space-y-1">
        <li>✓ 侧边栏应该在左侧显示</li>
        <li>✓ 侧边栏宽度应该是 256px (w-64)</li>
        <li>✓ 主内容区域应该有左边距 (lg:pl-64)</li>
        <li>✓ 在大屏幕上侧边栏应该始终可见</li>
        <li>✓ 在小屏幕上侧边栏应该可以切换显示/隐藏</li>
      </ul>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">当前屏幕信息</h3>
        <div class="space-y-2 text-sm">
          <p><strong>屏幕宽度：</strong> <span id="screen-width">检测中...</span></p>
          <p><strong>窗口宽度：</strong> <span id="window-width">检测中...</span></p>
          <p><strong>布局模式：</strong> <span id="layout-mode">检测中...</span></p>
        </div>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">布局状态</h3>
        <div class="space-y-2 text-sm">
          <p><strong>侧边栏状态：</strong> <span class="text-green-600">应该可见</span></p>
          <p><strong>主内容边距：</strong> <span class="text-green-600">应该有左边距</span></p>
          <p><strong>响应式：</strong> <span class="text-green-600">应该正常工作</span></p>
        </div>
      </div>
    </div>
    
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
      <h3 class="text-lg font-semibold text-blue-900 mb-3">测试说明</h3>
      <p class="text-blue-800 mb-3">
        如果布局正常工作，你应该看到：
      </p>
      <ul class="text-blue-800 space-y-1 list-disc list-inside">
        <li>左侧有一个深蓝色的侧边栏，包含导航菜单</li>
        <li>主内容区域在侧边栏右侧，有适当的间距</li>
        <li>在大屏幕上，侧边栏始终可见</li>
        <li>在小屏幕上，可以通过菜单按钮切换侧边栏</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: '侧边栏布局测试'
})

onMounted(() => {
  // 显示屏幕信息
  const updateScreenInfo = () => {
    const screenWidth = document.getElementById('screen-width')
    const windowWidth = document.getElementById('window-width')
    const layoutMode = document.getElementById('layout-mode')
    
    if (screenWidth) screenWidth.textContent = `${screen.width}px`
    if (windowWidth) windowWidth.textContent = `${window.innerWidth}px`
    if (layoutMode) {
      layoutMode.textContent = window.innerWidth >= 1024 ? '桌面模式' : '移动模式'
    }
  }
  
  updateScreenInfo()
  window.addEventListener('resize', updateScreenInfo)
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenInfo)
  })
})
</script>
