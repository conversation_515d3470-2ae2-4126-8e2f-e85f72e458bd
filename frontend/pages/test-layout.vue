<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-900">布局测试页面</h1>
      <p class="mt-1 text-sm text-gray-500">测试页面布局是否正常工作</p>
      <div class="mt-4 p-4 bg-blue-50 rounded-lg">
        <p class="text-sm text-blue-700">
          如果你能看到这个蓝色框，说明 Tailwind CSS 正在正常工作。
        </p>
        <p class="text-sm text-blue-700 mt-2">
          侧边栏应该在左侧显示，主内容应该有适当的左边距。
        </p>
      </div>
    </div>

    <!-- 测试网格布局 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <UCard>
        <div class="p-4">
          <h3 class="text-lg font-medium text-gray-900">卡片 1</h3>
          <p class="text-sm text-gray-500">这是第一个测试卡片</p>
        </div>
      </UCard>
      
      <UCard>
        <div class="p-4">
          <h3 class="text-lg font-medium text-gray-900">卡片 2</h3>
          <p class="text-sm text-gray-500">这是第二个测试卡片</p>
        </div>
      </UCard>
      
      <UCard>
        <div class="p-4">
          <h3 class="text-lg font-medium text-gray-900">卡片 3</h3>
          <p class="text-sm text-gray-500">这是第三个测试卡片</p>
        </div>
      </UCard>
      
      <UCard>
        <div class="p-4">
          <h3 class="text-lg font-medium text-gray-900">卡片 4</h3>
          <p class="text-sm text-gray-500">这是第四个测试卡片</p>
        </div>
      </UCard>
    </div>

    <!-- 测试两列布局 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">左侧内容</h3>
        </template>
        <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p class="text-gray-500">左侧内容区域</p>
        </div>
      </UCard>
      
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">右侧内容</h3>
        </template>
        <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p class="text-gray-500">右侧内容区域</p>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面配置 - 暂时移除认证中间件用于测试
// definePageMeta({
//   middleware: 'auth'
// })

// 设置页面标题
useHead({
  title: '布局测试'
})
</script>
