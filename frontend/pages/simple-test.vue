<template>
  <div class="p-6">
    <h1 class="text-3xl font-bold text-gray-900 mb-6">简单布局测试</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900">卡片 1</h3>
        <p class="text-gray-600 mt-2">这是第一个测试卡片</p>
        <div class="mt-4 text-2xl font-bold text-blue-600">123</div>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900">卡片 2</h3>
        <p class="text-gray-600 mt-2">这是第二个测试卡片</p>
        <div class="mt-4 text-2xl font-bold text-green-600">456</div>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900">卡片 3</h3>
        <p class="text-gray-600 mt-2">这是第三个测试卡片</p>
        <div class="mt-4 text-2xl font-bold text-yellow-600">789</div>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-900">卡片 4</h3>
        <p class="text-gray-600 mt-2">这是第四个测试卡片</p>
        <div class="mt-4 text-2xl font-bold text-red-600">101</div>
      </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">左侧内容</h3>
        <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p class="text-gray-500">这里是左侧内容区域</p>
        </div>
      </div>
      
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">右侧内容</h3>
        <div class="h-64 bg-gray-100 rounded flex items-center justify-center">
          <p class="text-gray-500">这里是右侧内容区域</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 设置页面标题
useHead({
  title: '简单布局测试'
})
</script>
