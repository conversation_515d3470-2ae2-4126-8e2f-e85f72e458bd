<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">仪表盘</h1>
      <p class="mt-1 text-sm text-gray-500">
        欢迎回来，{{ authStore.user?.fullName || authStore.user?.username }}！这里是系统概览。
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- 客户总数 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">客户总数</dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.customers?.totalCustomers || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 项目总数 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-folder" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">项目总数</dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.archives?.totalArchives || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 待处理工单 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-ticket" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">待处理工单</dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ getPendingServicesCount() }}
              </dd>
            </dl>
          </div>
        </div>
      </UCard>

      <!-- 配置项数量 -->
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">配置项数量</dt>
              <dd class="text-lg font-medium text-gray-900">
                {{ stats.configurations?.totalConfigurations || 0 }}
              </dd>
            </dl>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 工单状态分布 -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">工单状态分布</h3>
        </template>
        
        <div class="h-64">
          <ClientOnly>
            <component
              :is="VChart"
              v-if="VChart && serviceStatusChartData"
              :option="serviceStatusChartData"
              class="w-full h-full"
            />
            <div v-else class="flex items-center justify-center h-full">
              <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-gray-400 animate-spin" />
              <span class="ml-2 text-sm text-gray-500">加载图表中...</span>
            </div>
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-gray-400 animate-spin" />
                <span class="ml-2 text-sm text-gray-500">加载图表中...</span>
              </div>
            </template>
          </ClientOnly>
        </div>
      </UCard>

      <!-- 月度工单趋势 -->
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">月度工单趋势</h3>
        </template>
        
        <div class="h-64">
          <ClientOnly>
            <component
              :is="VChart"
              v-if="VChart && monthlyTrendChartData"
              :option="monthlyTrendChartData"
              class="w-full h-full"
            />
            <div v-else class="flex items-center justify-center h-full">
              <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-gray-400 animate-spin" />
              <span class="ml-2 text-sm text-gray-500">加载图表中...</span>
            </div>
            <template #fallback>
              <div class="flex items-center justify-center h-full">
                <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-gray-400 animate-spin" />
                <span class="ml-2 text-sm text-gray-500">加载图表中...</span>
              </div>
            </template>
          </ClientOnly>
        </div>
      </UCard>
    </div>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最新工单 -->
      <UCard class="h-fit">
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">最新工单</h3>
            <NuxtLink
              to="/services"
              class="text-sm text-blue-600 hover:text-blue-500 transition-colors"
            >
              查看全部
            </NuxtLink>
          </div>
        </template>

        <div class="space-y-3">
          <div
            v-for="service in recentServices"
            :key="service.id"
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ service.title }}
              </p>
              <p class="text-sm text-gray-500">
                {{ service.customer.name }} • {{ formatDate(service.createdAt) }}
              </p>
            </div>
            <div class="flex-shrink-0 ml-4">
              <UBadge
                :color="getStatusColor(service.status)"
                variant="soft"
              >
                {{ getStatusText(service.status) }}
              </UBadge>
            </div>
          </div>

          <div v-if="!recentServices.length" class="text-center py-8">
            <UIcon name="i-heroicons-inbox" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
            <p class="text-sm text-gray-500">暂无工单</p>
          </div>
        </div>
      </UCard>

      <!-- 系统通知 -->
      <UCard class="h-fit">
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">系统通知</h3>
        </template>

        <div class="space-y-3">
          <div
            v-for="notification in systemNotifications"
            :key="notification.id"
            class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <div class="flex-shrink-0 mt-0.5">
              <UIcon
                :name="getNotificationIcon(notification.type)"
                :class="[
                  'w-5 h-5',
                  notification.type === 'warning' ? 'text-yellow-500' :
                  notification.type === 'error' ? 'text-red-500' :
                  notification.type === 'success' ? 'text-green-500' :
                  'text-blue-500'
                ]"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-gray-900">
                {{ notification.title }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                {{ notification.message }}
              </p>
              <p class="text-xs text-gray-400 mt-2">
                {{ formatDate(notification.time) }}
              </p>
            </div>
          </div>

          <div v-if="!systemNotifications.length" class="text-center py-8">
            <UIcon name="i-heroicons-bell-slash" class="w-12 h-12 text-gray-300 mx-auto mb-2" />
            <p class="text-sm text-gray-500">暂无通知</p>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// ECharts 组件引用
const VChart = ref<unknown>(null)

// 动态加载 ECharts 组件
const loadECharts = async () => {
  if (import.meta.client && !VChart.value) {
    try {
      const { default: VChartComponent } = await import('vue-echarts')
      const { use } = await import('echarts/core')
      const { CanvasRenderer } = await import('echarts/renderers')
      const { PieChart, LineChart } = await import('echarts/charts')
      const {
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        GridComponent
      } = await import('echarts/components')

      // 注册 ECharts 组件
      use([
        CanvasRenderer,
        PieChart,
        LineChart,
        TitleComponent,
        TooltipComponent,
        LegendComponent,
        GridComponent
      ])

      VChart.value = VChartComponent
    } catch (error) {
      // 静默处理 ECharts 加载错误
    }
  }
}

// 定义类型接口
interface StatusDistribution {
  status: string
  count: number
}

interface DashboardStats {
  customers: { totalCustomers: number; recentCustomers: number; levelDistribution: unknown[]; topIndustries: unknown[] }
  archives: { totalArchives: number; recentArchives: number; statusDistribution: unknown[]; topTechnologies: unknown[] }
  services: { totalServices: number; recentServices: number; avgResolutionTime: number; statusDistribution: StatusDistribution[]; categoryDistribution: unknown[]; priorityDistribution: unknown[] }
  configurations: { totalConfigurations: number; encryptedConfigurations: number }
}

interface ServiceItem {
  id: string
  title: string
  status: 'pending' | 'processing' | 'completed' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  customer: { name: string }
  createdAt: string
}

interface SystemNotification {
  id: string
  type: 'info' | 'warning' | 'error' | 'success'
  title: string
  message: string
  time: string
}

// 页面配置 - 暂时移除认证中间件用于测试布局
definePageMeta({
  layout: 'simple'
})

// 设置页面标题
useHead({
  title: '仪表盘'
})

const authStore = useAuthStore()

// 数据状态
const stats = ref<DashboardStats>({} as DashboardStats)
const recentServices = ref<ServiceItem[]>([])
const systemNotifications = ref<SystemNotification[]>([
  {
    id: '1',
    type: 'info',
    title: '系统维护通知',
    message: '系统将于今晚22:00-24:00进行维护升级',
    time: new Date().toISOString()
  },
  {
    id: '2',
    type: 'warning',
    title: 'SLA预警',
    message: '有3个工单即将超出SLA时限',
    time: new Date(Date.now() - 3600000).toISOString()
  }
])

// 图表数据
const serviceStatusChartData = computed(() => {
  if (!stats.value.services) return null

  return {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '工单状态',
        type: 'pie',
        radius: '50%',
        data: [
          { value: getPendingServicesCount(), name: '待处理' },
          { value: getServiceStatusCount('PROCESSING'), name: '处理中' },
          { value: getServiceStatusCount('COMPLETED'), name: '已完成' },
          { value: getServiceStatusCount('CLOSED'), name: '已关闭' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
})

const monthlyTrendChartData = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新建工单', '完成工单']
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月', '4月', '5月', '6月']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新建工单',
        type: 'line',
        data: [12, 19, 15, 25, 22, 18]
      },
      {
        name: '完成工单',
        type: 'line',
        data: [10, 16, 13, 23, 20, 16]
      }
    ]
  }
})

// 工具函数
const {
  getStatusColor,
  getStatusText,
  formatDate: formatDateUtil
} = await import('~/utils/ui-helpers')

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'info':
      return 'i-heroicons-information-circle'
    case 'warning':
      return 'i-heroicons-exclamation-triangle'
    case 'error':
      return 'i-heroicons-x-circle'
    case 'success':
      return 'i-heroicons-check-circle'
    default:
      return 'i-heroicons-information-circle'
  }
}

// 本地格式化日期函数
const formatDate = formatDateUtil

// 获取待处理工单数量
const getPendingServicesCount = () => {
  if (!stats.value.services?.statusDistribution) return 0
  const pendingStatus = stats.value.services.statusDistribution.find((s: StatusDistribution) => s.status === 'PENDING')
  return pendingStatus?.count || 0
}

// 获取指定状态的工单数量
const getServiceStatusCount = (status: string) => {
  if (!stats.value.services?.statusDistribution) return 0
  const statusItem = stats.value.services.statusDistribution.find((s: StatusDistribution) => s.status === status)
  return statusItem?.count || 0
}

// 加载数据
const loadDashboardData = async () => {
  try {
    // 设置模拟数据用于测试布局
    stats.value = {
      customers: { totalCustomers: 25, recentCustomers: 5, levelDistribution: [], topIndustries: [] },
      archives: { totalArchives: 12, recentArchives: 3, statusDistribution: [], topTechnologies: [] },
      services: {
        totalServices: 48,
        recentServices: 8,
        avgResolutionTime: 24,
        statusDistribution: [
          { status: 'PENDING', count: 12 },
          { status: 'PROCESSING', count: 8 },
          { status: 'COMPLETED', count: 20 },
          { status: 'CLOSED', count: 8 }
        ],
        categoryDistribution: [],
        priorityDistribution: []
      },
      configurations: { totalConfigurations: 156, encryptedConfigurations: 89 }
    }

    recentServices.value = [
      {
        id: '1',
        title: '系统性能优化',
        status: 'processing',
        priority: 'high',
        customer: { name: '科技公司A' },
        createdAt: new Date().toISOString()
      },
      {
        id: '2',
        title: '数据库备份问题',
        status: 'pending',
        priority: 'medium',
        customer: { name: '制造企业B' },
        createdAt: new Date(Date.now() - 86400000).toISOString()
      }
    ]

    // TODO: 后续替换为真实的API调用
    // const [customersStats, archivesStats, servicesStats, configurationsStats, recentServicesData] = await Promise.all([
    //   $apiClient('/customers/stats'),
    //   $apiClient('/archives/stats'),
    //   $apiClient('/services/stats'),
    //   $apiClient('/configurations/stats'),
    //   $apiClient('/services', { query: { limit: 5, sort: 'createdAt', order: 'desc' } })
    // ])
  } catch (error) {
    // 静默处理错误，使用模拟数据
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await loadECharts()
  loadDashboardData()
})
</script>
