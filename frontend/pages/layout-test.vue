<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">布局测试页面</h1>
      <p class="mt-1 text-sm text-gray-500">
        测试页面布局是否正常显示
      </p>
    </div>

    <!-- 测试网格布局 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-users" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">测试卡片1</dt>
              <dd class="text-lg font-medium text-gray-900">100</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-folder" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">测试卡片2</dt>
              <dd class="text-lg font-medium text-gray-900">200</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-ticket" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">测试卡片3</dt>
              <dd class="text-lg font-medium text-gray-900">300</dd>
            </dl>
          </div>
        </div>
      </UCard>

      <UCard>
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <UIcon name="i-heroicons-cog-6-tooth" class="w-5 h-5 text-white" />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">测试卡片4</dt>
              <dd class="text-lg font-medium text-gray-900">400</dd>
            </dl>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 测试两列布局 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">左侧内容</h3>
        </template>
        
        <div class="space-y-3">
          <div class="p-3 bg-gray-50 rounded-lg">
            <p class="text-sm font-medium text-gray-900">测试内容1</p>
            <p class="text-sm text-gray-500">这是一些测试内容</p>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <p class="text-sm font-medium text-gray-900">测试内容2</p>
            <p class="text-sm text-gray-500">这是一些测试内容</p>
          </div>
        </div>
      </UCard>

      <UCard>
        <template #header>
          <h3 class="text-lg font-medium text-gray-900">右侧内容</h3>
        </template>
        
        <div class="space-y-3">
          <div class="p-3 bg-gray-50 rounded-lg">
            <p class="text-sm font-medium text-gray-900">测试内容A</p>
            <p class="text-sm text-gray-500">这是一些测试内容</p>
          </div>
          <div class="p-3 bg-gray-50 rounded-lg">
            <p class="text-sm font-medium text-gray-900">测试内容B</p>
            <p class="text-sm text-gray-500">这是一些测试内容</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 测试按钮和链接 -->
    <div class="space-y-4">
      <h3 class="text-lg font-medium text-gray-900">测试按钮和链接</h3>
      <div class="flex space-x-4">
        <UButton color="blue">蓝色按钮</UButton>
        <UButton color="green">绿色按钮</UButton>
        <UButton color="red">红色按钮</UButton>
        <NuxtLink to="/" class="text-blue-600 hover:text-blue-500">
          返回首页
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 页面元数据
definePageMeta({
  middleware: ['auth']
})

// 页面标题
useHead({
  title: '布局测试 - 运维管理系统'
})
</script>
