<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      title="审计日志"
      description="查看系统操作记录和用户活动日志"
    />

    <!-- 搜索和筛选 -->
    <UCard>
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索用户、操作或描述..."
            icon="i-heroicons-magnifying-glass"
            @input="debouncedSearch"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="actionFilter"
            :options="actionOptions"
            placeholder="操作类型"
            @change="loadLogs"
          />
          <USelect
            v-model="userFilter"
            :options="userOptions"
            placeholder="用户筛选"
            @change="loadLogs"
          />
          <UInputMenu
            v-model="dateRange"
            :options="dateRangeOptions"
            placeholder="时间范围"
            @change="loadLogs"
          />
        </div>
      </div>
    </UCard>

    <!-- 日志列表 -->
    <UCard>
      <div v-if="loading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"/>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作描述
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                资源类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                IP地址
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="log in logs" :key="log.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex items-center space-x-2">
                  <span class="w-2 h-2 rounded-full" :class="getActionIconColor(log.action)"/>
                  <span class="px-2 py-1 text-xs rounded-full" :class="getActionBadgeClass(log.action)">
                    {{ getActionLabel(log.action) }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ log.userName }}
              </td>
              <td class="px-6 py-4 text-sm text-gray-900">
                <div class="max-w-xs truncate" :title="log.description">
                  {{ log.description }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ log.resource }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ log.ipAddress || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDateTime(log.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <button
                  class="text-blue-600 hover:text-blue-800 text-xs"
                  @click="viewLogDetail(log)"
                >
                  查看
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        
        <!-- 分页 -->
        <div v-if="pagination.totalPages > 1" class="mt-4 flex justify-center">
          <nav class="flex space-x-2">
            <button
              v-for="page in pagination.totalPages"
              :key="page"
              class="px-3 py-1 text-sm border rounded"
              :class="page === pagination.page ? 'bg-blue-500 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'"
              @click="handlePageChange(page)"
            >
              {{ page }}
            </button>
          </nav>
        </div>
      </div>
    </UCard>

    <!-- 日志详情模态框 -->
    <UModal v-model="showDetailModal">
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">日志详情</h3>
            <UButton
              variant="ghost"
              icon="i-heroicons-x-mark"
              @click="showDetailModal = false"
            />
          </div>
        </template>

        <div v-if="selectedLog" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">用户</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedLog.userName }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">操作类型</label>
              <UBadge :color="getActionColor(selectedLog.action)" variant="soft">
                {{ getActionLabel(selectedLog.action) }}
              </UBadge>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">资源类型</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedLog.resource }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">资源ID</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedLog.resourceId || '-' }}</p>
            </div>
            
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700">操作描述</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedLog.description }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">IP地址</label>
              <p class="mt-1 text-sm text-gray-900">{{ selectedLog.ipAddress || '-' }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700">操作时间</label>
              <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(selectedLog.createdAt) }}</p>
            </div>
          </div>
          
          <!-- 用户代理信息 -->
          <div v-if="selectedLog.userAgent">
            <label class="block text-sm font-medium text-gray-700">用户代理</label>
            <p class="mt-1 text-xs text-gray-600 break-all">{{ selectedLog.userAgent }}</p>
          </div>
          
          <!-- 元数据 -->
          <div v-if="selectedLog.metadata && Object.keys(selectedLog.metadata).length > 0">
            <label class="block text-sm font-medium text-gray-700 mb-2">详细信息</label>
            <pre class="text-xs bg-gray-50 p-3 rounded overflow-auto max-h-40">{{ JSON.stringify(selectedLog.metadata, null, 2) }}</pre>
          </div>
        </div>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// import { PageHeader, DataTable } from '#components'
import type { AuditLog } from '~/types/api'

definePageMeta({
  middleware: ['auth', 'permission'],
  layout: 'admin'
})

useHead({
  title: '审计日志 - 系统管理'
})

// const authStore = useAuthStore()
const { $apiClient } = useNuxtApp()
const toast = useToast()

// 响应式数据
const loading = ref(false)
const logs = ref<AuditLog[]>([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

const searchQuery = ref('')
const actionFilter = ref('')
const userFilter = ref('')
const dateRange = ref('')

const showDetailModal = ref(false)
const selectedLog = ref<AuditLog | null>(null)

// 选项数据
const actionOptions = [
  { label: '全部操作', value: '' },
  { label: '用户登录', value: 'login' },
  { label: '用户登出', value: 'logout' },
  { label: '创建用户', value: 'user.create' },
  { label: '更新用户', value: 'user.update' },
  { label: '删除用户', value: 'user.delete' },
  { label: '创建角色', value: 'role.create' },
  { label: '更新角色', value: 'role.update' },
  { label: '删除角色', value: 'role.delete' },
  { label: '创建项目', value: 'project.create' },
  { label: '更新项目', value: 'project.update' },
  { label: '删除项目', value: 'project.delete' },
  { label: '创建工单', value: 'service.create' },
  { label: '更新工单', value: 'service.update' },
  { label: '删除工单', value: 'service.delete' }
]

const userOptions = ref([
  { label: '全部用户', value: '' }
])

const dateRangeOptions = [
  { label: '全部时间', value: '' },
  { label: '今天', value: 'today' },
  { label: '昨天', value: 'yesterday' },
  { label: '最近7天', value: 'week' },
  { label: '最近30天', value: 'month' }
]

const getActionBadgeClass = (action: string) => {
  const color = getActionColor(action)
  return `bg-${color}-100 text-${color}-800`
}

// 数据加载函数声明
const loadLogs = async () => {
  loading.value = true
  
  try {
    const params: Record<string, string | number> = {
      page: pagination.value.page,
      limit: pagination.value.limit
    }
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    if (actionFilter.value) {
      params.action = actionFilter.value
    }
    if (userFilter.value) {
      params.userId = userFilter.value
    }
    
    // 添加日期范围
    const dateParams = getDateRange(dateRange.value)
    Object.assign(params, dateParams)
    
    const response = await $apiClient('/audit-logs', { query: params })
    
    logs.value = response.data.items || response.data.logs
    pagination.value = {
      page: response.data.pagination.page,
      limit: response.data.pagination.limit,
      total: response.data.pagination.total,
      totalPages: response.data.pagination.totalPages
    }
  } catch (error) {
    // console.error('Load logs error:', error)
    toast.add({
      title: '加载失败',
      description: '获取审计日志失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 防抖搜索
const debouncedSearch = useDebounceFn(loadLogs, 300)

// 事件处理
const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadLogs()
}

const viewLogDetail = (log: AuditLog) => {
  selectedLog.value = log
  showDetailModal.value = true
}

// 辅助函数
const getActionLabel = (action: string) => {
  const labelMap: Record<string, string> = {
    'login': '登录',
    'logout': '登出',
    'user.create': '创建用户',
    'user.update': '更新用户',
    'user.delete': '删除用户',
    'role.create': '创建角色',
    'role.update': '更新角色',
    'role.delete': '删除角色',
    'project.create': '创建项目',
    'project.update': '更新项目',
    'project.delete': '删除项目',
    'service.create': '创建工单',
    'service.update': '更新工单',
    'service.delete': '删除工单',
    'customer.create': '创建客户',
    'customer.update': '更新客户',
    'customer.delete': '删除客户'
  }
  return labelMap[action] || action
}

const getActionColor = (action: string) => {
  if (action.includes('create')) return 'green'
  if (action.includes('update')) return 'blue'
  if (action.includes('delete')) return 'red'
  if (action === 'login') return 'green'
  if (action === 'logout') return 'gray'
  return 'gray'
}

// const getActionIcon = (action: string) => {
//   if (action.includes('create')) return 'i-heroicons-plus'
//   if (action.includes('update')) return 'i-heroicons-pencil'
//   if (action.includes('delete')) return 'i-heroicons-trash'
//   if (action === 'login') return 'i-heroicons-arrow-right-on-rectangle'
//   if (action === 'logout') return 'i-heroicons-arrow-left-on-rectangle'
//   return 'i-heroicons-information-circle'
// }

const getActionIconColor = (action: string) => {
  if (action.includes('create')) return 'text-green-500'
  if (action.includes('update')) return 'text-blue-500'
  if (action.includes('delete')) return 'text-red-500'
  if (action === 'login') return 'text-green-500'
  if (action === 'logout') return 'text-gray-500'
  return 'text-gray-500'
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getDateRange = (range: string) => {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  
  switch (range) {
    case 'today': {
      return {
        startDate: today.toISOString(),
        endDate: new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString()
      }
    }
    case 'yesterday': {
      const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
      return {
        startDate: yesterday.toISOString(),
        endDate: today.toISOString()
      }
    }
    case 'week': {
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
      return {
        startDate: weekAgo.toISOString(),
        endDate: now.toISOString()
      }
    }
    case 'month': {
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      return {
        startDate: monthAgo.toISOString(),
        endDate: now.toISOString()
      }
    }
    default:
      return {}
  }
}

const loadUsers = async () => {
  try {
    const response = await $apiClient('/users', { query: { limit: 1000 } })
    const users = response.data.users || response.data.items
    
    userOptions.value = [
      { label: '全部用户', value: '' },
      ...users.map((user: { fullName?: string; username: string; id: string }) => ({
        label: user.fullName || user.username,
        value: user.id
      }))
    ]
  } catch (error) {
    // console.error('Load users error:', error)
  }
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadLogs(),
    loadUsers()
  ])
})
</script>