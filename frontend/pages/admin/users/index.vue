<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <PageHeader
      title="用户管理"
      description="管理系统中的所有用户账户"
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 搜索和筛选 -->
    <UCard>
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索用户名、邮箱或姓名..."
            icon="i-heroicons-magnifying-glass"
            @input="debouncedSearch"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="roleFilter"
            :options="roleOptions"
            placeholder="角色筛选"
            @change="loadUsers"
          />
          <USelect
            v-model="statusFilter"
            :options="statusOptions"
            placeholder="状态筛选"
            @change="loadUsers"
          />
          <USelect
            v-model="departmentFilter"
            :options="departmentOptions"
            placeholder="部门筛选"
            @change="loadUsers"
          />
        </div>
      </div>
    </UCard>

    <!-- 用户列表 -->
    <UCard>
      <DataTable
        :loading="loading"
        :data="users"
        :columns="columns"
        :pagination="pagination"
        :actions="getActionItems"
        @action="handleTableAction"
        @page-change="handlePageChange"
      />
    </UCard>

    <!-- 新增用户模态框 -->
    <UModal v-model="showCreateModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">新增用户</h3>
        </template>
        
        <form @submit.prevent="handleCreate">
      <form @submit.prevent="handleCreate">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="用户名" required>
            <UInput
              v-model="createForm.username"
              placeholder="请输入用户名"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="邮箱" required>
            <UInput
              v-model="createForm.email"
              type="email"
              placeholder="请输入邮箱"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="姓名" required>
            <UInput
              v-model="createForm.fullName"
              placeholder="请输入姓名"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="手机号">
            <UInput
              v-model="createForm.phone"
              placeholder="请输入手机号"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="角色" required>
            <USelect
              v-model="createForm.roleId"
              :options="roleOptions"
              placeholder="请选择角色"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="部门">
            <USelect
              v-model="createForm.department"
              :options="departmentOptions"
              placeholder="请选择部门"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="密码" required class="md:col-span-2">
            <UInput
              v-model="createForm.password"
              type="password"
              placeholder="请输入密码"
              :disabled="createForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="状态" class="md:col-span-2">
            <UToggle
              v-model="createForm.isActive"
              :disabled="createForm.loading"
            />
            <span class="ml-2 text-sm text-gray-600">
              {{ createForm.isActive ? '激活' : '禁用' }}
            </span>
          </UFormGroup>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="handleCreateCancel"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="createForm.loading"
          >
            创建用户
          </UButton>
        </div>
      </form>
        </form></UCard>
      </UModal>

    <!-- 编辑用户模态框 -->
    <UModal v-model="showEditModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">编辑用户</h3>
        </template>
        
        <form @submit.prevent="handleUpdate">
      <form @submit.prevent="handleUpdate">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="用户名" required>
            <UInput
              v-model="editForm.username"
              placeholder="请输入用户名"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="邮箱" required>
            <UInput
              v-model="editForm.email"
              type="email"
              placeholder="请输入邮箱"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="姓名" required>
            <UInput
              v-model="editForm.fullName"
              placeholder="请输入姓名"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="手机号">
            <UInput
              v-model="editForm.phone"
              placeholder="请输入手机号"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="角色" required>
            <USelect
              v-model="editForm.roleId"
              :options="roleOptions"
              placeholder="请选择角色"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="部门">
            <USelect
              v-model="editForm.department"
              :options="departmentOptions"
              placeholder="请选择部门"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="状态" class="md:col-span-2">
            <UToggle
              v-model="editForm.isActive"
              :disabled="editForm.loading"
            />
            <span class="ml-2 text-sm text-gray-600">
              {{ editForm.isActive ? '激活' : '禁用' }}
            </span>
          </UFormGroup>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="handleEditCancel"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="editForm.loading"
          >
            更新用户
          </UButton>
        </div>
      </form>
        </form></UCard>
      </UModal>

    <!-- 重置密码模态框 -->
    <UModal v-model="showPasswordModal" :ui="{ width: 'sm:max-w-md' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">重置密码</h3>
        </template>
        
        <form @submit.prevent="handleResetPassword">
      <form @submit.prevent="handleResetPassword">
        <UFormGroup label="新密码" required>
          <UInput
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            :disabled="passwordForm.loading"
          />
        </UFormGroup>

        <UFormGroup label="确认密码" required>
          <UInput
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            :disabled="passwordForm.loading"
          />
        </UFormGroup>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="showPasswordModal = false"
          >
            取消
          </UButton>
                      <UButton
              type="submit"
              :loading="passwordForm.loading"
            >
              重置密码
            </UButton>
          </div>
        </form>
        </form></UCard>
      </UModal>

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="删除用户"
      :message="`确定要删除用户 ${selectedUser?.fullName || ''} 吗？此操作不可撤销。`"
      confirm-text="删除"
      confirm-color="red"
      :loading="deleteLoading"
      @confirm="handleDelete"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import { h } from 'vue'
// import { UAvatar, UBadge } from '#components'  // 暂时注释
import type { User } from '~/types/api'
import type { ActionItem } from '~/types/ui'
import { isSuccessResponse } from '~/types/api'

definePageMeta({
  middleware: ['auth', 'permission'],
  layout: 'admin'
})

useHead({
  title: '用户管理 - 系统管理'
})

const authStore = useAuthStore()
const { $apiClient } = useNuxtApp()
const toast = useToast()

// 响应式数据
const loading = ref(false)
const users = ref<User[]>([])
const pagination = ref({
  page: 1,
  limit: 20,
  total: 0,
  totalPages: 0
})

const searchQuery = ref('')
const roleFilter = ref('')
const statusFilter = ref('')
const departmentFilter = ref('')

const showCreateModal = ref(false)
const showEditModal = ref(false)
const showPasswordModal = ref(false)
const showDeleteModal = ref(false)
const deleteLoading = ref(false)

const selectedUser = ref<User | null>(null)

// 表单数据
const createForm = reactive({
  username: '',
  email: '',
  fullName: '',
  phone: '',
  roleId: '',
  department: '',
  password: '',
  isActive: true,
  loading: false
})

const editForm = reactive({
  username: '',
  email: '',
  fullName: '',
  phone: '',
  roleId: '',
  department: '',
  isActive: true,
  loading: false
})

const passwordForm = reactive({
  newPassword: '',
  confirmPassword: '',
  loading: false
})

const createFormFields = [
  { name: 'username', label: '用户名', type: 'text', required: true },
  { name: 'email', label: '邮箱', type: 'email', required: true },
  { name: 'fullName', label: '姓名', type: 'text', required: true },
  { name: 'phone', label: '手机号', type: 'text' },
  { name: 'roleId', label: '角色', type: 'select', options: [], required: true },
  { name: 'department', label: '部门', type: 'select', options: [] },
  { name: 'password', label: '密码', type: 'password', required: true },
  { name: 'isActive', label: '状态', type: 'checkbox' },
]

const createFormSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  email: z.string().email('请输入有效的邮箱地址'),
  fullName: z.string().min(1, '姓名不能为空'),
  phone: z.string().optional(),
  roleId: z.string().min(1, '请选择角色'),
  department: z.string().optional(),
  password: z.string().min(6, '密码至少需要6个字符'),
  isActive: z.boolean(),
})

const editFormFields = [
  { name: 'username', label: '用户名', type: 'text', required: true },
  { name: 'email', label: '邮箱', type: 'email', required: true },
  { name: 'fullName', label: '姓名', type: 'text', required: true },
  { name: 'phone', label: '手机号', type: 'text' },
  { name: 'roleId', label: '角色', type: 'select', options: [], required: true },
  { name: 'department', label: '部门', type: 'select', options: [] },
  { name: 'isActive', label: '状态', type: 'checkbox' },
]

const editFormSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  email: z.string().email('请输入有效的邮箱地址'),
  fullName: z.string().min(1, '姓名不能为空'),
  phone: z.string().optional(),
  roleId: z.string().min(1, '请选择角色'),
  department: z.string().optional(),
  isActive: z.boolean(),
})

const passwordFormFields = [
  { name: 'newPassword', label: '新密码', type: 'password', required: true },
  { name: 'confirmPassword', label: '确认密码', type: 'password', required: true },
]

const passwordFormSchema = z.object({
  newPassword: z.string().min(6, '密码至少需要6个字符'),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})
const roleOptions = ref<Array<{ label: string; value: string }>>([])
const departmentOptions = ref<Array<{ label: string; value: string }>>([])

const statusOptions = [
  { label: '全部', value: '' },
  { label: '激活', value: 'true' },
  { label: '禁用', value: 'false' }
]

// 页面操作
const headerActions = computed(() => {
  const actions: ActionItem[] = []
  
  if (authStore.hasPermission('user:create')) {
    actions.push({
      key: 'create',
      label: '新增用户',
      icon: 'i-heroicons-plus',
      color: 'primary'
    })
  }
  
  if (authStore.hasPermission('user:read')) {
    actions.push({
      key: 'export',
      label: '导出用户',
      icon: 'i-heroicons-arrow-down-tray',
      color: 'gray'
    })
  }
  
  return actions
})

// 表格列定义
const columns = [
  {
    key: 'avatar',
    label: '头像',
    render: (row: User) => h('div', { class: 'flex items-center' }, [
      h('div', { class: 'w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center' }, [
        h('span', { class: 'text-xs font-medium text-gray-600' }, row.fullName?.charAt(0) || 'U')
      ])
    ])
  },
  {
    key: 'username',
    label: '用户名',
    sortable: true
  },
  {
    key: 'fullName',
    label: '姓名',
    sortable: true
  },
  {
    key: 'email',
    label: '邮箱',
    sortable: true
  },
  {
    key: 'role',
    label: '角色',
    render: (row: User) => row.role?.name || '-'
  },
  {
    key: 'department',
    label: '部门',
    render: (row: User) => row.department || '-'
  },
  {
    key: 'isActive',
    label: '状态',
    render: (row: User) => h('span', {
      class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
        row.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
      }`
    }, row.isActive ? '激活' : '禁用')
  },
  {
    key: 'lastLoginAt',
    label: '最后登录',
    render: (row: User) => row.lastLoginAt ? 
      new Date(row.lastLoginAt).toLocaleDateString('zh-CN') : '-'
  },
  {
    key: 'createdAt',
    label: '创建时间',
    render: (row: User) => new Date(row.createdAt).toLocaleDateString('zh-CN'),
    sortable: true
  }
]

// 获取操作项
const getActionItems = (row: User): ActionItem[] => {
  const items: ActionItem[] = []
  
  if (authStore.hasPermission('user:read')) {
    items.push({
      key: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('user:update')) {
    items.push({
      key: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
    
    items.push({
      key: 'reset-password',
      label: '重置密码',
      icon: 'i-heroicons-key'
    })
    
    items.push({
      key: 'toggle-status',
      label: row.isActive ? '禁用用户' : '启用用户',
      icon: row.isActive ? 'i-heroicons-lock-closed' : 'i-heroicons-lock-open',
      color: row.isActive ? 'red' : 'green'
    })
  }
  
  if (authStore.hasPermission('user:delete') && row.id !== authStore.user?.id) {
    items.push({
      key: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red'
    })
  }
  
  return items
}

// 数据加载函数声明
const loadUsers = async () => {
  loading.value = true
  
  try {
    const params: Record<string, unknown> = {
      page: pagination.value.page,
      limit: pagination.value.limit
    }
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    if (roleFilter.value) {
      params.roleId = roleFilter.value
    }
    if (statusFilter.value) {
      params.isActive = statusFilter.value
    }
    if (departmentFilter.value) {
      params.department = departmentFilter.value
    }
    
    const response = await $apiClient('/users', { query: params })
    
    users.value = response.data.users || response.data.items
    pagination.value = {
      page: response.data.pagination.page,
      limit: response.data.pagination.limit,
      total: response.data.pagination.total,
      totalPages: response.data.pagination.totalPages
    }
  } catch (error) {
    console.error('Load users error:', error)
    toast.add({
      title: '加载失败',
      description: '获取用户列表失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 防抖搜索
const debouncedSearch = useDebounceFn(loadUsers, 300)

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'create':
      showCreateModal.value = true
      break
    case 'export':
      handleExport()
      break
  }
}

const handleTableAction = (action: string, row: User) => {
  selectedUser.value = row
  
  switch (action) {
    case 'view':
      navigateTo(`/admin/users/${row.id}`)
      break
    case 'edit':
      Object.assign(editForm, {
        username: row.username,
        email: row.email,
        fullName: row.fullName,
        phone: row.phone || '',
        roleId: row.role?.id || '',
        department: row.department || '',
        isActive: row.isActive
      })
      showEditModal.value = true
      break
    case 'reset-password':
      passwordForm.newPassword = ''
      passwordForm.confirmPassword = ''
      showPasswordModal.value = true
      break
    case 'toggle-status':
      toggleUserStatus(row)
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadUsers()
}

// CRUD操作
const handleCreate = async () => {
  createForm.loading = true
  
  try {
    await $apiClient('/users', {
      method: 'POST',
      body: {
        username: createForm.username,
        email: createForm.email,
        fullName: createForm.fullName,
        phone: createForm.phone || undefined,
        roleId: createForm.roleId,
        department: createForm.department || undefined,
        password: createForm.password,
        isActive: createForm.isActive
      }
    })
    
    toast.add({
      title: '创建成功',
      description: '用户创建成功',
      color: 'green'
    })
    
    showCreateModal.value = false
    resetCreateForm()
    loadUsers()
  } catch (error: any) {
    toast.add({
      title: '创建失败',
      description: error.message || '用户创建失败',
      color: 'red'
    })
  } finally {
    createForm.loading = false
  }
}

const handleUpdate = async () => {
  if (!selectedUser.value) return
  
  editForm.loading = true
  
  try {
    await $apiClient(`/users/${selectedUser.value.id}`, {
      method: 'PUT',
      body: {
        username: editForm.username,
        email: editForm.email,
        fullName: editForm.fullName,
        phone: editForm.phone || undefined,
        roleId: editForm.roleId,
        department: editForm.department || undefined,
        isActive: editForm.isActive
      }
    })
    
    toast.add({
      title: '更新成功',
      description: '用户信息更新成功',
      color: 'green'
    })
    
    showEditModal.value = false
    resetEditForm()
    loadUsers()
  } catch (error: any) {
    toast.add({
      title: '更新失败',
      description: error.message || '用户信息更新失败',
      color: 'red'
    })
  } finally {
    editForm.loading = false
  }
}

const handleResetPassword = async () => {
  if (!selectedUser.value) return
  
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    toast.add({
      title: '密码不匹配',
      description: '两次输入的密码不一致',
      color: 'red'
    })
    return
  }
  
  passwordForm.loading = true
  
  try {
    await $apiClient(`/users/${selectedUser.value.id}/reset-password`, {
      method: 'POST',
      body: {
        newPassword: passwordForm.newPassword
      }
    })
    
    toast.add({
      title: '重置成功',
      description: '密码重置成功',
      color: 'green'
    })
    
    showPasswordModal.value = false
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
  } catch (error: any) {
    toast.add({
      title: '重置失败',
      description: error.message || '密码重置失败',
      color: 'red'
    })
  } finally {
    passwordForm.loading = false
  }
}

const handleDelete = async () => {
  if (!selectedUser.value) return
  
  deleteLoading.value = true
  
  try {
    await $apiClient(`/users/${selectedUser.value.id}`, {
      method: 'DELETE'
    })
    
    toast.add({
      title: '删除成功',
      description: '用户删除成功',
      color: 'green'
    })
    
    showDeleteModal.value = false
    selectedUser.value = null
    loadUsers()
  } catch (error: any) {
    toast.add({
      title: '删除失败',
      description: error.message || '用户删除失败',
      color: 'red'
    })
  } finally {
    deleteLoading.value = false
  }
}

const toggleUserStatus = async (user: User) => {
  try {
    await $apiClient(`/users/${user.id}/toggle-status`, {
      method: 'POST'
    })
    
    toast.add({
      title: '状态更新',
      description: `用户已${user.isActive ? '禁用' : '启用'}`,
      color: 'green'
    })
    
    loadUsers()
  } catch (error: any) {
    toast.add({
      title: '操作失败',
      description: error.message || '状态更新失败',
      color: 'red'
    })
  }
}

const handleExport = () => {
  toast.add({
    title: '功能开发中',
    description: '用户导出功能正在开发中',
    color: 'blue'
  })
}

// 表单重置
const resetCreateForm = () => {
  Object.assign(createForm, {
    username: '',
    email: '',
    fullName: '',
    phone: '',
    roleId: '',
    department: '',
    password: '',
    isActive: true
  })
}

const resetEditForm = () => {
  Object.assign(editForm, {
    username: '',
    email: '',
    fullName: '',
    phone: '',
    roleId: '',
    department: '',
    isActive: true
  })
}

const handleCreateCancel = () => {
  showCreateModal.value = false
  resetCreateForm()
}

const handleEditCancel = () => {
  showEditModal.value = false
  resetEditForm()
  selectedUser.value = null
}



const loadRoles = async () => {
  try {
    const response = await $apiClient('/roles/all')
    roleOptions.value = [
      { label: '全部角色', value: '' },
      ...response.data.map((role: any) => ({
        label: role.description || role.name,
        value: role.id
      }))
    ]
  } catch (error) {
    console.error('Load roles error:', error)
  }
}

const loadDepartments = async () => {
  try {
    const response = await apiClient.get('/users/departments')
    if (isSuccessResponse(response)) {
      departmentOptions.value = [
        { label: '全部部门', value: '' },
        ...(response.data as string[]).map((dept: string) => ({
          label: dept,
          value: dept
        }))
      ]
    }
  } catch (error) {
    console.error('Load departments error:', error)
    // 如果API调用失败，使用默认部门选项
    departmentOptions.value = [
      { label: '全部部门', value: '' },
      { label: '技术部', value: '技术部' },
      { label: '销售部', value: '销售部' },
      { label: '市场部', value: '市场部' },
      { label: '人事部', value: '人事部' },
      { label: '财务部', value: '财务部' },
      { label: '运营部', value: '运营部' }
    ]
  }
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadUsers(),
    loadRoles(),
    loadDepartments()
  ])
  
  // 检查URL参数是否有创建用户的操作
  const route = useRoute()
  if (route.query.action === 'create' && authStore.hasPermission('user:create')) {
    showCreateModal.value = true
  }
})
</script>