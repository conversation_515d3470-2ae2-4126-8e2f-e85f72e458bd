<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <UButton
          variant="ghost"
          icon="i-heroicons-arrow-left"
          @click="navigateTo('/admin/users')"
        >
          返回用户列表
        </UButton>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">用户详情</h1>
          <p class="mt-1 text-sm text-gray-500">
            查看和管理用户的详细信息
          </p>
        </div>
      </div>
      
      <div class="flex space-x-3">
        <UButton
          v-if="authStore.hasPermission('user:update')"
          variant="outline"
          icon="i-heroicons-pencil"
          @click="showEditModal = true"
        >
          编辑用户
        </UButton>
        
        <UButton
          v-if="authStore.hasPermission('user:update')"
          variant="outline"
          icon="i-heroicons-key"
          @click="showPasswordModal = true"
        >
          重置密码
        </UButton>
      </div>
    </div>

    <!-- 用户信息卡片 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 基本信息 -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <h2 class="text-lg font-medium text-gray-900">基本信息</h2>
          </template>
          
          <div v-if="loading" class="animate-pulse">
            <div class="space-y-4">
              <div class="h-4 bg-gray-200 rounded w-3/4"/>
              <div class="h-4 bg-gray-200 rounded w-1/2"/>
              <div class="h-4 bg-gray-200 rounded w-2/3"/>
            </div>
          </div>
          
          <div v-else-if="user" class="space-y-6">
            <!-- 头像和基本信息 -->
            <div class="flex items-start space-x-6">
              <div class="flex-shrink-0">
                <div class="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-xl font-bold text-gray-600">
                    {{ user.fullName?.charAt(0) || user.username?.charAt(0) }}
                  </span>
                </div>
              </div>
              
              <div class="flex-1 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700">用户名</label>
                    <p class="mt-1 text-sm text-gray-900">{{ user.username }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">姓名</label>
                    <p class="mt-1 text-sm text-gray-900">{{ user.fullName }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">邮箱</label>
                    <p class="mt-1 text-sm text-gray-900">{{ user.email }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">手机号</label>
                    <p class="mt-1 text-sm text-gray-900">{{ user.phone || '-' }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">部门</label>
                    <p class="mt-1 text-sm text-gray-900">{{ user.department || '-' }}</p>
                  </div>
                  
                  <div>
                    <label class="block text-sm font-medium text-gray-700">状态</label>
                    <UBadge
                      :color="user.isActive ? 'green' : 'red'"
                      variant="soft"
                      class="mt-1"
                    >
                      {{ user.isActive ? '激活' : '禁用' }}
                    </UBadge>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 时间信息 -->
            <div class="border-t border-gray-200 pt-4">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700">创建时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(user.createdAt) }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">更新时间</label>
                  <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(user.updatedAt) }}</p>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700">最后登录</label>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ user.lastLoginAt ? formatDateTime(user.lastLoginAt) : '从未登录' }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 角色权限 -->
      <div>
        <UCard>
          <template #header>
            <h2 class="text-lg font-medium text-gray-900">角色权限</h2>
          </template>
          
          <div v-if="loading" class="animate-pulse">
            <div class="space-y-3">
              <div class="h-4 bg-gray-200 rounded"/>
              <div class="h-3 bg-gray-200 rounded w-3/4"/>
              <div class="h-3 bg-gray-200 rounded w-1/2"/>
            </div>
          </div>
          
          <div v-else-if="user" class="space-y-4">
            <!-- 角色信息 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">分配角色</label>
              <div class="flex items-center space-x-2">
                <UIcon name="i-heroicons-shield-check" class="w-5 h-5 text-blue-500" />
                <span class="text-sm font-medium text-gray-900">{{ user.role?.name || '-' }}</span>
              </div>
              <p class="text-xs text-gray-500 mt-1">{{ user.role?.description || '-' }}</p>
            </div>
            
            <!-- 权限列表 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">权限列表</label>
              <div class="space-y-2 max-h-60 overflow-y-auto">
                <div
                  v-for="permission in user.role?.permissions"
                  :key="permission"
                  class="flex items-center space-x-2 p-2 bg-gray-50 rounded"
                >
                  <UIcon name="i-heroicons-check" class="w-4 h-4 text-green-500" />
                  <span class="text-xs text-gray-700">{{ getPermissionLabel(permission) }}</span>
                </div>
                
                <div v-if="!user.role?.permissions?.length" class="text-center py-4">
                  <UIcon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-gray-400 mx-auto mb-2" />
                  <p class="text-sm text-gray-500">暂无权限</p>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 活动统计 -->
    <UCard v-if="user">
      <template #header>
        <h2 class="text-lg font-medium text-gray-900">活动统计</h2>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-2xl font-bold text-blue-600">{{ stats.totalLogins }}</div>
          <div class="text-sm text-gray-500">总登录次数</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-green-600">{{ stats.totalProjects }}</div>
          <div class="text-sm text-gray-500">参与项目</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-purple-600">{{ stats.totalServices }}</div>
          <div class="text-sm text-gray-500">处理工单</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-yellow-600">{{ stats.avgResponseTime }}</div>
          <div class="text-sm text-gray-500">平均响应时间(小时)</div>
        </div>
      </div>
    </UCard>

    <!-- 最近活动日志 -->
    <UCard v-if="user && authStore.hasPermission('audit:read')">
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-medium text-gray-900">最近活动</h2>
          <UButton
            variant="ghost"
            size="sm"
            to="/admin/audit-logs"
          >
            查看全部
          </UButton>
        </div>
      </template>
      
      <div class="space-y-4">
        <div
          v-for="log in recentLogs"
          :key="log.id"
          class="flex items-start space-x-3 p-3 rounded-lg bg-gray-50"
        >
          <UIcon
            :name="getLogIcon(log.action)"
            :class="getLogColor(log.action)"
            class="w-4 h-4 mt-0.5"
          />
          <div class="flex-1 min-w-0">
            <p class="text-sm text-gray-900">{{ log.description }}</p>
            <p class="text-xs text-gray-500">{{ formatDateTime(log.createdAt) }}</p>
          </div>
        </div>
        
        <div v-if="!recentLogs.length" class="text-center py-6">
          <UIcon name="i-heroicons-clock" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p class="text-sm text-gray-500">暂无活动记录</p>
        </div>
      </div>
    </UCard>

    <!-- 编辑用户模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑用户"
      :fields="editFormFields"
      :schema="editFormSchema"
      :form-state="editForm"
      @submit="handleUpdate"
      @cancel="showEditModal = false"
    >
      <form @submit.prevent="handleUpdate">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <UFormGroup label="用户名" required>
            <UInput
              v-model="editForm.username"
              placeholder="请输入用户名"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="邮箱" required>
            <UInput
              v-model="editForm.email"
              type="email"
              placeholder="请输入邮箱"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="姓名" required>
            <UInput
              v-model="editForm.fullName"
              placeholder="请输入姓名"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="手机号">
            <UInput
              v-model="editForm.phone"
              placeholder="请输入手机号"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="角色" required>
            <USelect
              v-model="editForm.roleId"
              :options="roleOptions"
              placeholder="请选择角色"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="部门">
            <UInput
              v-model="editForm.department"
              placeholder="请输入部门"
              :disabled="editForm.loading"
            />
          </UFormGroup>

          <UFormGroup label="状态" class="md:col-span-2">
            <UToggle
              v-model="editForm.isActive"
              :disabled="editForm.loading"
            />
            <span class="ml-2 text-sm text-gray-600">
              {{ editForm.isActive ? '激活' : '禁用' }}
            </span>
          </UFormGroup>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="showEditModal = false"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="editForm.loading"
          >
            更新用户
          </UButton>
        </div>
      </form>
    </FormModal>

    <!-- 重置密码模态框 -->
    <FormModal
      v-model="showPasswordModal"
      title="重置密码"
      :fields="passwordFormFields"
      :schema="passwordFormSchema"
      :form-state="passwordForm"
      @submit="handleResetPassword"
      @cancel="showPasswordModal = false"
    >
      <form @submit.prevent="handleResetPassword">
        <UFormGroup label="新密码" required>
          <UInput
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            :disabled="passwordForm.loading"
          />
        </UFormGroup>

        <UFormGroup label="确认密码" required>
          <UInput
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请确认新密码"
            :disabled="passwordForm.loading"
          />
        </UFormGroup>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="showPasswordModal = false"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="passwordForm.loading"
          >
            重置密码
          </UButton>
        </div>
      </form>
    </FormModal>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { User, AuditLog } from '~/types/api'
import type { FormField, InputField, SelectField, CheckboxField } from '~/types/ui'

definePageMeta({
  middleware: ['auth', 'permission'],
  layout: 'admin'
})

const authStore = useAuthStore()
const { $apiClient } = useNuxtApp()
const toast = useToast()
const route = useRoute()

const userId = route.params.id as string

// 响应式数据
const loading = ref(false)
const user = ref<User | null>(null)
const stats = ref({
  totalLogins: 0,
  totalProjects: 0,
  totalServices: 0,
  avgResponseTime: 0
})
const recentLogs = ref<AuditLog[]>([])

const showEditModal = ref(false)
const showPasswordModal = ref(false)

const roleOptions = ref([])

// 表单数据
const editForm = reactive({
  username: '',
  email: '',
  fullName: '',
  phone: '',
  roleId: '',
  department: '',
  isActive: true,
  loading: false
})

const passwordForm = reactive({
  newPassword: '',
  confirmPassword: '',
  loading: false
})

// 计算属性
const editFormFields: FormField[] = [
  { name: 'username', label: '用户名', type: 'text', required: true } as InputField,
  { name: 'email', label: '邮箱', type: 'email', required: true } as InputField,
  { name: 'fullName', label: '姓名', type: 'text', required: true } as InputField,
  { name: 'phone', label: '手机号', type: 'text' } as InputField,
  { name: 'roleId', label: '角色', type: 'select', options: roleOptions.value, required: true } as SelectField,
  { name: 'department', label: '部门', type: 'text' } as InputField,
  { name: 'isActive', label: '状态', type: 'checkbox' } as CheckboxField,
]

const editFormSchema = z.object({
  username: z.string().min(1, '用户名不能为空'),
  email: z.string().email('请输入有效的邮箱地址'),
  fullName: z.string().min(1, '姓名不能为空'),
  phone: z.string().optional(),
  roleId: z.string().min(1, '请选择角色'),
  department: z.string().optional(),
  isActive: z.boolean(),
})

const passwordFormFields: FormField[] = [
  { name: 'newPassword', label: '新密码', type: 'password', required: true } as InputField,
  { name: 'confirmPassword', label: '确认密码', type: 'password', required: true } as InputField,
]

const passwordFormSchema = z.object({
  newPassword: z.string().min(6, '密码至少需要6个字符'),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

const pageTitle = computed(() => {
  return user.value ? `${user.value.fullName} - 用户详情` : '用户详情'
})

// 页面标题
useHead({
  title: pageTitle
})

// 方法
const loadUser = async () => {
  loading.value = true
  
  try {
    const response = await $apiClient(`/users/${userId}`)
    user.value = response.data
    
    // 预填充编辑表单
    if (user.value) {
      Object.assign(editForm, {
        username: user.value.username,
        email: user.value.email,
        fullName: user.value.fullName,
        phone: user.value.phone || '',
        roleId: user.value.role?.id || '',
        department: user.value.department || '',
        isActive: user.value.isActive
      })
    }
  } catch (error: unknown) {
    // console.error('Load user error:', error)
    toast.add({
      title: '加载失败',
      description: error instanceof Error ? error.message : '获取用户信息失败',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

const loadUserStats = async () => {
  try {
    const response = await $apiClient(`/users/${userId}/stats`)
    stats.value = response.data
  } catch (error) {
    // console.error('Load user stats error:', error)
  }
}

const loadRecentLogs = async () => {
  if (!authStore.hasPermission('audit:read')) return
  
  try {
    const response = await $apiClient('/audit-logs', {
      query: {
        userId,
        limit: 10,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }
    })
    recentLogs.value = response.data.items || response.data.logs || []
  } catch (error) {
    // console.error('Load recent logs error:', error)
  }
}

const loadRoles = async () => {
  try {
    const response = await $apiClient('/roles/all')
    roleOptions.value = response.data.map((role: any) => ({
      label: role.description || role.name,
      value: role.id
    }))
  } catch (error) {
    // console.error('Load roles error:', error)
  }
}

const handleUpdate = async () => {
  editForm.loading = true
  
  try {
    await $apiClient(`/users/${userId}`, {
      method: 'PUT',
      body: {
        username: editForm.username,
        email: editForm.email,
        fullName: editForm.fullName,
        phone: editForm.phone || undefined,
        roleId: editForm.roleId,
        department: editForm.department || undefined,
        isActive: editForm.isActive
      }
    })
    
    toast.add({
      title: '更新成功',
      description: '用户信息更新成功',
      color: 'green'
    })
    
    showEditModal.value = false
    await loadUser()
  } catch (error: any) {
    toast.add({
      title: '更新失败',
      description: error.message || '用户信息更新失败',
      color: 'red'
    })
  } finally {
    editForm.loading = false
  }
}

const handleResetPassword = async () => {
  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    toast.add({
      title: '密码不匹配',
      description: '两次输入的密码不一致',
      color: 'red'
    })
    return
  }
  
  passwordForm.loading = true
  
  try {
    await $apiClient(`/users/${userId}/reset-password`, {
      method: 'POST',
      body: {
        newPassword: passwordForm.newPassword
      }
    })
    
    toast.add({
      title: '重置成功',
      description: '密码重置成功',
      color: 'green'
    })
    
    showPasswordModal.value = false
    passwordForm.newPassword = ''
    passwordForm.confirmPassword = ''
  } catch (error: any) {
    toast.add({
      title: '重置失败',
      description: error.message || '密码重置失败',
      color: 'red'
    })
  } finally {
    passwordForm.loading = false
  }
}

// 辅助函数
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getPermissionLabel = (permission: string) => {
  // 这里可以从后端获取权限描述映射
  const permissionMap: Record<string, string> = {
    'admin:all': '系统管理员',
    'user:read': '查看用户',
    'user:create': '创建用户',
    'user:update': '编辑用户',
    'user:delete': '删除用户',
    'role:read': '查看角色',
    'role:create': '创建角色',
    'role:update': '编辑角色',
    'role:delete': '删除角色',
    'customer:read': '查看客户',
    'customer:create': '创建客户',
    'customer:update': '编辑客户',
    'customer:delete': '删除客户',
    'archive:read': '查看项目',
    'archive:create': '创建项目',
    'archive:update': '编辑项目',
    'archive:delete': '删除项目',
    'service:read': '查看工单',
    'service:create': '创建工单',
    'service:update': '编辑工单',
    'service:delete': '删除工单',
    'audit:read': '查看日志'
  }
  
  return permissionMap[permission] || permission
}

const getLogIcon = (action: string) => {
  const iconMap: Record<string, string> = {
    'user.create': 'i-heroicons-user-plus',
    'user.update': 'i-heroicons-user',
    'user.delete': 'i-heroicons-user-minus',
    'role.create': 'i-heroicons-shield-plus',
    'role.update': 'i-heroicons-shield-check',
    'role.delete': 'i-heroicons-shield-exclamation',
    'login': 'i-heroicons-arrow-right-on-rectangle',
    'logout': 'i-heroicons-arrow-left-on-rectangle',
    'project.create': 'i-heroicons-folder-plus',
    'project.update': 'i-heroicons-folder',
    'service.create': 'i-heroicons-ticket',
    'service.update': 'i-heroicons-wrench'
  }
  return iconMap[action] || 'i-heroicons-information-circle'
}

const getLogColor = (action: string) => {
  const colorMap: Record<string, string> = {
    'user.create': 'text-green-500',
    'user.update': 'text-blue-500',
    'user.delete': 'text-red-500',
    'role.create': 'text-purple-500',
    'role.update': 'text-purple-500',
    'role.delete': 'text-red-500',
    'login': 'text-green-500',
    'logout': 'text-gray-500',
    'project.create': 'text-blue-500',
    'project.update': 'text-blue-500',
    'service.create': 'text-yellow-500',
    'service.update': 'text-yellow-500'
  }
  return colorMap[action] || 'text-gray-500'
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadUser(),
    loadUserStats(),
    loadRecentLogs(),
    loadRoles()
  ])
})
</script>