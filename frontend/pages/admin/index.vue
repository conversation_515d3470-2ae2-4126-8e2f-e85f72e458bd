<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">系统管理</h1>
      <p class="mt-1 text-sm text-gray-500">
        管理系统用户、角色权限和配置信息
      </p>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <StatsCard
        title="用户总数"
        :value="stats.totalUsers"
        icon="i-heroicons-users"
        color="blue"
        :change="stats.userGrowth"
        change-type="increase"
      />
      <StatsCard
        title="活跃用户"
        :value="stats.activeUsers"
        icon="i-heroicons-check-circle"
        color="green"
        :change="stats.activeGrowth"
        change-type="increase"
      />
      <StatsCard
        title="角色数量"
        :value="stats.totalRoles"
        icon="i-heroicons-shield-check"
        color="purple"
      />
      <StatsCard
        title="今日登录"
        :value="stats.todayLogins"
        icon="i-heroicons-arrow-right-on-rectangle"
        color="yellow"
        :change="stats.loginGrowth"
        change-type="increase"
      />
    </div>

    <!-- 快捷操作 -->
    <UCard>
      <template #header>
        <h2 class="text-lg font-medium text-gray-900">快捷操作</h2>
      </template>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <UButton
          v-if="authStore.hasPermission('user:create')"
          block
          icon="i-heroicons-user-plus"
          color="blue"
          variant="soft"
          @click="navigateTo('/admin/users?action=create')"
        >
          新增用户
        </UButton>
        
        <UButton
          v-if="authStore.hasPermission('role:create')"
          block
          icon="i-heroicons-shield-plus"
          color="green"
          variant="soft"
          @click="navigateTo('/admin/roles?action=create')"
        >
          新增角色
        </UButton>
        
        <UButton
          v-if="authStore.hasPermission('user:read')"
          block
          icon="i-heroicons-chart-bar"
          color="purple"
          variant="soft"
          @click="navigateTo('/admin/reports')"
        >
          查看报表
        </UButton>
      </div>
    </UCard>

    <!-- 最近活动 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 最近用户 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">最近注册用户</h2>
            <UButton
              v-if="authStore.hasPermission('user:read')"
              variant="ghost"
              size="sm"
              to="/admin/users"
            >
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div
            v-for="user in recentUsers"
            :key="user.id"
            class="flex items-center justify-between p-3 rounded-lg bg-gray-50"
          >
            <div class="flex items-center space-x-3">
              <UAvatar
                :alt="user.fullName"
                :src="user.avatar"
                size="sm"
              />
              <div>
                <p class="text-sm font-medium text-gray-900">{{ user.fullName }}</p>
                <p class="text-xs text-gray-500">{{ user.email }}</p>
              </div>
            </div>
            <div class="text-right">
              <p class="text-xs text-gray-500">{{ formatDate(user.createdAt) }}</p>
              <UBadge
                :color="user.isActive ? 'green' : 'red'"
                variant="soft"
                size="xs"
              >
                {{ user.isActive ? '激活' : '禁用' }}
              </UBadge>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 系统日志 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-lg font-medium text-gray-900">系统日志</h2>
            <UButton
              v-if="authStore.hasPermission('audit:read')"
              variant="ghost"
              size="sm"
              to="/admin/audit-logs"
            >
              查看全部
            </UButton>
          </div>
        </template>
        
        <div class="space-y-4">
          <div
            v-for="log in recentLogs"
            :key="log.id"
            class="flex items-start space-x-3 p-3 rounded-lg bg-gray-50"
          >
            <UIcon
              :name="getLogIcon(log.action)"
              :class="getLogColor(log.action)"
              class="w-4 h-4 mt-0.5"
            />
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900">{{ log.description }}</p>
              <p class="text-xs text-gray-500">
                {{ log.userName }} · {{ formatDate(log.createdAt) }}
              </p>
            </div>
          </div>
        </div>
      </UCard>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { StatsCard } from '#components'  // 暂时注释，直接使用组件

definePageMeta({
  middleware: ['auth', 'permission'],
  layout: 'admin'
})

// 页面标题
useHead({
  title: '系统管理 - 运维管理系统'
})

const authStore = useAuthStore()
const { $apiClient } = useNuxtApp()

// 响应式数据
const stats = ref({
  totalUsers: 0,
  activeUsers: 0,
  totalRoles: 0,
  todayLogins: 0,
  userGrowth: 0,
  activeGrowth: 0,
  loginGrowth: 0
})

const recentUsers = ref<any[]>([])
const recentLogs = ref<any[]>([])
const loading = ref(false)

// 加载数据
const loadStats = async () => {
  try {
    const response = await $apiClient('/admin/stats')
    stats.value = response.data
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

const loadRecentUsers = async () => {
  try {
    const response = await $apiClient('/users', {
      query: { 
        limit: 5, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      }
    })
    recentUsers.value = response.data.items || response.data.users || []
  } catch (error) {
    console.error('Load recent users error:', error)
  }
}

const loadRecentLogs = async () => {
  try {
    const response = await $apiClient('/audit-logs', {
      query: { 
        limit: 5, 
        sortBy: 'createdAt', 
        sortOrder: 'desc' 
      }
    })
    recentLogs.value = response.data.items || response.data.logs || []
  } catch (error) {
    console.error('Load recent logs error:', error)
  }
}

// 辅助函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getLogIcon = (action: string) => {
  const iconMap: Record<string, string> = {
    'user.create': 'i-heroicons-user-plus',
    'user.update': 'i-heroicons-user',
    'user.delete': 'i-heroicons-user-minus',
    'role.create': 'i-heroicons-shield-plus',
    'role.update': 'i-heroicons-shield-check',
    'role.delete': 'i-heroicons-shield-exclamation',
    'login': 'i-heroicons-arrow-right-on-rectangle',
    'logout': 'i-heroicons-arrow-left-on-rectangle'
  }
  return iconMap[action] || 'i-heroicons-information-circle'
}

const getLogColor = (action: string) => {
  const colorMap: Record<string, string> = {
    'user.create': 'text-green-500',
    'user.update': 'text-blue-500',
    'user.delete': 'text-red-500',
    'role.create': 'text-purple-500',
    'role.update': 'text-purple-500',
    'role.delete': 'text-red-500',
    'login': 'text-green-500',
    'logout': 'text-gray-500'
  }
  return colorMap[action] || 'text-gray-500'
}

// 页面加载
onMounted(async () => {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadRecentUsers(),
      loadRecentLogs()
    ])
  } finally {
    loading.value = false
  }
})
</script>