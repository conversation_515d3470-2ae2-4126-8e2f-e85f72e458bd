<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <PageHeader
      title="角色权限管理"
      description="管理系统中的角色和权限配置"
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 搜索和筛选 -->
    <UCard>
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索角色名称或描述..."
            icon="i-heroicons-magnifying-glass"
            @input="debouncedSearch"
          />
        </div>
      </div>
    </UCard>

    <!-- 角色列表 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
      <div
        v-for="role in roles"
        :key="role.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
      >
        <!-- 角色头部 -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">{{ role.name }}</h3>
            <p class="text-sm text-gray-500 mt-1">{{ role.description }}</p>
          </div>
          
          <!-- 操作按钮 -->
          <UDropdown v-if="getActionItems(role).length > 0">
            <UButton
              variant="ghost"
              size="sm"
              icon="i-heroicons-ellipsis-vertical"
            />
            
            <template #panel>
              <div class="p-1">
                <UButton
                  v-for="action in getActionItems(role)"
                  :key="action.key"
                  variant="ghost"
                  size="sm"
                  :icon="action.icon"
                  :color="action.color"
                  block
                  @click="handleTableAction(action.key, role)"
                >
                  {{ action.label }}
                </UButton>
              </div>
            </template>
          </UDropdown>
        </div>

        <!-- 角色统计 -->
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-4">
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">{{ role.userCount || 0 }}</div>
              <div class="text-xs text-gray-500">用户数</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-semibold text-gray-900">{{ role.permissions?.length || 0 }}</div>
              <div class="text-xs text-gray-500">权限数</div>
            </div>
          </div>
          
          <UBadge
            v-if="role.isSystem"
            color="blue"
            variant="soft"
            size="xs"
          >
            系统角色
          </UBadge>
        </div>

        <!-- 权限列表 -->
        <div class="space-y-2">
          <div class="text-sm font-medium text-gray-700">权限列表：</div>
          <div class="flex flex-wrap gap-1">
            <UBadge
              v-for="permission in role.permissions?.slice(0, 6)"
              :key="permission"
              color="gray"
              variant="soft"
              size="xs"
            >
              {{ getPermissionLabel(permission) }}
            </UBadge>
            <UBadge
              v-if="role.permissions && role.permissions.length > 6"
              color="gray"
              variant="outline"
              size="xs"
            >
              +{{ role.permissions.length - 6 }}
            </UBadge>
          </div>
        </div>

        <!-- 创建时间 -->
        <div class="mt-4 pt-4 border-t border-gray-100">
          <div class="text-xs text-gray-500">
            创建于 {{ formatDate(role.createdAt) }}
          </div>
        </div>
      </div>
      
      <!-- 新增角色卡片 -->
      <div
        v-if="authStore.hasPermission('role:create')"
        class="bg-white rounded-lg shadow-sm border-2 border-dashed border-gray-300 p-6 flex items-center justify-center hover:border-gray-400 transition-colors cursor-pointer"
        @click="showCreateModal = true"
      >
        <div class="text-center">
          <UIcon name="i-heroicons-plus" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <div class="text-sm font-medium text-gray-600">新建角色</div>
        </div>
      </div>
    </div>

    <!-- 新增角色模态框 -->
    <UModal v-model="showCreateModal" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">新建角色</h3>
        </template>
        
        <form @submit.prevent="handleCreate">
      <form @submit.prevent="handleCreate">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="角色名称" required>
              <UInput
                v-model="createForm.name"
                placeholder="请输入角色名称（英文）"
                :disabled="createForm.loading"
              />
            </UFormGroup>

            <UFormGroup label="角色描述" required>
              <UInput
                v-model="createForm.description"
                placeholder="请输入角色描述"
                :disabled="createForm.loading"
              />
            </UFormGroup>
          </div>

          <!-- 权限选择 -->
          <div>
            <UFormGroup label="权限配置" required>
              <div class="space-y-4">
                <div
                  v-for="group in permissionGroups"
                  :key="group.category"
                  class="border border-gray-200 rounded-lg p-4"
                >
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">{{ group.category }}</h4>
                    <UButton
                      variant="ghost"
                      size="xs"
                      @click="toggleGroupPermissions(group, createForm.permissions)"
                    >
                      {{ isGroupSelected(group, createForm.permissions) ? '取消全选' : '全选' }}
                    </UButton>
                  </div>
                  
                  <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                    <div
                      v-for="permission in group.permissions"
                      :key="permission.key"
                      class="flex items-center space-x-2"
                    >
                      <UCheckbox
                        :model-value="createForm.permissions.includes(permission.key)"
                        @update:model-value="togglePermission(permission.key, createForm.permissions)"
                      />
                      <span class="text-sm text-gray-700">{{ permission.description }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </UFormGroup>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="handleCreateCancel"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="createForm.loading"
            :disabled="createForm.permissions.length === 0"
          >
            创建角色
          </UButton>
        </div>
      </form>
        </form></UCard>
      </UModal>

    <!-- 编辑角色模态框 -->
    <UModal v-model="showEditModal" :ui="{ width: 'sm:max-w-4xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">编辑角色</h3>
        </template>
        
        <form @submit.prevent="handleUpdate">
      <form @submit.prevent="handleUpdate">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="角色名称" required>
              <UInput
                v-model="editForm.name"
                placeholder="请输入角色名称（英文）"
                :disabled="editForm.loading || selectedRole?.isSystem"
              />
            </UFormGroup>

            <UFormGroup label="角色描述" required>
              <UInput
                v-model="editForm.description"
                placeholder="请输入角色描述"
                :disabled="editForm.loading"
              />
            </UFormGroup>
          </div>

          <!-- 权限选择 -->
          <div>
            <UFormGroup label="权限配置" required>
              <div class="space-y-4">
                <div
                  v-for="group in permissionGroups"
                  :key="group.category"
                  class="border border-gray-200 rounded-lg p-4"
                >
                  <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">{{ group.category }}</h4>
                    <UButton
                      variant="ghost"
                      size="xs"
                      :disabled="selectedRole?.isSystem"
                      @click="toggleGroupPermissions(group, editForm.permissions)"
                    >
                      {{ isGroupSelected(group, editForm.permissions) ? '取消全选' : '全选' }}
                    </UButton>
                  </div>
                  
                  <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                    <div
                      v-for="permission in group.permissions"
                      :key="permission.key"
                      class="flex items-center space-x-2"
                    >
                      <UCheckbox
                        :model-value="editForm.permissions.includes(permission.key)"
                        :disabled="selectedRole?.isSystem"
                        @update:model-value="togglePermission(permission.key, editForm.permissions)"
                      />
                      <span class="text-sm text-gray-700">{{ permission.description }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </UFormGroup>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <UButton
            variant="ghost"
            @click="handleEditCancel"
          >
            取消
          </UButton>
          <UButton
            type="submit"
            :loading="editForm.loading"
            :disabled="editForm.permissions.length === 0"
          >
            更新角色
          </UButton>
        </div>
      </form>
        </form></UCard>
      </UModal>

    <!-- 角色模板模态框 -->
    <UModal v-model="showTemplateModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">选择角色模板</h3>
        </template>
        
        <div class="space-y-4">
      <div class="space-y-4">
        <div
          v-for="template in roleTemplates"
          :key="template.name"
          class="border border-gray-200 rounded-lg p-4 cursor-pointer transition-colors"
          :class="{ 'border-blue-500 bg-blue-50': templateForm.selectedTemplate === template.name }"
          @click="templateForm.selectedTemplate = template.name"
        >
          <div class="flex items-start space-x-3">
            <URadio
              :model-value="templateForm.selectedTemplate"
              :value="template.name"
              @update:model-value="templateForm.selectedTemplate = template.name"
            />
            <div class="flex-1">
              <h4 class="font-medium text-gray-900">{{ template.description }}</h4>
              <p class="text-sm text-gray-500 mt-1">{{ template.permissions.length }} 个权限</p>
              <div class="flex flex-wrap gap-1 mt-2">
                <UBadge
                  v-for="permission in template.permissions.slice(0, 4)"
                  :key="permission"
                  color="blue"
                  variant="soft"
                  size="xs"
                >
                  {{ getPermissionLabel(permission) }}
                </UBadge>
                <UBadge
                  v-if="template.permissions.length > 4"
                  color="gray"
                  variant="outline"
                  size="xs"
                >
                  +{{ template.permissions.length - 4 }}
                </UBadge>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex justify-end space-x-3 mt-6">
        <UButton
          variant="ghost"
          @click="showTemplateModal = false"
        >
          取消
        </UButton>
        <UButton
          :disabled="!templateForm.selectedTemplate"
          @click="handleCreateFromTemplate"
        >
          使用模板
        </UButton>
      </div>
        </div></UCard>
      </UModal>

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="删除角色"
      :message="`确定要删除角色 ${selectedRole?.name || ''} 吗？此操作不可撤销。`"
      confirm-text="删除"
      confirm-color="red"
      :loading="deleteLoading"
      @confirm="handleDelete"
      @cancel="showDeleteModal = false"
    />
  </div>
</template>

<script setup lang="ts">
// import { PageHeader, FormModal, ConfirmModal } from '#components'  // 暂时注释，直接使用组件
import type { ActionItem } from '~/types/ui'
import { isSuccessResponse } from '~/types/api'

definePageMeta({
  middleware: ['auth', 'permission'],
  layout: 'admin'
})

useHead({
  title: '角色权限管理 - 系统管理'
})

interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount?: number
  isSystem?: boolean
  createdAt: string
  updatedAt: string
}

interface Permission {
  key: string
  description: string
  category: string
}

interface PermissionGroup {
  category: string
  permissions: Permission[]
}

interface RoleTemplate {
  name: string
  description: string
  permissions: string[]
}

const authStore = useAuthStore()
const toast = useToast()

// 响应式数据
const loading = ref(false)
const roles = ref<Role[]>([])
const permissions = ref<Permission[]>([])
const permissionGroups = ref<PermissionGroup[]>([])
const roleTemplates = ref<RoleTemplate[]>([])

const searchQuery = ref('')
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showTemplateModal = ref(false)
const showDeleteModal = ref(false)
const deleteLoading = ref(false)

const selectedRole = ref<Role | null>(null)

// 表单数据
const createForm = reactive({
  name: '',
  description: '',
  permissions: [] as string[],
  loading: false
})

const editForm = reactive({
  name: '',
  description: '',
  permissions: [] as string[],
  loading: false
})

const templateForm = reactive({
  selectedTemplate: ''
})

// 页面操作
const headerActions = computed<ActionItem[]>(() => {
  const actions: ActionItem[] = []
  
  if (authStore.hasPermission('role:create')) {
    actions.push({
      key: 'create',
      label: '新建角色',
      icon: 'i-heroicons-plus',
      color: 'primary'
    })
    
    actions.push({
      key: 'template',
      label: '使用模板',
      icon: 'i-heroicons-document-duplicate',
      color: 'gray'
    })
  }
  
  return actions
})

// 获取操作项
const getActionItems = (role: Role): ActionItem[] => {
  const items: ActionItem[] = []
  
  if (authStore.hasPermission('role:read')) {
    items.push({
      key: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('role:update') && !role.isSystem) {
    items.push({
      key: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
    
    items.push({
      key: 'duplicate',
      label: '复制角色',
      icon: 'i-heroicons-document-duplicate'
    })
  }
  
  if (authStore.hasPermission('role:delete') && !role.isSystem && role.userCount === 0) {
    items.push({
      key: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red'
    })
  }
  
  return items
}

// 防抖搜索 - 将在loadRoles定义后初始化

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'create':
      showCreateModal.value = true
      break
    case 'template':
      showTemplateModal.value = true
      break
  }
}

const handleTableAction = (action: string, role: Role) => {
  selectedRole.value = role
  
  switch (action) {
    case 'view':
      navigateTo(`/admin/roles/${role.id}`)
      break
    case 'edit':
      Object.assign(editForm, {
        name: role.name,
        description: role.description,
        permissions: [...role.permissions]
      })
      showEditModal.value = true
      break
    case 'duplicate':
      duplicateRole(role)
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

// CRUD操作
const handleCreate = async () => {
  createForm.loading = true
  
  try {
    await apiClient.post('/roles', {
      name: createForm.name,
      description: createForm.description,
      permissions: createForm.permissions
    })
    
    toast.add({
      title: '创建成功',
      description: '角色创建成功',
      color: 'green'
    })
    
    showCreateModal.value = false
    resetCreateForm()
    loadRoles()
  } catch (error: any) {
    toast.add({
      title: '创建失败',
      description: error.message || '角色创建失败',
      color: 'red'
    })
  } finally {
    createForm.loading = false
  }
}

const handleUpdate = async () => {
  if (!selectedRole.value) return
  
  editForm.loading = true
  
  try {
    await apiClient.put(`/roles/${selectedRole.value.id}`, {
      name: editForm.name,
      description: editForm.description,
      permissions: editForm.permissions
    })
    
    toast.add({
      title: '更新成功',
      description: '角色信息更新成功',
      color: 'green'
    })
    
    showEditModal.value = false
    resetEditForm()
    loadRoles()
  } catch (error: any) {
    toast.add({
      title: '更新失败',
      description: error.message || '角色信息更新失败',
      color: 'red'
    })
  } finally {
    editForm.loading = false
  }
}

const handleDelete = async () => {
  if (!selectedRole.value) return
  
  deleteLoading.value = true
  
  try {
    await apiClient.delete(`/roles/${selectedRole.value.id}`)
    
    toast.add({
      title: '删除成功',
      description: '角色删除成功',
      color: 'green'
    })
    
    showDeleteModal.value = false
    selectedRole.value = null
    loadRoles()
  } catch (error: any) {
    toast.add({
      title: '删除失败',
      description: error.message || '角色删除失败',
      color: 'red'
    })
  } finally {
    deleteLoading.value = false
  }
}

const handleCreateFromTemplate = async () => {
  if (!templateForm.selectedTemplate) return
  
  try {
    await apiClient.post('/roles/from-template', {
      templateName: templateForm.selectedTemplate
    })
    
    toast.add({
      title: '创建成功',
      description: '从模板创建角色成功',
      color: 'green'
    })
    
    showTemplateModal.value = false
    templateForm.selectedTemplate = ''
    loadRoles()
  } catch (error: any) {
    toast.add({
      title: '创建失败',
      description: error.message || '从模板创建角色失败',
      color: 'red'
    })
  }
}

const duplicateRole = async (role: Role) => {
  try {
    await apiClient.post(`/roles/${role.id}/duplicate`, {
      newName: `${role.name}_copy`,
      newDescription: `${role.description} (副本)`
    })
    
    toast.add({
      title: '复制成功',
      description: '角色复制成功',
      color: 'green'
    })
    
    loadRoles()
  } catch (error: any) {
    toast.add({
      title: '复制失败',
      description: error.message || '角色复制失败',
      color: 'red'
    })
  }
}

// 权限管理辅助函数
const togglePermission = (permission: string, permissionList: string[]) => {
  const index = permissionList.indexOf(permission)
  if (index > -1) {
    permissionList.splice(index, 1)
  } else {
    permissionList.push(permission)
  }
}

const toggleGroupPermissions = (group: PermissionGroup, permissionList: string[]) => {
  const groupPermissions = group.permissions.map(p => p.key)
  const isSelected = isGroupSelected(group, permissionList)
  
  if (isSelected) {
    // 取消选择组内所有权限
    groupPermissions.forEach(permission => {
      const index = permissionList.indexOf(permission)
      if (index > -1) {
        permissionList.splice(index, 1)
      }
    })
  } else {
    // 选择组内所有权限
    groupPermissions.forEach(permission => {
      if (!permissionList.includes(permission)) {
        permissionList.push(permission)
      }
    })
  }
}

const isGroupSelected = (group: PermissionGroup, permissionList: string[]) => {
  return group.permissions.every(p => permissionList.includes(p.key))
}

const getPermissionLabel = (permission: string) => {
  const perm = permissions.value.find(p => p.key === permission)
  return perm ? perm.description : permission
}

// 表单重置
const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    description: '',
    permissions: []
  })
}

const resetEditForm = () => {
  Object.assign(editForm, {
    name: '',
    description: '',
    permissions: []
  })
}

const handleCreateCancel = () => {
  showCreateModal.value = false
  resetCreateForm()
}

const handleEditCancel = () => {
  showEditModal.value = false
  resetEditForm()
  selectedRole.value = null
}

// 辅助函数
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 数据加载
const loadRoles = async () => {
  loading.value = true
  
  try {
    const params: any = {}
    
    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    
    const response = await apiClient.paginate('/roles', params)
    roles.value = response.items as any
  } catch (error) {
    console.error('Load roles error:', error)
    toast.add({
      title: '加载失败',
      description: '获取角色列表失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 初始化防抖搜索
const debouncedSearch = useDebounceFn(loadRoles, 300)

const loadPermissions = async () => {
  try {
    const response = await apiClient.get('/roles/permissions')
    if (isSuccessResponse(response)) {
      permissions.value = response.data as any
    }
  } catch (error) {
    console.error('Load permissions error:', error)
  }
}

const loadPermissionGroups = async () => {
  try {
    const response = await apiClient.get('/roles/permissions/groups')
    if (isSuccessResponse(response)) {
      permissionGroups.value = response.data as any
    }
  } catch (error) {
    console.error('Load permission groups error:', error)
  }
}

const loadRoleTemplates = async () => {
  try {
    const response = await apiClient.get('/roles/templates')
    if (isSuccessResponse(response)) {
      roleTemplates.value = response.data as any
    }
  } catch (error) {
    console.error('Load role templates error:', error)
  }
}

// 页面初始化
onMounted(async () => {
  await Promise.all([
    loadRoles(),
    loadPermissions(),
    loadPermissionGroups(),
    loadRoleTemplates()
  ])
  
  // 检查URL参数是否有创建角色的操作
  const route = useRoute()
  if (route.query.action === 'create' && authStore.hasPermission('role:create')) {
    showCreateModal.value = true
  }
})
</script>