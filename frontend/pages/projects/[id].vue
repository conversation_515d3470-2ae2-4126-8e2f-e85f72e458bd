<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <UButton
          icon="i-heroicons-arrow-left"
          variant="ghost"
          @click="$router.back()"
        >
          返回
        </UButton>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            {{ project?.name || '项目详情' }}
          </h1>
          <p class="mt-1 text-sm text-gray-500">
            {{ project?.customerName }} • {{ formatDate(project?.createdAt) }}
          </p>
        </div>
      </div>
      <div class="flex space-x-3">
        <UButton
          v-if="authStore.hasPermission('archive:update')"
          icon="i-heroicons-pencil"
          @click="showEditModal = true"
        >
          编辑
        </UButton>
        <UButton
          v-if="authStore.hasPermission('service:create')"
          icon="i-heroicons-ticket"
          @click="navigateTo(`/services/create?projectId=${project?.id}`)"
        >
          创建工单
        </UButton>
        <UButton
          v-if="authStore.hasPermission('archive:delete')"
          color="red"
          variant="soft"
          icon="i-heroicons-trash"
          @click="showDeleteModal = true"
        >
          删除
        </UButton>
      </div>
    </div>

    <!-- 项目概览卡片 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 项目基本信息 -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <div class="flex items-center justify-between">
              <h3 class="text-lg font-medium text-gray-900">项目信息</h3>
              <div class="flex items-center space-x-2">
                <UBadge
                  :color="getStatusColor(project?.status || '') as BadgeColor"
                  variant="soft"
                >
                  {{ getStatusText(project?.status) }}
                </UBadge>
                <UBadge
                  :color="getPriorityColor(project?.priority || '') as BadgeColor"
                  variant="soft"
                >
                  {{ getPriorityText(project?.priority) }}
                </UBadge>
              </div>
            </div>
          </template>

          <div class="space-y-6">
            <!-- 项目描述 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-2">项目描述</h4>
              <p class="text-sm text-gray-600">
                {{ project?.description || '暂无描述' }}
              </p>
            </div>

            <!-- 项目需求 -->
            <div v-if="project?.requirements">
              <h4 class="text-sm font-medium text-gray-900 mb-2">项目需求</h4>
              <p class="text-sm text-gray-600 whitespace-pre-wrap">
                {{ project.requirements }}
              </p>
            </div>

            <!-- 基本信息网格 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <dt class="text-sm font-medium text-gray-500">项目类型</dt>
                <dd class="mt-1">
                  <UBadge
                    :color="getTypeColor(project?.type || '') as BadgeColor"
                    variant="soft"
                  >
                    {{ getTypeText(project?.type) }}
                  </UBadge>
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">项目预算</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatCurrency(project?.budget) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">开始日期</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(project?.startDate) }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">结束日期</dt>
                <dd class="mt-1 text-sm text-gray-900">
                  {{ project?.endDate ? formatDate(project.endDate) : '进行中' }}
                </dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">技术栈</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ project?.techStack || '-' }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">创建时间</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ formatDate(project?.createdAt) }}</dd>
              </div>
            </div>

            <!-- 项目进度 -->
            <div>
              <div class="flex justify-between text-sm text-gray-600 mb-2">
                <span class="font-medium">项目进度</span>
                <span>{{ project?.progress || 0 }}%</span>
              </div>
              <div class="w-full bg-gray-200 rounded-full h-3">
                <div
                  class="bg-primary-600 h-3 rounded-full transition-all"
                  :style="{ width: `${project?.progress || 0}%` }"
                />
              </div>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 统计信息和快速操作 -->
      <div class="space-y-6">
        <!-- 统计信息 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">统计信息</h3>
          </template>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">服务工单</span>
              <span class="text-lg font-semibold text-gray-900">{{ stats.services || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">待处理工单</span>
              <span class="text-lg font-semibold text-yellow-600">{{ stats.pendingServices || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">已完成工单</span>
              <span class="text-lg font-semibold text-green-600">{{ stats.completedServices || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">配置项数量</span>
              <span class="text-lg font-semibold text-gray-900">{{ stats.configurations || 0 }}</span>
            </div>
          </div>
        </UCard>

        <!-- 快速操作 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">快速操作</h3>
          </template>

          <div class="space-y-3">
            <UButton
              v-if="authStore.hasPermission('service:create')"
              block
              variant="soft"
              icon="i-heroicons-ticket"
              @click="navigateTo(`/services/create?projectId=${project?.id}`)"
            >
              创建工单
            </UButton>
            <UButton
              v-if="authStore.hasPermission('configuration:create')"
              block
              variant="soft"
              icon="i-heroicons-cog-6-tooth"
              @click="navigateTo(`/admin/configurations/create?projectId=${project?.id}`)"
            >
              添加配置
            </UButton>
            <UButton
              v-if="authStore.hasPermission('archive:read')"
              block
              variant="soft"
              icon="i-heroicons-document-text"
              @click="generateReport"
            >
              生成报告
            </UButton>
          </div>
        </UCard>

        <!-- 项目时间线 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">项目时间线</h3>
          </template>

          <div class="space-y-3">
            <div class="flex items-center space-x-3">
              <div class="w-2 h-2 bg-green-500 rounded-full"/>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">项目创建</p>
                <p class="text-xs text-gray-500">{{ formatDate(project?.createdAt) }}</p>
              </div>
            </div>
            <div v-if="project?.startDate" class="flex items-center space-x-3">
              <div class="w-2 h-2 bg-blue-500 rounded-full"/>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">项目开始</p>
                <p class="text-xs text-gray-500">{{ formatDate(project.startDate) }}</p>
              </div>
            </div>
            <div v-if="project?.endDate" class="flex items-center space-x-3">
              <div class="w-2 h-2 bg-gray-400 rounded-full"/>
              <div class="flex-1">
                <p class="text-sm font-medium text-gray-900">计划结束</p>
                <p class="text-xs text-gray-500">{{ formatDate(project.endDate) }}</p>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 相关数据标签页 -->
    <UCard>
      <UTabs v-model="activeTab" :items="tabItems">
        <!-- 服务工单 -->
        <template #services>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">服务工单</h4>
              <UButton
                v-if="authStore.hasPermission('service:create')"
                size="sm"
                icon="i-heroicons-plus"
                @click="navigateTo(`/services/create?projectId=${project?.id}`)"
              >
                新增工单
              </UButton>
            </div>
            
            <div v-if="services.length > 0" class="space-y-3">
              <div
                v-for="service in services"
                :key="service.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                @click="navigateTo(`/services/${service.id}`)"
              >
                <div class="flex-1">
                  <h5 class="font-medium text-gray-900">{{ service.title }}</h5>
                  <p class="text-sm text-gray-500">{{ service.description }}</p>
                  <div class="flex items-center space-x-4 mt-2">
                    <span class="text-xs text-gray-400">{{ formatDate(service.createdAt) }}</span>
                    <span class="text-xs text-gray-400">优先级: {{ getPriorityText(service.priority) }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <UBadge
                    :color="getServiceStatusColor(service.status || '') as BadgeColor"
                    variant="soft"
                  >
                    {{ getServiceStatusText(service.status) }}
                  </UBadge>
                  <UIcon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <UIcon name="i-heroicons-ticket" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">暂无服务工单</p>
              <UButton
                v-if="authStore.hasPermission('service:create')"
                class="mt-4"
                @click="navigateTo(`/services/create?projectId=${project?.id}`)"
              >
                创建第一个工单
              </UButton>
            </div>
          </div>
        </template>

        <!-- 配置项 -->
        <template #configurations>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">配置项</h4>
              <UButton
                v-if="authStore.hasPermission('configuration:create')"
                size="sm"
                icon="i-heroicons-plus"
                @click="navigateTo(`/admin/configurations/create?projectId=${project?.id}`)"
              >
                新增配置
              </UButton>
            </div>
            
            <div v-if="configurations.length > 0" class="space-y-3">
              <div
                v-for="config in configurations"
                :key="config.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
                @click="navigateTo(`/admin/configurations/${config.id}`)"
              >
                <div class="flex-1">
                  <h5 class="font-medium text-gray-900">{{ config.name }}</h5>
                  <p class="text-sm text-gray-500">{{ config.description }}</p>
                  <div class="flex items-center space-x-4 mt-2">
                    <span class="text-xs text-gray-400">{{ getTypeText(config.type) }}</span>
                    <span class="text-xs text-gray-400">{{ formatDate(config.updatedAt) }}</span>
                  </div>
                </div>
                <div class="flex items-center space-x-2">
                  <UBadge
                    :color="config.status === 'active' ? 'green' : 'gray'"
                    variant="soft"
                  >
                    {{ config.status === 'active' ? '启用' : '禁用' }}
                  </UBadge>
                  <UIcon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400" />
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <UIcon name="i-heroicons-cog-6-tooth" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">暂无配置项</p>
              <UButton
                v-if="authStore.hasPermission('configuration:create')"
                class="mt-4"
                @click="navigateTo(`/admin/configurations/create?projectId=${project?.id}`)"
              >
                添加第一个配置
              </UButton>
            </div>
          </div>
        </template>

        <!-- 项目文档 -->
        <template #documents>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">项目文档</h4>
              <UButton
                v-if="authStore.hasPermission('archive:update')"
                size="sm"
                icon="i-heroicons-plus"
                @click="showUploadModal = true"
              >
                上传文档
              </UButton>
            </div>
            
            <div class="text-center py-8">
              <UIcon name="i-heroicons-document-text" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">文档管理功能开发中...</p>
            </div>
          </div>
        </template>
      </UTabs>
    </UCard>

    <!-- 编辑项目模态框 -->
    <UModal v-model="showEditModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">编辑项目</h3>
        </template>

        <UForm
          :schema="projectSchema"
          :state="editForm"
          class="space-y-4"
          @submit="handleUpdate"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="项目名称" name="name" required>
              <UInput v-model="editForm.name" placeholder="请输入项目名称" />
            </UFormGroup>

            <UFormGroup label="项目类型" name="type" required>
              <USelect
                v-model="editForm.type"
                :options="typeOptions"
                placeholder="请选择项目类型"
              />
            </UFormGroup>

            <UFormGroup label="项目状态" name="status" required>
              <USelect
                v-model="editForm.status"
                :options="statusOptions"
                placeholder="请选择项目状态"
              />
            </UFormGroup>

            <UFormGroup label="优先级" name="priority" required>
              <USelect
                v-model="editForm.priority"
                :options="priorityOptions"
                placeholder="请选择优先级"
              />
            </UFormGroup>

            <UFormGroup label="项目预算" name="budget">
              <UInput
                v-model="editForm.budget"
                type="number"
                placeholder="请输入项目预算"
                step="0.01"
              />
            </UFormGroup>

            <UFormGroup label="项目进度" name="progress">
              <UInput
                v-model="editForm.progress"
                type="number"
                placeholder="请输入项目进度 (0-100)"
                min="0"
                max="100"
              />
            </UFormGroup>

            <UFormGroup label="开始日期" name="startDate" required>
              <UInput
                v-model="editForm.startDate"
                type="date"
                placeholder="请选择开始日期"
              />
            </UFormGroup>

            <UFormGroup label="结束日期" name="endDate">
              <UInput
                v-model="editForm.endDate"
                type="date"
                placeholder="请选择结束日期"
              />
            </UFormGroup>

            <UFormGroup label="技术栈" name="techStack" class="md:col-span-2">
              <UInput
                v-model="editForm.techStack"
                placeholder="如：Vue.js, Node.js, MySQL"
              />
            </UFormGroup>
          </div>

          <UFormGroup label="项目描述" name="description">
            <UTextarea
              v-model="editForm.description"
              placeholder="请输入项目描述"
              :rows="4"
            />
          </UFormGroup>

          <UFormGroup label="项目需求" name="requirements">
            <UTextarea
              v-model="editForm.requirements"
              placeholder="请输入项目需求"
              :rows="3"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showEditModal = false"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="updating"
            >
              更新
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>

    <!-- 删除确认模态框 -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-red-600">确认删除</h3>
        </template>

        <div class="space-y-4">
          <p class="text-gray-600">
            确定要删除项目 <strong>{{ project?.name }}</strong> 吗？
          </p>
          <p class="text-sm text-red-600">
            此操作不可撤销，相关的服务工单和配置也将受到影响。
          </p>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showDeleteModal = false"
            >
              取消
            </UButton>
            <UButton
              color="red"
              :loading="deleting"
              @click="handleDelete"
            >
              确认删除
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { BadgeColor } from '~/types/ui'
import { isSuccessResponse } from '~/types/api'

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 设置页面标题
useHead({
  title: computed(() => project.value?.name ? `${project.value.name} - 项目详情` : '项目详情')
})

// 项目验证Schema
const projectSchema = z.object({
  name: z.string().min(1, '请输入项目名称'),
  type: z.enum(['web', 'mobile', 'desktop', 'api', 'other']),
  status: z.enum(['planning', 'active', 'completed', 'cancelled', 'on_hold']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  budget: z.string().optional().transform(val => val ? Number(val) : undefined),
  startDate: z.string().min(1, '请选择开始日期'),
  endDate: z.string().optional(),
  progress: z.string().optional().transform(val => val ? Number(val) : 0),
  techStack: z.string().optional(),
  description: z.string().optional(),
  requirements: z.string().optional()
})

// 数据状态
const project = ref<any>(null)
const stats = ref<any>({})
const services = ref<any[]>([])
const configurations = ref<any[]>([])

// 界面状态
const loading = ref(false)
const updating = ref(false)
const deleting = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const showUploadModal = ref(false)
const activeTab = ref(0)

// 表单状态
const editForm = reactive({
  name: '',
  type: 'web',
  status: 'planning',
  priority: 'medium',
  budget: '',
  startDate: '',
  endDate: '',
  progress: '0',
  techStack: '',
  description: '',
  requirements: ''
})

// 选项配置
const statusOptions = [
  { label: '规划中', value: 'planning' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
  { label: '暂停', value: 'on_hold' }
]

const typeOptions = [
  { label: 'Web应用', value: 'web' },
  { label: '移动应用', value: 'mobile' },
  { label: '桌面应用', value: 'desktop' },
  { label: 'API服务', value: 'api' },
  { label: '其他', value: 'other' }
]

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 标签页配置
const tabItems = [
  { key: 'services', label: '服务工单', slot: 'services' },
  { key: 'configurations', label: '配置项', slot: 'configurations' },
  { key: 'documents', label: '项目文档', slot: 'documents' }
]

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    planning: 'blue',
    active: 'green',
    completed: 'gray',
    cancelled: 'red',
    on_hold: 'yellow'
  }
  return colors[status] || 'gray'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    planning: '规划中',
    active: '进行中',
    completed: '已完成',
    cancelled: '已取消',
    on_hold: '暂停'
  }
  return texts[status] || status
}

const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    web: 'blue',
    mobile: 'green',
    desktop: 'purple',
    api: 'orange',
    other: 'gray'
  }
  return colors[type] || 'gray'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    web: 'Web应用',
    mobile: '移动应用',
    desktop: '桌面应用',
    api: 'API服务',
    other: '其他'
  }
  return texts[type] || type
}

const getPriorityColor = (priority: string) => {
  const colors: Record<string, string> = {
    low: 'gray',
    medium: 'blue',
    high: 'orange',
    urgent: 'red'
  }
  return colors[priority] || 'gray'
}

const getPriorityText = (priority: string) => {
  const texts: Record<string, string> = {
    low: '低优先级',
    medium: '中优先级',
    high: '高优先级',
    urgent: '紧急'
  }
  return texts[priority] || priority
}

const getServiceStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    pending: 'yellow',
    processing: 'blue',
    completed: 'green',
    closed: 'gray'
  }
  return colors[status] || 'gray'
}

const getServiceStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    closed: '已关闭'
  }
  return texts[status] || status
}

const formatCurrency = (amount: number) => {
  if (!amount) return '未设置'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 数据加载
const loadProjectDetail = async () => {
  loading.value = true
  
  try {
    const projectId = route.params.id as string
    
    // 并行加载项目详情和相关数据
    const [
      projectResponse,
      statsResponse,
      servicesResponse,
      configurationsResponse
    ] = await Promise.all([
      apiClient.get(`/archives/${projectId}`),
      apiClient.get(`/archives/${projectId}/stats`),
      apiClient.paginate('/services', { projectId, limit: 10 }),
      apiClient.paginate('/configurations', { projectId, limit: 10 })
    ])

    if (isSuccessResponse(projectResponse)) {
      project.value = projectResponse.data
    }
    if (isSuccessResponse(statsResponse)) {
      stats.value = statsResponse.data
    }
    // 处理分页响应
    services.value = (servicesResponse as any)?.items || []
    configurations.value = (configurationsResponse as any)?.items || []

    // 填充编辑表单
    Object.assign(editForm, {
      ...project.value,
      budget: project.value.budget?.toString() || '',
      progress: project.value.progress?.toString() || '0',
      startDate: project.value.startDate ? new Date(project.value.startDate).toISOString().split('T')[0] : '',
      endDate: project.value.endDate ? new Date(project.value.endDate).toISOString().split('T')[0] : ''
    })
  } catch (error: any) {
    console.error('Load project detail error:', error)
    
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: '项目不存在'
      })
    }
    
    toast.add({
      title: '加载失败',
      description: '获取项目详情失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 更新项目
const handleUpdate = async () => {
  updating.value = true
  
  try {
    const projectId = route.params.id as string
    const response = await apiClient.put(`/archives/${projectId}`, editForm)
    
    if (isSuccessResponse(response)) {
      project.value = response.data
    } else {
      throw new Error(response.message || '更新失败')
    }
    showEditModal.value = false
    
    toast.add({
      title: '更新成功',
      description: '项目信息已更新',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Update project error:', error)
    toast.add({
      title: '更新失败',
      description: error.message || '更新失败，请稍后重试',
      color: 'red'
    })
  } finally {
    updating.value = false
  }
}

// 删除项目
const handleDelete = async () => {
  deleting.value = true
  
  try {
    const projectId = route.params.id as string
    await apiClient.delete(`/archives/${projectId}`)
    
    toast.add({
      title: '删除成功',
      description: '项目已删除',
      color: 'green'
    })

    router.push('/projects')
  } catch (error: any) {
    console.error('Delete project error:', error)
    toast.add({
      title: '删除失败',
      description: error.message || '删除失败，请稍后重试',
      color: 'red'
    })
  } finally {
    deleting.value = false
  }
}

// 生成报告
const generateReport = () => {
  toast.add({
    title: '功能开发中',
    description: '项目报告生成功能正在开发中',
    color: 'blue'
  })
}

// 页面加载时获取数据
onMounted(() => {
  loadProjectDetail()
})
</script>
