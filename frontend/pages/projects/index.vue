<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">项目档案管理</h1>
        <p class="mt-1 text-sm text-gray-500">
          管理系统中的所有项目档案信息
        </p>
      </div>
      <UButton
        v-if="authStore.hasPermission('archive:create')"
        icon="i-heroicons-plus"
        @click="showCreateModal = true"
      >
        新增项目
      </UButton>
    </div>

    <!-- 搜索和筛选 -->
    <UCard>
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <UInput
            v-model="searchQuery"
            placeholder="搜索项目名称、描述或技术栈..."
            icon="i-heroicons-magnifying-glass"
            @input="debouncedSearch"
          />
        </div>
        <div class="flex gap-2">
          <USelect
            v-model="statusFilter"
            :options="statusOptions"
            placeholder="状态筛选"
            @change="loadProjects"
          />
          <USelect
            v-model="customerFilter"
            :options="customerOptions"
            placeholder="客户筛选"
            @change="loadProjects"
          />
          <USelect
            v-model="typeFilter"
            :options="typeOptions"
            placeholder="类型筛选"
            @change="loadProjects"
          />
        </div>
      </div>
    </UCard>

    <!-- 项目列表 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="project in projects"
        :key="project.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
        @click="navigateTo(`/projects/${project.id}`)"
      >
        <div class="p-6">
          <!-- 项目头部 -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">
                {{ project.name }}
              </h3>
              <p class="text-sm text-gray-500">
                {{ project.customer.name }}
              </p>
            </div>
            <div class="flex items-center space-x-2">
              <UBadge
                :color="getStatusColor(project.status)"
                variant="soft"
              >
                {{ getStatusText(project.status) }}
              </UBadge>
              <UDropdown :items="getActionItems(project)" @click.stop>
                <UButton
                  color="gray"
                  variant="ghost"
                  icon="i-heroicons-ellipsis-vertical"
                  size="sm"
                />
              </UDropdown>
            </div>
          </div>

          <!-- 项目描述 -->
          <p class="text-sm text-gray-600 mb-4 line-clamp-2">
            {{ project.description || '暂无描述' }}
          </p>

          <!-- 项目信息 -->
          <div class="space-y-2 mb-4">
            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-calendar" class="w-4 h-4 mr-2" />
              <span>{{ formatDateRange(project.startDate, project.endDate) }}</span>
            </div>
            <div class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-currency-dollar" class="w-4 h-4 mr-2" />
              <span>预算：{{ formatCurrency(project.budget) }}</span>
            </div>
            <div v-if="project.techStack" class="flex items-center text-sm text-gray-500">
              <UIcon name="i-heroicons-code-bracket" class="w-4 h-4 mr-2" />
              <span class="truncate">{{ project.techStack }}</span>
            </div>
          </div>

          <!-- 进度条 -->
          <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-1">
              <span>进度</span>
              <span>{{ project.progress || 0 }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                class="bg-primary-600 h-2 rounded-full transition-all"
                :style="{ width: `${project.progress || 0}%` }"
              />
            </div>
          </div>

          <!-- 项目标签 -->
          <div class="flex flex-wrap gap-1">
            <UBadge
              v-if="project.type"
              :color="getTypeColor(project.type)"
              variant="soft"
              size="xs"
            >
              {{ getTypeText(project.type) }}
            </UBadge>
            <UBadge
              v-if="project.priority"
              :color="getPriorityColor(project.priority)"
              variant="soft"
              size="xs"
            >
              {{ getPriorityText(project.priority) }}
            </UBadge>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div
        v-if="!loading && projects.length === 0"
        class="col-span-full flex flex-col items-center justify-center py-12"
      >
        <UIcon name="i-heroicons-folder" class="w-16 h-16 text-gray-400 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目档案</h3>
        <p class="text-gray-500 mb-4">点击上方按钮创建第一个项目档案</p>
        <UButton
          v-if="authStore.hasPermission('archive:create')"
          @click="showCreateModal = true"
        >
          创建项目
        </UButton>
      </div>

      <!-- 加载状态 -->
      <div
        v-if="loading"
        class="col-span-full flex justify-center py-12"
      >
        <UIcon name="i-heroicons-arrow-path" class="w-8 h-8 text-gray-400 animate-spin" />
      </div>
    </div>

    <!-- 分页 -->
    <div v-if="projects.length > 0" class="flex justify-center">
      <UPagination
        v-model="pagination.page"
        :page-count="pagination.limit"
        :total="pagination.total"
        @update:model-value="loadProjects"
      />
    </div>

    <!-- 新增/编辑项目模态框 -->
    <UModal v-model="showCreateModal" :ui="{ width: 'sm:max-w-2xl' }">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">
            {{ editingProject ? '编辑项目' : '新增项目' }}
          </h3>
        </template>

        <UForm
          :schema="projectSchema"
          :state="projectForm"
          class="space-y-4"
          @submit="handleSubmit"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="项目名称" name="name" required>
              <UInput v-model="projectForm.name" placeholder="请输入项目名称" />
            </UFormGroup>

            <UFormGroup label="所属客户" name="customerId" required>
              <USelect
                v-model="projectForm.customerId"
                :options="customerOptions"
                placeholder="请选择客户"
                value-attribute="value"
                option-attribute="label"
              />
            </UFormGroup>

            <UFormGroup label="项目类型" name="type" required>
              <USelect
                v-model="projectForm.type"
                :options="typeOptions"
                placeholder="请选择项目类型"
              />
            </UFormGroup>

            <UFormGroup label="项目状态" name="status" required>
              <USelect
                v-model="projectForm.status"
                :options="statusOptions"
                placeholder="请选择项目状态"
              />
            </UFormGroup>

            <UFormGroup label="优先级" name="priority" required>
              <USelect
                v-model="projectForm.priority"
                :options="priorityOptions"
                placeholder="请选择优先级"
              />
            </UFormGroup>

            <UFormGroup label="项目预算" name="budget">
              <UInput
                v-model="projectForm.budget"
                type="number"
                placeholder="请输入项目预算"
                step="0.01"
              />
            </UFormGroup>

            <UFormGroup label="开始日期" name="startDate" required>
              <UInput
                v-model="projectForm.startDate"
                type="date"
                placeholder="请选择开始日期"
              />
            </UFormGroup>

            <UFormGroup label="结束日期" name="endDate">
              <UInput
                v-model="projectForm.endDate"
                type="date"
                placeholder="请选择结束日期"
              />
            </UFormGroup>

            <UFormGroup label="项目进度" name="progress">
              <UInput
                v-model="projectForm.progress"
                type="number"
                placeholder="请输入项目进度 (0-100)"
                min="0"
                max="100"
              />
            </UFormGroup>

            <UFormGroup label="技术栈" name="techStack">
              <UInput
                v-model="projectForm.techStack"
                placeholder="如：Vue.js, Node.js, MySQL"
              />
            </UFormGroup>
          </div>

          <UFormGroup label="项目描述" name="description">
            <UTextarea
              v-model="projectForm.description"
              placeholder="请输入项目描述"
              :rows="4"
            />
          </UFormGroup>

          <UFormGroup label="项目需求" name="requirements">
            <UTextarea
              v-model="projectForm.requirements"
              placeholder="请输入项目需求"
              :rows="3"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="closeModal"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="submitting"
            >
              {{ editingProject ? '更新' : '创建' }}
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>

    <!-- 删除确认模态框 -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-red-600">确认删除</h3>
        </template>

        <div class="space-y-4">
          <p class="text-gray-600">
            确定要删除项目 <strong>{{ deletingProject?.name }}</strong> 吗？
          </p>
          <p class="text-sm text-red-600">
            此操作不可撤销，相关的服务工单和配置也将受到影响。
          </p>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showDeleteModal = false"
            >
              取消
            </UButton>
            <UButton
              color="red"
              :loading="deleting"
              @click="handleDelete"
            >
              确认删除
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { z } from 'zod'

// 定义类型接口
interface Project {
  id: string
  name: string
  customerId: string
  customer: { name: string }
  type: 'web' | 'mobile' | 'desktop' | 'api' | 'other'
  status: 'planning' | 'development' | 'testing' | 'deployed' | 'maintenance' | 'archived'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  startDate: string
  endDate: string
  budget: number
  description: string
  requirements: string
  techStack?: string
  progress?: number
  createdAt: string
  updatedAt: string
}

interface Customer {
  id: string
  name: string
}

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

// 设置页面标题
useHead({
  title: '项目档案管理'
})

const authStore = useAuthStore()
const toast = useToast()
const route = useRoute()
const { $apiClient } = useNuxtApp()

// 项目验证Schema
const projectSchema = z.object({
  name: z.string().min(1, '请输入项目名称'),
  customerId: z.string().min(1, '请选择客户'),
  type: z.enum(['web', 'mobile', 'desktop', 'api', 'other']),
  status: z.enum(['planning', 'active', 'completed', 'cancelled', 'on_hold']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  budget: z.string().optional().transform(val => val ? Number(val) : undefined),
  startDate: z.string().min(1, '请选择开始日期'),
  endDate: z.string().optional(),
  progress: z.string().optional().transform(val => val ? Number(val) : 0),
  techStack: z.string().optional(),
  description: z.string().optional(),
  requirements: z.string().optional()
})

// 数据状态
const projects = ref<Project[]>([])
const customers = ref<Customer[]>([])
const loading = ref(false)
const submitting = ref(false)
const deleting = ref(false)

// 搜索和筛选
const searchQuery = ref('')
const statusFilter = ref('')
const customerFilter = ref('')
const typeFilter = ref('')

// 分页
const pagination = ref({
  page: 1,
  limit: 12,
  total: 0,
  totalPages: 0
})

// 模态框状态
const showCreateModal = ref(false)
const showDeleteModal = ref(false)
const editingProject = ref<any>(null)
const deletingProject = ref<any>(null)

// 表单状态
const projectForm = reactive({
  name: '',
  customerId: '',
  type: 'web',
  status: 'planning',
  priority: 'medium',
  budget: undefined,
  startDate: '',
  endDate: '',
  progress: 0,
  techStack: '',
  description: '',
  requirements: ''
})

// 选项配置
const statusOptions = [
  { label: '全部状态', value: '' },
  { label: '规划中', value: 'planning' },
  { label: '进行中', value: 'active' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
  { label: '暂停', value: 'on_hold' }
]

const typeOptions = [
  { label: '全部类型', value: '' },
  { label: 'Web应用', value: 'web' },
  { label: '移动应用', value: 'mobile' },
  { label: '桌面应用', value: 'desktop' },
  { label: 'API服务', value: 'api' },
  { label: '其他', value: 'other' }
]

const priorityOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' }
]

// 计算属性
const customerOptions = computed(() => [
  { label: '全部客户', value: '' },
  ...customers.value.map(customer => ({
    label: customer.name,
    value: customer.id
  }))
])

// 防抖搜索
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

const debouncedSearch = debounce(() => {
  pagination.value.page = 1
  loadProjects()
}, 300)

// 工具函数 - 使用统一的 UI 工具函数
const { 
  getStatusColor, 
  getStatusText, 
  getTypeColor, 
  getPriorityColor,
  getTypeText,
  getPriorityText,
  formatDate 
} = await import('~/utils/ui-helpers')

const getLocalTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    web: 'blue',
    mobile: 'green',
    desktop: 'purple',
    api: 'orange',
    other: 'gray'
  }
  return colors[type] || 'gray'
}

// 本地类型文本映射 (项目特定)
const getLocalTypeText = (type: string) => {
  const texts: Record<string, string> = {
    web: 'Web应用',
    mobile: '移动应用',
    desktop: '桌面应用',
    api: 'API服务',
    other: '其他'
  }
  return texts[type] || getTypeText(type)
}

const formatCurrency = (amount: number) => {
  if (!amount) return '未设置'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const formatDateRange = (startDate: string, endDate?: string) => {
  const start = new Date(startDate).toLocaleDateString('zh-CN')
  if (!endDate) return `${start} - 进行中`
  const end = new Date(endDate).toLocaleDateString('zh-CN')
  return `${start} - ${end}`
}

const getActionItems = (project: any) => {
  const items: Array<Array<{
    label: string
    icon: string
    click: () => void | Promise<void>
  }>> = []

  if (authStore.hasPermission('archive:read')) {
    items.push([{
      label: '查看详情',
      icon: 'i-heroicons-eye',
      click: () => navigateTo(`/projects/${project.id}`)
    }])
  }

  if (authStore.hasPermission('archive:update')) {
    items.push([{
      label: '编辑',
      icon: 'i-heroicons-pencil',
      click: () => editProject(project)
    }])
  }

  if (authStore.hasPermission('archive:delete')) {
    items.push([{
      label: '删除',
      icon: 'i-heroicons-trash',
      click: () => confirmDelete(project)
    }])
  }

  return items
}

// 数据加载
const loadProjects = async () => {
  loading.value = true
  
  try {
    const params: any = {
      page: pagination.value.page,
      limit: pagination.value.limit
    }

    if (searchQuery.value) {
      params.search = searchQuery.value
    }
    if (statusFilter.value) {
      params.status = statusFilter.value
    }
    if (customerFilter.value) {
      params.customerId = customerFilter.value
    }
    if (typeFilter.value) {
      params.type = typeFilter.value
    }

    const response = await $apiClient('/archives', { query: params })
    
    projects.value = response.data.items
    pagination.value = {
      page: response.data.page,
      limit: response.data.limit,
      total: response.data.total,
      totalPages: response.data.totalPages
    }
  } catch (error) {
    console.error('Load projects error:', error)
    toast.add({
      title: '加载失败',
      description: '获取项目列表失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

const loadCustomers = async () => {
  try {
    const response = await $apiClient('/customers', { query: { limit: 1000 } })
    customers.value = response.data.items || response.data
  } catch (error) {
    console.error('Load customers error:', error)
  }
}

// 表单操作
const resetForm = () => {
  Object.assign(projectForm, {
    name: '',
    customerId: '',
    type: 'web',
    status: 'planning',
    priority: 'medium',
    budget: undefined,
    startDate: '',
    endDate: '',
    progress: 0,
    techStack: '',
    description: '',
    requirements: ''
  })
}

const closeModal = () => {
  showCreateModal.value = false
  editingProject.value = null
  resetForm()
}

const editProject = (project: any) => {
  editingProject.value = project
  Object.assign(projectForm, {
    ...project,
    startDate: project.startDate ? new Date(project.startDate).toISOString().split('T')[0] : '',
    endDate: project.endDate ? new Date(project.endDate).toISOString().split('T')[0] : ''
  })
  showCreateModal.value = true
}

const handleSubmit = async () => {
  submitting.value = true
  
  try {
    const formData = { ...projectForm }
    
    // 转换数字类型
    if (formData.budget) {
      (formData as any).budget = Number(formData.budget)
    }
    if (formData.progress) {
      (formData as any).progress = Number(formData.progress)
    }

    if (editingProject.value) {
      // 更新项目
      await $apiClient(`/archives/${editingProject.value.id}`, { method: 'PUT', body: formData })
      toast.add({
        title: '更新成功',
        description: '项目信息已更新',
        color: 'green'
      })
    } else {
      // 创建项目
      await $apiClient('/archives', { method: 'POST', body: formData })
      toast.add({
        title: '创建成功',
        description: '新项目已创建',
        color: 'green'
      })
    }

    closeModal()
    loadProjects()
  } catch (error: any) {
    console.error('Submit project error:', error)
    toast.add({
      title: '操作失败',
      description: error.message || '操作失败，请稍后重试',
      color: 'red'
    })
  } finally {
    submitting.value = false
  }
}

// 删除操作
const confirmDelete = (project: any) => {
  deletingProject.value = project
  showDeleteModal.value = true
}

const handleDelete = async () => {
  if (!deletingProject.value) return

  deleting.value = true
  
  try {
    await $apiClient(`/archives/${deletingProject.value.id}`, { method: 'DELETE' })
    
    toast.add({
      title: '删除成功',
      description: '项目已删除',
      color: 'green'
    })

    showDeleteModal.value = false
    deletingProject.value = null
    loadProjects()
  } catch (error: any) {
    console.error('Delete project error:', error)
    toast.add({
      title: '删除失败',
      description: error.message || '删除失败，请稍后重试',
      color: 'red'
    })
  } finally {
    deleting.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadCustomers()
  loadProjects()
  
  // 如果URL中有customerId参数，预填充客户
  const customerId = route.query.customerId as string
  if (customerId) {
    projectForm.customerId = customerId
  }
})
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
