<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      title="客户管理"
      description="管理系统中的所有客户信息"
      icon="i-heroicons-users"
      :actions="headerActions"
      :stats="headerStats"
      @action="handleHeaderAction"
    />

    <!-- 数据表格 -->
    <DataTable
      :data="items"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :filters="filterConfig"
      :badge-config="badgeConfig"
      :action-items="getActionItems"
      search-placeholder="搜索客户名称、联系人或邮箱..."
      @search="handleSearch"
      @filter="handleFilter"
      @page-change="handlePageChange"
      @action="handleTableAction"
    >
      <!-- 自定义客户信息列 -->
      <template #name-data="{ row }">
        <div class="space-y-1">
          <div class="flex items-center space-x-2">
            <span class="text-sm font-medium text-gray-900">{{ row.name }}</span>
            <UBadge
              v-if="row.isVip"
              color="yellow"
              variant="soft"
              size="xs"
            >
              VIP
            </UBadge>
          </div>
          <div class="text-xs text-gray-500">
            {{ row.code }} • {{ row.industry || '未分类' }}
          </div>
        </div>
      </template>

      <!-- 自定义联系信息列 -->
      <template #contact-data="{ row }">
        <div class="space-y-1">
          <div class="text-sm text-gray-900">{{ row.contactPerson || '-' }}</div>
          <div class="text-xs text-gray-500">{{ row.contactPhone || '-' }}</div>
          <div class="text-xs text-gray-500">{{ row.contactEmail || '-' }}</div>
        </div>
      </template>
    </DataTable>

    <!-- 创建客户模态框 -->
    <FormModal
      v-model="showCreateModal"
      title="新增客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="createForm.formState"
      :loading="creating"
      :field-options="fieldOptions"
      @submit="handleCreate"
      @cancel="handleCreateCancel"
    />

    <!-- 编辑客户模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="editForm.formState"
      :loading="updating"
      :field-options="fieldOptions"
      @submit="handleUpdate"
      @cancel="handleEditCancel"
    />

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="确认删除"
      :message="`确定要删除客户 ${selectedItem?.name} 吗？`"
      warning-message="此操作不可撤销，相关的项目和工单也将受到影响。"
      :loading="deleting"
      @confirm="handleDelete"
      @cancel="handleDeleteCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { BadgeColor, ActionItem, ButtonVariant } from '~/types/ui'
import { isSuccessResponse } from '~/types/api'

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const authStore = useAuthStore()

// 设置页面标题
useHead({
  title: '客户管理'
})

// 客户验证Schema
const customerSchema = z.object({
  name: z.string().min(1, '请输入客户名称'),
  code: z.string().min(1, '请输入客户编码'),
  type: z.enum(['enterprise', 'individual', 'government', 'nonprofit']),
  industry: z.string().optional(),
  contactPerson: z.string().min(1, '请输入联系人'),
  contactPhone: z.string().min(1, '请输入联系电话'),
  contactEmail: z.string().email('请输入有效的邮箱地址'),
  address: z.string().optional(),
  description: z.string().optional(),
  isVip: z.boolean().optional()
})

// 使用CRUD组合函数
const {
  items,
  pagination,
  loading,
  creating,
  updating,
  deleting,
  selectedItem,
  loadItems,
  createItem,
  updateItem,
  deleteItem,
  setSelectedItem
} = useCrud({
  endpoint: '/customers',
  defaultPageSize: 15,
  defaultSort: '-createdAt',
  onSuccess: (action, data) => {
    if (action === 'create') {
      showCreateModal.value = false
      createForm.resetForm()
    } else if (action === 'update') {
      showEditModal.value = false
      editForm.resetForm()
    } else if (action === 'delete') {
      showDeleteModal.value = false
      setSelectedItem(null)
    }
  }
})

// 使用搜索组合函数
const { handleSearch, handleFilter } = useSearch(loadItems)

// 使用表单组合函数
const createForm = useForm({
  name: '',
  code: '',
  type: 'enterprise',
  industry: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: '',
  isVip: false
})

const editForm = useForm({
  name: '',
  code: '',
  type: 'enterprise',
  industry: '',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: '',
  isVip: false
})

// 界面状态
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)

// 统计数据
const stats = ref({
  total: 0,
  active: 0,
  vip: 0,
  thisMonth: 0
})

// 配置数据
const headerActions = computed(() => [
  {
    key: 'create',
    label: '新增客户',
    icon: 'i-heroicons-plus',
    show: authStore.hasPermission('customer:create')
  },
  {
    key: 'export',
    label: '导出数据',
    icon: 'i-heroicons-arrow-down-tray',
    variant: 'soft' as ButtonVariant,
    show: authStore.hasPermission('customer:export')
  }
])

const headerStats = computed(() => [
  { label: '总客户', value: stats.value.total },
  { label: '活跃客户', value: stats.value.active },
  { label: 'VIP客户', value: stats.value.vip },
  { label: '本月新增', value: stats.value.thisMonth }
])

const columns: any[] = [
  { key: 'name', label: '客户信息', type: 'text' },
  { key: 'type', label: '类型', type: 'badge' },
  { key: 'contact', label: '联系信息', type: 'text' },
  { key: 'status', label: '状态', type: 'badge' },
  { key: 'createdAt', label: '创建时间', type: 'date' },
  { key: 'actions', label: '操作', type: 'actions' }
]

const filterConfig = [
  {
    key: 'status',
    placeholder: '状态筛选',
    options: [
      { label: '全部状态', value: '' },
      { label: '活跃', value: 'active' },
      { label: '停用', value: 'inactive' }
    ]
  },
  {
    key: 'type',
    placeholder: '类型筛选',
    options: [
      { label: '全部类型', value: '' },
      { label: '企业客户', value: 'enterprise' },
      { label: '个人客户', value: 'individual' },
      { label: '政府机构', value: 'government' },
      { label: '非营利组织', value: 'nonprofit' }
    ]
  },
  {
    key: 'isVip',
    placeholder: 'VIP筛选',
    options: [
      { label: '全部客户', value: '' },
      { label: 'VIP客户', value: 'true' },
      { label: '普通客户', value: 'false' }
    ]
  }
]

const badgeConfig = {
  status: {
    active: { color: 'green' as BadgeColor, text: '活跃' },
    inactive: { color: 'gray' as BadgeColor, text: '停用' }
  },
  type: {
    enterprise: { color: 'blue' as BadgeColor, text: '企业' },
    individual: { color: 'green' as BadgeColor, text: '个人' },
    government: { color: 'purple' as BadgeColor, text: '政府' },
    nonprofit: { color: 'yellow' as BadgeColor, text: '非营利' }
  }
}

// 表单字段配置
const customerFields: any[] = [
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'name', label: '客户名称', type: 'text', placeholder: '请输入客户名称', required: true },
      { name: 'code', label: '客户编码', type: 'text', placeholder: '请输入客户编码', required: true },
      { name: 'type', label: '客户类型', type: 'select', required: true },
      { name: 'industry', label: '所属行业', type: 'text', placeholder: '请输入所属行业' }
    ]
  },
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'contactPerson', label: '联系人', type: 'text', placeholder: '请输入联系人姓名', required: true },
      { name: 'contactPhone', label: '联系电话', type: 'text', placeholder: '请输入联系电话', required: true }
    ]
  },
  { name: 'contactEmail', label: '联系邮箱', type: 'email', placeholder: '请输入联系邮箱', required: true },
  { name: 'address', label: '客户地址', type: 'textarea', placeholder: '请输入客户地址', rows: 2 },
  { name: 'description', label: '客户描述', type: 'textarea', placeholder: '请输入客户描述', rows: 3 },
  { name: 'isVip', label: 'VIP客户', type: 'checkbox', placeholder: '标记为VIP客户' }
]

// 字段选项配置
const fieldOptions = ref({
  type: [
    { label: '企业客户', value: 'enterprise' },
    { label: '个人客户', value: 'individual' },
    { label: '政府机构', value: 'government' },
    { label: '非营利组织', value: 'nonprofit' }
  ]
})

// 获取操作项
const getActionItems = (row: any): ActionItem[] => {
  const items: ActionItem[] = []
  
  if (authStore.hasPermission('customer:read')) {
    items.push({
      key: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('customer:update')) {
    items.push({
      key: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
  }
  
  if (authStore.hasPermission('customer:delete')) {
    items.push({
      key: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red'
    })
  }
  
  return items
}

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'create':
      showCreateModal.value = true
      break
    case 'export':
      handleExport()
      break
  }
}

const handleTableAction = (action: string, row: any) => {
  setSelectedItem(row)
  
  switch (action) {
    case 'view':
      navigateTo(`/customers/${row.id}`)
      break
    case 'edit':
      editForm.setFormData(row)
      showEditModal.value = true
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadItems()
}

// CRUD操作
const handleCreate = async (data: any) => {
  await createItem(data)
}

const handleUpdate = async (data: any) => {
  await updateItem(selectedItem.value.id, data)
}

const handleDelete = async () => {
  await deleteItem(selectedItem.value.id)
}

const handleExport = () => {
  const toast = useToast()
  toast.add({
    title: '功能开发中',
    description: '数据导出功能正在开发中',
    color: 'blue'
  })
}

// 取消操作
const handleCreateCancel = () => {
  showCreateModal.value = false
  createForm.resetForm()
}

const handleEditCancel = () => {
  showEditModal.value = false
  editForm.resetForm()
  setSelectedItem(null)
}

const handleDeleteCancel = () => {
  showDeleteModal.value = false
  setSelectedItem(null)
}

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await apiClient.get('/customers/stats')
    if (isSuccessResponse(response)) {
      stats.value = response.data as any
    }
  } catch (error) {
    console.error('Load stats error:', error)
  }
}

// 页面加载时获取数据
onMounted(async () => {
  await Promise.all([
    loadItems(),
    loadStats()
  ])
})
</script>
