<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      title="客户管理"
      description="管理系统中的所有客户信息"
      icon="i-heroicons-users"
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 数据表格 -->
    <DataTable
      :data="customers"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :filters="filters"
      :badge-config="badgeConfig"
      :action-items="getActionItems"
      search-placeholder="搜索客户名称、联系人或邮箱..."
      @search="handleSearch"
      @filter="handleFilter"
      @page-change="handlePageChange"
      @action="handleTableAction"
    >
      <!-- 自定义客户名称列 -->
      <template #name-data="{ row }">
        <div class="flex items-center space-x-3">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-primary-700">
                {{ row.name.charAt(0) }}
              </span>
            </div>
          </div>
          <div>
            <div class="text-sm font-medium text-gray-900">{{ row.name }}</div>
            <div class="text-sm text-gray-500">{{ row.code }}</div>
          </div>
        </div>
      </template>

      <!-- 自定义联系方式列 -->
      <template #contact-data="{ row }">
        <div>
          <div class="text-sm text-gray-900">{{ row.contactPerson }}</div>
          <div class="text-sm text-gray-500">{{ row.contactPhone }}</div>
          <div class="text-sm text-gray-500">{{ row.contactEmail }}</div>
        </div>
      </template>
    </DataTable>

    <!-- 创建客户模态框 -->
    <FormModal
      v-model="showCreateModal"
      title="新增客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="createForm"
      :loading="creating"
      :field-options="fieldOptions"
      @submit="handleCreate"
      @cancel="resetCreateForm"
    />

    <!-- 编辑客户模态框 -->
    <FormModal
      v-model="showEditModal"
      title="编辑客户"
      :fields="customerFields"
      :schema="customerSchema"
      :form-state="editForm"
      :loading="updating"
      :field-options="fieldOptions"
      @submit="handleUpdate"
      @cancel="resetEditForm"
    />

    <!-- 删除确认模态框 -->
    <ConfirmModal
      v-model="showDeleteModal"
      title="确认删除"
      :message="`确定要删除客户 ${selectedCustomer?.name} 吗？`"
      warning-message="此操作不可撤销，相关的项目和服务也将受到影响。"
      :loading="deleting"
      @confirm="handleDelete"
      @cancel="selectedCustomer = null"
    />
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { BadgeColor, ActionItem } from '@/types/ui'

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const authStore = useAuthStore()
const toast = useToast()
const { $apiClient: apiClient } = useNuxtApp()

// 设置页面标题
useHead({
  title: '客户管理'
})

// 客户验证Schema
const customerSchema = z.object({
  name: z.string().min(1, '请输入客户名称'),
  code: z.string().min(1, '请输入客户编码'),
  type: z.enum(['enterprise', 'government', 'individual', 'other']),
  contactPerson: z.string().min(1, '请输入联系人'),
  contactPhone: z.string().min(1, '请输入联系电话'),
  contactEmail: z.string().email('请输入有效的邮箱地址'),
  address: z.string().optional(),
  description: z.string().optional()
})

// 数据状态
const customers = ref<any[]>([])
const pagination = ref({
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
})

// 界面状态
const loading = ref(false)
const creating = ref(false)
const updating = ref(false)
const deleting = ref(false)
const showCreateModal = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const selectedCustomer = ref<any>(null)

// 表单状态
const createForm = reactive({
  name: '',
  code: '',
  type: 'enterprise',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: ''
})

const editForm = reactive({
  name: '',
  code: '',
  type: 'enterprise',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: ''
})

// 页面头部配置
const headerActions = computed(() => [
  {
    key: 'create',
    label: '新增客户',
    icon: 'i-heroicons-plus',
    show: authStore.hasPermission('customer:create')
  }
])

// 表格列配置
const columns: any[] = [
  { key: 'name', label: '客户名称', type: 'text' },
  { key: 'contact', label: '联系信息', type: 'text' },
  { key: 'type', label: '客户类型', type: 'badge' },
  { key: 'status', label: '状态', type: 'badge' },
  { key: 'createdAt', label: '创建时间', type: 'date' },
  { key: 'actions', label: '操作', type: 'actions' }
]

// 筛选器配置
const filters = [
  {
    key: 'status',
    placeholder: '状态筛选',
    options: [
      { label: '全部状态', value: '' },
      { label: '活跃', value: 'active' },
      { label: '停用', value: 'inactive' }
    ]
  },
  {
    key: 'type',
    placeholder: '类型筛选',
    options: [
      { label: '全部类型', value: '' },
      { label: '企业客户', value: 'enterprise' },
      { label: '政府机构', value: 'government' },
      { label: '个人客户', value: 'individual' },
      { label: '其他', value: 'other' }
    ]
  }
]

// 徽章配置
const badgeConfig = {
  status: {
    active: { color: 'green' as const, text: '活跃' },
    inactive: { color: 'gray' as const, text: '停用' }
  },
  type: {
    enterprise: { color: 'blue' as const, text: '企业客户' },
    government: { color: 'purple' as const, text: '政府机构' },
    individual: { color: 'green' as const, text: '个人客户' },
    other: { color: 'gray' as const, text: '其他' }
  }
}

// 表单字段配置
const customerFields: any[] = [
  {
    type: 'grid',
    gridClass: 'grid grid-cols-1 md:grid-cols-2 gap-4',
    fields: [
      { name: 'name', label: '客户名称', type: 'text', placeholder: '请输入客户名称', required: true },
      { name: 'code', label: '客户编码', type: 'text', placeholder: '请输入客户编码', required: true },
      { name: 'type', label: '客户类型', type: 'select', required: true },
      { name: 'contactPerson', label: '联系人', type: 'text', placeholder: '请输入联系人', required: true },
      { name: 'contactPhone', label: '联系电话', type: 'text', placeholder: '请输入联系电话', required: true },
      { name: 'contactEmail', label: '联系邮箱', type: 'email', placeholder: '请输入联系邮箱', required: true }
    ]
  },
  { name: 'address', label: '客户地址', type: 'textarea', placeholder: '请输入客户地址', rows: 2 },
  { name: 'description', label: '客户描述', type: 'textarea', placeholder: '请输入客户描述', rows: 3 }
]

// 字段选项配置
const fieldOptions = {
  type: [
    { label: '企业客户', value: 'enterprise' },
    { label: '政府机构', value: 'government' },
    { label: '个人客户', value: 'individual' },
    { label: '其他', value: 'other' }
  ]
}

// 获取操作项
const getActionItems = (row: any): ActionItem[] => {
  const items: ActionItem[] = []
  
  if (authStore.hasPermission('customer:read')) {
    items.push({
      key: 'view',
      label: '查看详情',
      icon: 'i-heroicons-eye'
    })
  }
  
  if (authStore.hasPermission('customer:update')) {
    items.push({
      key: 'edit',
      label: '编辑',
      icon: 'i-heroicons-pencil'
    })
  }
  
  if (authStore.hasPermission('customer:delete')) {
    items.push({
      key: 'delete',
      label: '删除',
      icon: 'i-heroicons-trash',
      color: 'red' as const
    })
  }
  
  return items
}

// 事件处理
const handleHeaderAction = (action: string) => {
  if (action === 'create') {
    showCreateModal.value = true
  }
}

const handleTableAction = (action: string, row: any) => {
  selectedCustomer.value = row
  
  switch (action) {
    case 'view':
      navigateTo(`/customers/${row.id}`)
      break
    case 'edit':
      Object.assign(editForm, row)
      showEditModal.value = true
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

const handleSearch = (query: string) => {
  // 实现搜索逻辑
  loadCustomers({ search: query })
}

const handleFilter = (filters: Record<string, string>) => {
  // 实现筛选逻辑
  loadCustomers(filters)
}

const handlePageChange = (page: number) => {
  pagination.value.page = page
  loadCustomers()
}

// CRUD操作
const loadCustomers = async (params = {}) => {
  loading.value = true
  
  try {
    const response = await apiClient.paginate('/customers', {
      page: pagination.value.page,
      limit: pagination.value.limit,
      ...params
    })
    
    customers.value = response.data?.items || []
    pagination.value = {
      page: response.data?.page || 1,
      limit: response.data?.limit || 10,
      total: response.data?.total || 0,
      totalPages: response.data?.totalPages || 0
    }
  } catch (error: any) {
    console.error('Load customers error:', error)
    toast.add({
      title: '加载失败',
      description: '获取客户列表失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

const handleCreate = async (data: any) => {
  creating.value = true
  
  try {
    await apiClient.post('/customers', data)
    
    showCreateModal.value = false
    resetCreateForm()
    loadCustomers()
    
    toast.add({
      title: '创建成功',
      description: '客户已创建',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Create customer error:', error)
    toast.add({
      title: '创建失败',
      description: error.message || '创建失败，请稍后重试',
      color: 'red'
    })
  } finally {
    creating.value = false
  }
}

const handleUpdate = async (data: any) => {
  updating.value = true
  
  try {
    await apiClient.put(`/customers/${selectedCustomer.value.id}`, data)
    
    showEditModal.value = false
    resetEditForm()
    loadCustomers()
    
    toast.add({
      title: '更新成功',
      description: '客户信息已更新',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Update customer error:', error)
    toast.add({
      title: '更新失败',
      description: error.message || '更新失败，请稍后重试',
      color: 'red'
    })
  } finally {
    updating.value = false
  }
}

const handleDelete = async () => {
  deleting.value = true
  
  try {
    await apiClient.delete(`/customers/${selectedCustomer.value.id}`)
    
    showDeleteModal.value = false
    selectedCustomer.value = null
    loadCustomers()
    
    toast.add({
      title: '删除成功',
      description: '客户已删除',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Delete customer error:', error)
    toast.add({
      title: '删除失败',
      description: error.message || '删除失败，请稍后重试',
      color: 'red'
    })
  } finally {
    deleting.value = false
  }
}

// 重置表单
const resetCreateForm = () => {
  Object.assign(createForm, {
    name: '',
    code: '',
    type: 'enterprise',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    address: '',
    description: ''
  })
}

const resetEditForm = () => {
  Object.assign(editForm, {
    name: '',
    code: '',
    type: 'enterprise',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    address: '',
    description: ''
  })
  selectedCustomer.value = null
}

// 页面加载时获取数据
onMounted(() => {
  loadCustomers()
})
</script>
