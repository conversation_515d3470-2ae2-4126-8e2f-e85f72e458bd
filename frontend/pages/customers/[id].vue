<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <PageHeader
      :title="customer?.name || '客户详情'"
      :description="`客户编码：${customer?.code || '-'}`"
      icon="i-heroicons-users"
      :status="getStatusText(customer?.status)"
      :status-color="getStatusColor(customer?.status || '') as BadgeColor"
      show-back
      :actions="headerActions"
      @action="handleHeaderAction"
    />

    <!-- 客户基本信息 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 基本信息卡片 -->
      <div class="lg:col-span-2">
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">基本信息</h3>
          </template>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <dt class="text-sm font-medium text-gray-500">客户名称</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.name }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">客户编码</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.code }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">客户类型</dt>
              <dd class="mt-1">
                <UBadge
                  :color="getTypeColor(customer?.type)"
                  variant="soft"
                >
                  {{ getTypeText(customer?.type) }}
                </UBadge>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">状态</dt>
              <dd class="mt-1">
                <UBadge
                  :color="customer?.status === 'active' ? 'green' : 'gray'"
                  variant="soft"
                >
                  {{ customer?.status === 'active' ? '活跃' : '停用' }}
                </UBadge>
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">联系人</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.contactPerson }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">联系电话</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.contactPhone }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">联系邮箱</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.contactEmail }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">公司地址</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.address || '-' }}</dd>
            </div>
            <div class="md:col-span-2">
              <dt class="text-sm font-medium text-gray-500">备注</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ customer?.description || '-' }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">创建时间</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ formatDate(customer?.createdAt) }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">更新时间</dt>
              <dd class="mt-1 text-sm text-gray-900">{{ formatDate(customer?.updatedAt) }}</dd>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 统计信息卡片 -->
      <div class="space-y-6">
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">统计信息</h3>
          </template>

          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">项目数量</span>
              <span class="text-lg font-semibold text-gray-900">{{ stats.projects || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">服务工单</span>
              <span class="text-lg font-semibold text-gray-900">{{ stats.services || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">待处理工单</span>
              <span class="text-lg font-semibold text-yellow-600">{{ stats.pendingServices || 0 }}</span>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-500">配置项数量</span>
              <span class="text-lg font-semibold text-gray-900">{{ stats.configurations || 0 }}</span>
            </div>
          </div>
        </UCard>

        <!-- 快速操作 -->
        <UCard>
          <template #header>
            <h3 class="text-lg font-medium text-gray-900">快速操作</h3>
          </template>

          <div class="space-y-3">
            <UButton
              v-if="authStore.hasPermission('archive:create')"
              block
              variant="soft"
              icon="i-heroicons-folder-plus"
              @click="navigateTo(`/projects/create?customerId=${customer?.id}`)"
            >
              创建项目
            </UButton>
            <UButton
              v-if="authStore.hasPermission('service:create')"
              block
              variant="soft"
              icon="i-heroicons-ticket"
              @click="navigateTo(`/services/create?customerId=${customer?.id}`)"
            >
              创建工单
            </UButton>
            <UButton
              v-if="authStore.hasPermission('configuration:create')"
              block
              variant="soft"
              icon="i-heroicons-cog-6-tooth"
              @click="navigateTo(`/admin/configurations/create?customerId=${customer?.id}`)"
            >
              添加配置
            </UButton>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 相关数据标签页 -->
    <UCard>
      <UTabs v-model="activeTab" :items="tabItems">
        <!-- 项目列表 -->
        <template #projects>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">项目列表</h4>
              <UButton
                v-if="authStore.hasPermission('archive:create')"
                size="sm"
                icon="i-heroicons-plus"
                @click="navigateTo(`/projects/create?customerId=${customer?.id}`)"
              >
                新增项目
              </UButton>
            </div>
            
            <div v-if="projects.length > 0" class="space-y-3">
              <div
                v-for="project in projects"
                :key="project.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <h5 class="font-medium text-gray-900">{{ project.name }}</h5>
                  <p class="text-sm text-gray-500">{{ project.description }}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <UBadge
                    :color="project.status === 'active' ? 'green' : 'gray'"
                    variant="soft"
                  >
                    {{ project.status === 'active' ? '进行中' : '已完成' }}
                  </UBadge>
                  <UButton
                    size="sm"
                    variant="ghost"
                    icon="i-heroicons-arrow-top-right-on-square"
                    @click="navigateTo(`/projects/${project.id}`)"
                  />
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <UIcon name="i-heroicons-folder" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">暂无项目数据</p>
            </div>
          </div>
        </template>

        <!-- 服务工单 -->
        <template #services>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">服务工单</h4>
              <UButton
                v-if="authStore.hasPermission('service:create')"
                size="sm"
                icon="i-heroicons-plus"
                @click="navigateTo(`/services/create?customerId=${customer?.id}`)"
              >
                新增工单
              </UButton>
            </div>
            
            <div v-if="services.length > 0" class="space-y-3">
              <div
                v-for="service in services"
                :key="service.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <h5 class="font-medium text-gray-900">{{ service.title }}</h5>
                  <p class="text-sm text-gray-500">{{ service.description }}</p>
                  <p class="text-xs text-gray-400 mt-1">{{ formatDate(service.createdAt) }}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <UBadge
                    :color="getServiceStatusColor(service.status)"
                    variant="soft"
                  >
                    {{ getServiceStatusText(service.status) }}
                  </UBadge>
                  <UButton
                    size="sm"
                    variant="ghost"
                    icon="i-heroicons-arrow-top-right-on-square"
                    @click="navigateTo(`/services/${service.id}`)"
                  />
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <UIcon name="i-heroicons-ticket" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">暂无工单数据</p>
            </div>
          </div>
        </template>

        <!-- 配置项 -->
        <template #configurations>
          <div class="space-y-4">
            <div class="flex justify-between items-center">
              <h4 class="text-lg font-medium">配置项</h4>
              <UButton
                v-if="authStore.hasPermission('configuration:create')"
                size="sm"
                icon="i-heroicons-plus"
                @click="navigateTo(`/admin/configurations/create?customerId=${customer?.id}`)"
              >
                新增配置
              </UButton>
            </div>
            
            <div v-if="configurations.length > 0" class="space-y-3">
              <div
                v-for="config in configurations"
                :key="config.id"
                class="flex items-center justify-between p-4 bg-gray-50 rounded-lg"
              >
                <div>
                  <h5 class="font-medium text-gray-900">{{ config.name }}</h5>
                  <p class="text-sm text-gray-500">{{ config.description }}</p>
                </div>
                <div class="flex items-center space-x-2">
                  <UBadge
                    :color="config.status === 'active' ? 'green' : 'gray'"
                    variant="soft"
                  >
                    {{ config.status === 'active' ? '启用' : '禁用' }}
                  </UBadge>
                  <UButton
                    size="sm"
                    variant="ghost"
                    icon="i-heroicons-arrow-top-right-on-square"
                    @click="navigateTo(`/admin/configurations/${config.id}`)"
                  />
                </div>
              </div>
            </div>
            <div v-else class="text-center py-8">
              <UIcon name="i-heroicons-cog-6-tooth" class="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-500">暂无配置数据</p>
            </div>
          </div>
        </template>
      </UTabs>
    </UCard>

    <!-- 编辑模态框 -->
    <UModal v-model="showEditModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">编辑客户</h3>
        </template>

        <UForm
          :schema="customerSchema"
          :state="editForm"
          class="space-y-4"
          @submit="handleUpdate"
        >
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <UFormGroup label="客户名称" name="name" required>
              <UInput v-model="editForm.name" placeholder="请输入客户名称" />
            </UFormGroup>

            <UFormGroup label="客户编码" name="code" required>
              <UInput v-model="editForm.code" placeholder="请输入客户编码" />
            </UFormGroup>

            <UFormGroup label="客户类型" name="type" required>
              <USelect
                v-model="editForm.type"
                :options="typeOptions"
                placeholder="请选择客户类型"
              />
            </UFormGroup>

            <UFormGroup label="状态" name="status" required>
              <USelect
                v-model="editForm.status"
                :options="statusOptions"
                placeholder="请选择状态"
              />
            </UFormGroup>

            <UFormGroup label="联系人" name="contactPerson" required>
              <UInput v-model="editForm.contactPerson" placeholder="请输入联系人姓名" />
            </UFormGroup>

            <UFormGroup label="联系电话" name="contactPhone" required>
              <UInput v-model="editForm.contactPhone" placeholder="请输入联系电话" />
            </UFormGroup>

            <UFormGroup label="联系邮箱" name="contactEmail" required>
              <UInput v-model="editForm.contactEmail" type="email" placeholder="请输入联系邮箱" />
            </UFormGroup>

            <UFormGroup label="公司地址" name="address">
              <UInput v-model="editForm.address" placeholder="请输入公司地址" />
            </UFormGroup>
          </div>

          <UFormGroup label="备注" name="description">
            <UTextarea
              v-model="editForm.description"
              placeholder="请输入备注信息"
              :rows="3"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showEditModal = false"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="updating"
            >
              更新
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>

    <!-- 删除确认模态框 -->
    <UModal v-model="showDeleteModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold text-red-600">确认删除</h3>
        </template>

        <div class="space-y-4">
          <p class="text-gray-600">
            确定要删除客户 <strong>{{ customer?.name }}</strong> 吗？
          </p>
          <p class="text-sm text-red-600">
            此操作不可撤销，相关的项目和工单也将受到影响。
          </p>
        </div>

        <template #footer>
          <div class="flex justify-end space-x-3">
            <UButton
              color="gray"
              variant="soft"
              @click="showDeleteModal = false"
            >
              取消
            </UButton>
            <UButton
              color="red"
              :loading="deleting"
              @click="handleDelete"
            >
              确认删除
            </UButton>
          </div>
        </template>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'
import type { BadgeColor } from '~/types/ui'
import { isSuccessResponse } from '~/types/api'
import type { ApiResponse, PaginatedResponse, Customer } from '~/types/api'

// 页面配置
definePageMeta({
  middleware: ['auth', 'permission']
})

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const toast = useToast()

// 设置页面标题
useHead({
  title: computed(() => customer.value?.name ? `${customer.value.name} - 客户详情` : '客户详情')
})

// 客户验证Schema
const customerSchema = z.object({
  name: z.string().min(1, '请输入客户名称'),
  code: z.string().min(1, '请输入客户编码'),
  type: z.enum(['enterprise', 'individual', 'government', 'other']),
  status: z.enum(['active', 'inactive']),
  contactPerson: z.string().min(1, '请输入联系人'),
  contactPhone: z.string().min(1, '请输入联系电话'),
  contactEmail: z.string().email('请输入有效的邮箱地址'),
  address: z.string().optional(),
  description: z.string().optional()
})

// 数据状态
const customer = ref<any>(null)
const stats = ref<any>({})
const projects = ref<any[]>([])
const services = ref<any[]>([])
const configurations = ref<any[]>([])

// 界面状态
const loading = ref(false)
const updating = ref(false)
const deleting = ref(false)
const showEditModal = ref(false)
const showDeleteModal = ref(false)
const activeTab = ref(0)

// 表单状态
const editForm = reactive({
  name: '',
  code: '',
  type: 'enterprise',
  status: 'active',
  contactPerson: '',
  contactPhone: '',
  contactEmail: '',
  address: '',
  description: ''
})

// 选项配置
const statusOptions = [
  { label: '活跃', value: 'active' },
  { label: '停用', value: 'inactive' }
]

const typeOptions = [
  { label: '企业客户', value: 'enterprise' },
  { label: '个人客户', value: 'individual' },
  { label: '政府机构', value: 'government' },
  { label: '其他', value: 'other' }
]

// 标签页配置
const tabItems = [
  { key: 'projects', label: '项目列表', slot: 'projects' },
  { key: 'services', label: '服务工单', slot: 'services' },
  { key: 'configurations', label: '配置项', slot: 'configurations' }
]

// 工具函数
const getTypeColor = (type: string): BadgeColor => {
  const colors: Record<string, BadgeColor> = {
    enterprise: 'blue',
    individual: 'green',
    government: 'purple',
    other: 'gray'
  }
  return colors[type] || 'gray'
}

const getTypeText = (type: string) => {
  const texts: Record<string, string> = {
    enterprise: '企业客户',
    individual: '个人客户',
    government: '政府机构',
    other: '其他'
  }
  return texts[type] || type
}

const getServiceStatusColor = (status: string): BadgeColor => {
  const colors: Record<string, BadgeColor> = {
    pending: 'yellow',
    processing: 'blue',
    completed: 'green',
    closed: 'gray'
  }
  return colors[status] || 'gray'
}

const getServiceStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待处理',
    processing: '处理中',
    completed: '已完成',
    closed: '已关闭'
  }
  return texts[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 数据加载
const loadCustomerDetail = async () => {
  loading.value = true
  
  try {
    const customerId = route.params.id as string
    
    // 并行加载客户详情和相关数据
    const [
      customerResponse,
      statsResponse,
      projectsResponse,
      servicesResponse,
      configurationsResponse
    ] = await Promise.all([
      apiClient.get(`/customers/${customerId}`),
      apiClient.get(`/customers/${customerId}/stats`),
      apiClient.paginate('/archives', { customerId, limit: 10 }),
      apiClient.paginate('/services', { customerId, limit: 10 }),
      apiClient.paginate('/configurations', { customerId, limit: 10 })
    ])

    if (isSuccessResponse(customerResponse)) {
      customer.value = customerResponse.data
    }
    if (isSuccessResponse(statsResponse)) {
      stats.value = statsResponse.data
    }
    // 处理分页响应
    projects.value = (projectsResponse as any)?.items || []
    services.value = (servicesResponse as any)?.items || []
    configurations.value = (configurationsResponse as any)?.items || []

    // 填充编辑表单
    Object.assign(editForm, customer.value)
  } catch (error: any) {
    console.error('Load customer detail error:', error)
    
    if (error.statusCode === 404) {
      throw createError({
        statusCode: 404,
        statusMessage: '客户不存在'
      })
    }
    
    toast.add({
      title: '加载失败',
      description: '获取客户详情失败，请稍后重试',
      color: 'red'
    })
  } finally {
    loading.value = false
  }
}

// 更新客户
const handleUpdate = async () => {
  updating.value = true
  
  try {
    const customerId = route.params.id as string
    const response = await apiClient.put(`/customers/${customerId}`, editForm)
    
    if (isSuccessResponse(response)) {
      customer.value = response.data
    }
    showEditModal.value = false
    
    toast.add({
      title: '更新成功',
      description: '客户信息已更新',
      color: 'green'
    })
  } catch (error: any) {
    console.error('Update customer error:', error)
    toast.add({
      title: '更新失败',
      description: error.message || '更新失败，请稍后重试',
      color: 'red'
    })
  } finally {
    updating.value = false
  }
}

// 删除客户
const handleDelete = async () => {
  deleting.value = true
  
  try {
    const customerId = route.params.id as string
    await apiClient.delete(`/customers/${customerId}`)
    
    toast.add({
      title: '删除成功',
      description: '客户已删除',
      color: 'green'
    })

    router.push('/customers')
  } catch (error: any) {
    console.error('Delete customer error:', error)
    toast.add({
      title: '删除失败',
      description: error.message || '删除失败，请稍后重试',
      color: 'red'
    })
  } finally {
    deleting.value = false
  }
}

// 配置数据
const headerActions = computed<any[]>(() => [
  {
    key: 'edit',
    label: '编辑',
    icon: 'i-heroicons-pencil',
    show: authStore.hasPermission('customer:update')
  },
  {
    key: 'delete',
    label: '删除',
    icon: 'i-heroicons-trash',
    color: 'red',
    show: authStore.hasPermission('customer:delete')
  }
])

// 工具函数
const getStatusColor = (status: string) => {
  const colors: Record<string, string> = {
    active: 'green',
    inactive: 'gray'
  }
  return colors[status] || 'gray'
}

const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    active: '活跃',
    inactive: '停用'
  }
  return texts[status] || status
}

// 事件处理
const handleHeaderAction = (action: string) => {
  switch (action) {
    case 'edit':
      showEditModal.value = true
      break
    case 'delete':
      showDeleteModal.value = true
      break
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadCustomerDetail()
})
</script>
