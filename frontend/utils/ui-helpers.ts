// UI 工具函数
import type { BadgeColor } from '~/types/ui'

// 状态颜色映射
export const getStatusColor = (status: string): BadgeColor => {
  const statusColorMap: Record<string, BadgeColor> = {
    // 通用状态
    'active': 'green',
    'inactive': 'red',
    'pending': 'yellow',
    'processing': 'blue',
    'completed': 'green',
    'cancelled': 'red',
    'draft': 'gray',
    'approved': 'green',
    'rejected': 'red',
    'suspended': 'yellow',
    
    // 服务工单状态
    'open': 'blue',
    'in_progress': 'yellow',
    'resolved': 'green',
    'closed': 'gray',
    'reopened': 'purple',
    
    // 项目状态
    'planning': 'blue',
    'development': 'yellow',
    'testing': 'purple',
    'deployment': 'indigo',
    'maintenance': 'gray'
  }
  return statusColorMap[status] || 'gray'
}

// 优先级颜色映射
export const getPriorityColor = (priority: string): BadgeColor => {
  const priorityColorMap: Record<string, BadgeColor> = {
    'low': 'gray',
    'medium': 'yellow',
    'high': 'red',
    'urgent': 'purple',
    'critical': 'red'
  }
  return priorityColorMap[priority] || 'gray'
}

// 类型颜色映射
export const getTypeColor = (type: string): BadgeColor => {
  const typeColorMap: Record<string, BadgeColor> = {
    // 服务类型
    'bug': 'red',
    'feature': 'blue',
    'enhancement': 'green',
    'support': 'yellow',
    'maintenance': 'gray',
    'consultation': 'purple',
    
    // 项目类型
    'development': 'blue',
    'integration': 'green',
    'migration': 'yellow',
    'upgrade': 'purple',
    'customization': 'indigo'
  }
  return typeColorMap[type] || 'gray'
}

// SLA 状态颜色映射
export const getSlaStatusColor = (slaStatus: string): BadgeColor => {
  const slaColorMap: Record<string, BadgeColor> = {
    'met': 'green',
    'warning': 'yellow',
    'breached': 'red',
    'unknown': 'gray'
  }
  return slaColorMap[slaStatus] || 'gray'
}

// 状态文本映射
export const getStatusText = (status: string): string => {
  const statusTextMap: Record<string, string> = {
    // 通用状态
    'active': '活跃',
    'inactive': '非活跃',
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'cancelled': '已取消',
    'draft': '草稿',
    'approved': '已批准',
    'rejected': '已拒绝',
    'suspended': '已暂停',
    
    // 服务工单状态
    'open': '待处理',
    'in_progress': '处理中',
    'resolved': '已解决',
    'closed': '已关闭',
    'reopened': '重新打开',
    
    // 项目状态
    'planning': '规划中',
    'development': '开发中',
    'testing': '测试中',
    'deployment': '部署中',
    'maintenance': '维护中'
  }
  return statusTextMap[status] || status
}

// 优先级文本映射
export const getPriorityText = (priority: string): string => {
  const priorityTextMap: Record<string, string> = {
    'low': '低',
    'medium': '中',
    'high': '高',
    'urgent': '紧急',
    'critical': '严重'
  }
  return priorityTextMap[priority] || priority
}

// 类型文本映射
export const getTypeText = (type: string): string => {
  const typeTextMap: Record<string, string> = {
    // 服务类型
    'bug': '错误修复',
    'feature': '新功能',
    'enhancement': '功能增强',
    'support': '技术支持',
    'maintenance': '维护',
    'consultation': '咨询',
    
    // 项目类型
    'development': '开发项目',
    'integration': '系统集成',
    'migration': '数据迁移',
    'upgrade': '系统升级',
    'customization': '定制开发'
  }
  return typeTextMap[type] || type
}

// 分类文本映射
export const getCategoryText = (category: string): string => {
  const categoryTextMap: Record<string, string> = {
    'system': '系统问题',
    'network': '网络问题',
    'database': '数据库问题',
    'application': '应用问题',
    'hardware': '硬件问题',
    'security': '安全问题',
    'performance': '性能问题',
    'integration': '集成问题',
    'user': '用户问题',
    'data': '数据问题'
  }
  return categoryTextMap[category] || category
}

// SLA 状态文本映射
export const getSlaStatusText = (slaStatus: string): string => {
  const slaTextMap: Record<string, string> = {
    'met': '已达标',
    'warning': '即将超时',
    'breached': '已超时',
    'unknown': '未知'
  }
  return slaTextMap[slaStatus] || slaStatus
}

// SLA 状态样式类映射
export const getSlaStatusClass = (slaStatus: string): string => {
  const slaClassMap: Record<string, string> = {
    'met': 'text-green-600',
    'warning': 'text-yellow-600',
    'breached': 'text-red-600',
    'unknown': 'text-gray-600'
  }
  return slaClassMap[slaStatus] || 'text-gray-600'
}

// 日期格式化函数
export const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '-'
  
  try {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  } catch (error) {
    console.error('Invalid date:', dateString)
    return '-'
  }
}

// SLA 时间格式化
export const formatSlaTime = (deadline: string | undefined): string => {
  if (!deadline) return '-'
  
  try {
    const now = new Date()
    const deadlineDate = new Date(deadline)
    const diff = deadlineDate.getTime() - now.getTime()
    
    if (diff < 0) {
      const hours = Math.abs(Math.floor(diff / (1000 * 60 * 60)))
      return `已超时 ${hours} 小时`
    } else {
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      
      if (hours > 24) {
        const days = Math.floor(hours / 24)
        return `剩余 ${days} 天`
      } else if (hours > 0) {
        return `剩余 ${hours} 小时 ${minutes} 分钟`
      } else {
        return `剩余 ${minutes} 分钟`
      }
    }
  } catch (error) {
    console.error('Invalid SLA deadline:', deadline)
    return '-'
  }
}

// 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`
}

// 进度百分比格式化
export const formatProgress = (progress: number): string => {
  return `${Math.round(progress)}%`
}

// 货币格式化
export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}