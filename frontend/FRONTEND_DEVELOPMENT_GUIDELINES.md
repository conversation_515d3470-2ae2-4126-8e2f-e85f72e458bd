# 前端开发准则

基于项目 TypeScript 错误分析，形成的前端开发最佳实践和错误预防准则。

## 错误分析总结

### 错误统计
- **总错误数量**: 128 个
- **涉及文件**: 20 个
- **主要错误类型**: 类型不匹配、属性缺失、API 响应类型错误

### 错误分布
```
组件相关错误: 21 个 (16%)
页面相关错误: 84 个 (66%) 
状态管理错误: 15 个 (12%)
工具类错误: 9 个 (7%)
```

## 1. 类型定义与接口设计

### 🚨 **常见错误模式**

#### 1.1 API 响应类型不一致
```typescript
// ❌ 错误：假设 API 响应有 success 和 data 字段
if (data.success) {
  user.value = data.data
}

// ✅ 正确：定义准确的 API 响应类型
interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
}

interface LoginResponse {
  user: User
  token: string
}

// 使用时进行类型断言或验证
const response = await $fetch<ApiResponse<LoginResponse>>('/auth/login')
if (response.success) {
  user.value = response.data.user
}
```

#### 1.2 组件 Props 类型定义不完整
```typescript
// ❌ 错误：字符串类型过于宽泛
interface Column {
  key: string
  label: string
  type: string // 过于宽泛
}

// ✅ 正确：使用联合类型或枚举
interface Column {
  key: string
  label: string
  type: 'text' | 'date' | 'actions' | 'progress' | 'badge' | 'currency'
}

// 或使用枚举
enum ColumnType {
  Text = 'text',
  Date = 'date',
  Actions = 'actions',
  Progress = 'progress',
  Badge = 'badge',
  Currency = 'currency'
}
```

### 🎯 **最佳实践**

#### 1.1 统一 API 响应类型
```typescript
// types/api.ts
export interface BaseApiResponse {
  success: boolean
  message?: string
}

export interface ApiSuccessResponse<T> extends BaseApiResponse {
  success: true
  data: T
}

export interface ApiErrorResponse extends BaseApiResponse {
  success: false
  message: string
  errors?: Record<string, string[]>
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse

// 使用类型守卫
export function isSuccessResponse<T>(
  response: ApiResponse<T>
): response is ApiSuccessResponse<T> {
  return response.success === true
}
```

#### 1.2 严格的表单字段类型
```typescript
// types/form.ts
export interface BaseFormField {
  name: string
  label: string
  required?: boolean
  placeholder?: string
}

export interface InputField extends BaseFormField {
  type: 'text' | 'email' | 'password' | 'number' | 'tel'
}

export interface SelectField extends BaseFormField {
  type: 'select'
  options: Array<{ label: string; value: string | number }>
}

export interface TextareaField extends BaseFormField {
  type: 'textarea'
  rows?: number
}

export interface FormGroupField {
  type: 'group'
  gridClass: string
  fields: FormField[]
}

export type FormField = InputField | SelectField | TextareaField | FormGroupField
```

## 2. Nuxt.js 特定类型问题

### 🚨 **常见错误模式**

#### 2.1 PageMeta 配置错误
```typescript
// ❌ 错误：layout 属性类型错误
definePageMeta({
  layout: false, // boolean 不能赋值给 string
  auth: false
})

// ✅ 正确：扩展 PageMeta 接口
declare global {
  interface PageMeta {
    layout?: string | false
    auth?: boolean
    middleware?: string | string[]
    [key: string]: any
  }
}

definePageMeta({
  layout: false,
  auth: false
})
```

#### 2.2 自动导入类型缺失
```typescript
// ❌ 错误：全局函数类型定义不完整
// global.d.ts 中缺少准确的类型定义

// ✅ 正确：完善全局类型定义
declare global {
  const definePageMeta: (meta: PageMeta) => void
  const useHead: (head: any) => void
  const useRoute: () => RouteLocationNormalizedLoaded
  const useRouter: () => Router
  const navigateTo: (to: string | RouteLocationRaw, options?: NavigateToOptions) => Promise<void> | void
  // ... 其他自动导入函数
}
```

### 🎯 **最佳实践**

#### 2.1 完善的全局类型声明
```typescript
// types/global.d.ts
import type { RouteLocationRaw, RouteLocationNormalizedLoaded } from 'vue-router'
import type { UseFetchOptions } from 'nuxt/app'

declare global {
  // Nuxt 3 自动导入函数的准确类型
  const useAsyncData: <T>(
    key: string,
    handler: () => Promise<T>,
    options?: any
  ) => any
  
  const useFetch: <T>(
    url: string,
    options?: UseFetchOptions<T>
  ) => any
  
  const navigateTo: (
    to: string | RouteLocationRaw,
    options?: { replace?: boolean; external?: boolean }
  ) => Promise<void> | void

  // 项目特定的全局函数
  interface ComponentCustomProperties {
    $config: RuntimeConfig
    $apiClient: ApiClient
  }
}
```

## 3. API 客户端与网络请求

### 🚨 **常见错误模式**

#### 3.1 HTTP Headers 类型错误
```typescript
// ❌ 错误：Headers 接口不支持自定义属性
const headers: Headers = {
  'Authorization': `Bearer ${token}`, // 错误
  'Content-Type': 'application/json'  // 错误
}

// ✅ 正确：使用 Record 或 HeadersInit
const headers: HeadersInit = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}

// 或使用 Record
const headers: Record<string, string> = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

#### 3.2 HTTP 方法类型不匹配
```typescript
// ❌ 错误：字符串字面量不匹配 fetch 的 method 类型
return this.client(url, { 
  method: 'GET', // 类型错误
  ...options 
})

// ✅ 正确：使用正确的类型或类型断言
const method: RequestInit['method'] = 'GET'
return this.client(url, { method, ...options })

// 或使用 as const
return this.client(url, { 
  method: 'GET' as const,
  ...options 
})
```

### 🎯 **最佳实践**

#### 3.1 类型安全的 API 客户端
```typescript
// utils/api.ts
export interface ApiClientOptions {
  baseURL?: string
  timeout?: number
  headers?: Record<string, string>
}

export interface RequestOptions {
  headers?: Record<string, string>
  params?: Record<string, any>
  timeout?: number
}

export class ApiClient {
  private baseURL: string
  private defaultHeaders: Record<string, string>

  constructor(options: ApiClientOptions = {}) {
    this.baseURL = options.baseURL || ''
    this.defaultHeaders = options.headers || {}
  }

  private async request<T>(
    url: string,
    options: RequestInit & RequestOptions = {}
  ): Promise<T> {
    const { headers, params, ...fetchOptions } = options
    
    const finalUrl = params ? `${url}?${new URLSearchParams(params)}` : url
    const finalHeaders: HeadersInit = {
      ...this.defaultHeaders,
      ...headers
    }

    const response = await $fetch<T>(finalUrl, {
      ...fetchOptions,
      headers: finalHeaders,
      baseURL: this.baseURL
    })

    return response
  }

  async get<T>(url: string, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(url, { ...options, method: 'GET' })
  }

  async post<T>(url: string, body?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    })
  }
}
```

## 4. 组件开发规范

### 🚨 **常见错误模式**

#### 4.1 动态插槽类型不安全
```typescript
// ❌ 错误：数组类型推断为 never[]
const items = [] // 类型推断为 never[]
items.push({
  action: 'view',
  label: '查看',
  icon: 'i-heroicons-eye'
}) // 错误：不能推入到 never[]

// ✅ 正确：明确数组类型
interface ActionItem {
  action: string
  label: string
  icon: string
  color?: string
}

const items: ActionItem[] = []
items.push({
  action: 'view',
  label: '查看',
  icon: 'i-heroicons-eye'
})
```

#### 4.2 UI 库组件 Props 类型错误
```typescript
// ❌ 错误：字符串不能赋值给特定的联合类型
<UBadge :color="getStatusColor(status)" />
// getStatusColor 返回 string，但 color 需要 BadgeColor

// ✅ 正确：返回正确的类型或使用类型断言
const getStatusColor = (status: string): BadgeColor => {
  const colorMap: Record<string, BadgeColor> = {
    'active': 'green',
    'inactive': 'red',
    'pending': 'yellow'
  }
  return colorMap[status] || 'gray'
}

// 或使用类型断言（不推荐）
<UBadge :color="getStatusColor(status) as BadgeColor" />
```

### 🎯 **最佳实践**

#### 4.1 类型安全的组件 Props
```typescript
// components/DataTable.vue
<script setup lang="ts" generic="T extends Record<string, any>">
interface Column {
  key: keyof T
  label: string
  type?: 'text' | 'date' | 'actions' | 'progress' | 'badge' | 'currency'
  sortable?: boolean
  width?: string
}

interface Props {
  data: T[]
  columns: Column[]
  loading?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  emptyState?: {
    icon: string
    title: string
    description: string
  }
  onAction?: (action: string, row: T) => void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: true,
  searchPlaceholder: '搜索...',
  emptyState: () => ({
    icon: 'i-heroicons-inbox',
    title: '暂无数据',
    description: '当前没有任何数据'
  })
})
</script>
```

#### 4.2 类型安全的 UI 工具函数
```typescript
// utils/ui.ts
export type BadgeColor = 'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'indigo' | 'purple' | 'pink'

export const getStatusColor = (status: string): BadgeColor => {
  const statusColorMap: Record<string, BadgeColor> = {
    'active': 'green',
    'inactive': 'red',
    'pending': 'yellow',
    'processing': 'blue',
    'completed': 'green',
    'cancelled': 'red',
    'draft': 'gray'
  }
  return statusColorMap[status] || 'gray'
}

export const getPriorityColor = (priority: string): BadgeColor => {
  const priorityColorMap: Record<string, BadgeColor> = {
    'low': 'gray',
    'medium': 'yellow',
    'high': 'red',
    'urgent': 'purple'
  }
  return priorityColorMap[priority] || 'gray'
}
```

## 5. 状态管理最佳实践

### 🚨 **常见错误模式**

#### 5.1 Store 中的类型推断错误
```typescript
// ❌ 错误：类型推断不准确
export const useAuthStore = defineStore('auth', () => {
  const user = ref(null) // 类型推断为 Ref<null>
  
  const login = async (credentials) => { // 参数类型丢失
    const data = await $fetch('/auth/login', {
      method: 'POST',
      body: credentials
    })
    user.value = data.user // 类型错误
  }
})

// ✅ 正确：明确类型定义
export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  
  const login = async (credentials: LoginCredentials): Promise<LoginResult> => {
    const response = await $fetch<ApiResponse<LoginResponse>>('/auth/login', {
      method: 'POST',
      body: credentials
    })
    
    if (isSuccessResponse(response)) {
      user.value = response.data.user
      return { success: true, message: '登录成功' }
    } else {
      return { success: false, message: response.message }
    }
  }
  
  return { user, login }
})
```

### 🎯 **最佳实践**

#### 5.1 类型安全的 Pinia Store
```typescript
// stores/auth.ts
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  role: {
    id: string
    name: string
    permissions: string[]
  }
  department?: string
  phone?: string
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResult {
  success: boolean
  message: string
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isAuthenticated = computed(() => !!user.value && !!token.value)

  // 计算属性
  const hasPermission = computed(() => (permission: string): boolean => {
    if (!user.value) return false
    return user.value.role.permissions.includes(permission) || 
           user.value.role.permissions.includes('admin:all')
  })

  // 方法
  const login = async (credentials: LoginCredentials): Promise<LoginResult> => {
    try {
      const response = await $fetch<ApiResponse<{ user: User; token: string }>>('/auth/login', {
        method: 'POST',
        body: credentials
      })

      if (isSuccessResponse(response)) {
        user.value = response.data.user
        token.value = response.data.token
        
        // 设置认证 cookie
        const tokenCookie = useCookie('auth-token', {
          default: () => null,
          maxAge: credentials.remember ? 60 * 60 * 24 * 30 : 60 * 60 * 24, // 30天或1天
          httpOnly: false,
          secure: true,
          sameSite: 'strict'
        })
        tokenCookie.value = response.data.token

        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: response.message }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { success: false, message: '登录失败，请稍后重试' }
    }
  }

  const logout = async (): Promise<void> => {
    try {
      if (token.value) {
        await $fetch('/auth/logout', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token.value}`
          }
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      user.value = null
      token.value = null
      
      const tokenCookie = useCookie('auth-token')
      tokenCookie.value = null
      
      await navigateTo('/login')
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isAuthenticated,
    
    // 计算属性
    hasPermission,
    
    // 方法
    login,
    logout
  }
})
```

## 6. 表单处理与验证

### 🚨 **常见错误模式**

#### 6.1 表单字段结构不匹配
```typescript
// ❌ 错误：嵌套结构与组件期望不匹配
const customerFields = [
  {
    type: 'group',
    gridClass: 'grid-cols-2',
    fields: [
      { name: 'name', label: '客户名称', type: 'text' },
      { name: 'email', label: '邮箱', type: 'email' }
    ]
  }
] // 类型错误：组件期望扁平结构

// ✅ 正确：匹配组件的字段结构
const customerFields: FormField[] = [
  {
    name: 'name',
    label: '客户名称',
    type: 'text',
    required: true,
    placeholder: '请输入客户名称'
  },
  {
    name: 'email',
    label: '邮箱',
    type: 'email',
    required: true,
    placeholder: '请输入邮箱地址'
  }
]
```

### 🎯 **最佳实践**

#### 6.1 统一的表单配置系统
```typescript
// composables/useFormConfig.ts
export interface FormFieldConfig {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'select' | 'textarea' | 'date' | 'checkbox'
  required?: boolean
  placeholder?: string
  options?: Array<{ label: string; value: string | number }>
  validation?: ZodSchema
  gridClass?: string
  rows?: number
}

export const useFormConfig = () => {
  const createUserFields = (): FormFieldConfig[] => [
    {
      name: 'username',
      label: '用户名',
      type: 'text',
      required: true,
      placeholder: '请输入用户名',
      validation: z.string().min(3).max(50)
    },
    {
      name: 'email',
      label: '邮箱',
      type: 'email',
      required: true,
      placeholder: '请输入邮箱地址',
      validation: z.string().email()
    },
    {
      name: 'roleId',
      label: '角色',
      type: 'select',
      required: true,
      options: [], // 动态加载
      validation: z.string().min(1)
    }
  ]

  const createCustomerFields = (): FormFieldConfig[] => [
    {
      name: 'name',
      label: '客户名称',
      type: 'text',
      required: true,
      placeholder: '请输入客户名称'
    },
    {
      name: 'contactPerson',
      label: '联系人',
      type: 'text',
      required: true,
      placeholder: '请输入联系人姓名'
    }
  ]

  return {
    createUserFields,
    createCustomerFields
  }
}
```

## 7. 开发工具配置

### 🎯 **推荐配置**

#### 7.1 ESLint 配置优化
```javascript
// eslint.config.js
export default createConfigForNuxt({
  features: {
    typescript: true,
    vue: true,
    nuxt: true
  }
}).append({
  rules: {
    // TypeScript 严格规则
    '@typescript-eslint/no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_',
      ignoreRestSiblings: true
    }],
    '@typescript-eslint/no-explicit-any': 'error', // 禁止 any
    '@typescript-eslint/explicit-function-return-type': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'error',
    
    // Vue 规则
    'vue/component-definition-name-casing': ['error', 'PascalCase'],
    'vue/component-name-in-template-casing': ['error', 'PascalCase'],
    'vue/define-props-declaration': ['error', 'type-based'],
    'vue/define-emits-declaration': ['error', 'type-based'],
    
    // 通用规则
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error'
  }
})
```

#### 7.2 TypeScript 配置优化
```json
{
  "extends": "./.nuxt/tsconfig.json",
  "compilerOptions": {
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true
  }
}
```

## 8. 错误预防检查清单

### 🔍 **开发前检查**
- [ ] API 响应类型是否正确定义？
- [ ] 组件 Props 类型是否完整？
- [ ] 全局类型声明是否覆盖所有自动导入？
- [ ] 表单字段配置是否匹配组件期望？

### 🔍 **开发中检查**
- [ ] 数组初始化时是否指定类型？
- [ ] UI 库组件的 Props 类型是否正确？
- [ ] 可选属性访问是否使用可选链？
- [ ] API 调用是否有错误处理？

### 🔍 **开发后检查**
- [ ] 运行 `npx nuxi typecheck` 无错误
- [ ] 运行 `npm run lint` 无警告
- [ ] 所有组件能正常渲染
- [ ] 网络请求能正常处理响应

## 9. 常用工具函数

### 9.1 类型守卫函数
```typescript
// utils/guards.ts
export function isDefined<T>(value: T | undefined | null): value is T {
  return value !== undefined && value !== null
}

export function isString(value: unknown): value is string {
  return typeof value === 'string'
}

export function hasProperty<T extends object, K extends PropertyKey>(
  obj: T,
  key: K
): obj is T & Record<K, unknown> {
  return key in obj
}
```

### 9.2 API 响应处理
```typescript
// utils/api-helpers.ts
export async function handleApiCall<T>(
  apiCall: () => Promise<ApiResponse<T>>
): Promise<{ data?: T; error?: string }> {
  try {
    const response = await apiCall()
    
    if (isSuccessResponse(response)) {
      return { data: response.data }
    } else {
      return { error: response.message }
    }
  } catch (error) {
    console.error('API call failed:', error)
    return { error: '网络请求失败，请稍后重试' }
  }
}
```

## 总结

这份开发准则基于实际项目中发现的 128 个 TypeScript 错误制定，涵盖了前端开发中最常见的类型问题。遵循这些准则可以：

1. **提高代码质量**: 减少运行时错误
2. **提升开发效率**: 更好的 IDE 支持和自动补全
3. **增强代码可维护性**: 清晰的类型定义和接口
4. **改善团队协作**: 统一的代码风格和约定

建议在每次开发完成后运行类型检查，并定期回顾和更新这份准则。