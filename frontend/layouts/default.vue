<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div
      :class="[
        'fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      ]"
    >
      <!-- Logo -->
      <div class="flex items-center justify-center h-16 px-4 bg-blue-600">
        <h1 class="text-xl font-bold text-white">运维管理系统</h1>
      </div>

      <!-- 导航菜单 -->
      <nav class="mt-8">
        <div class="px-4 space-y-2">
          <template v-for="item in menuItems" :key="item.name">
            <!-- 单级菜单 -->
            <NuxtLink
              v-if="!item.children"
              :to="item.path"
              :class="[
                'flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors',
                isActiveRoute(item.path)
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              ]"
            >
              <UIcon :name="item.icon" class="w-5 h-5 mr-3" />
              {{ item.name }}
            </NuxtLink>

            <!-- 多级菜单 -->
            <div v-else>
              <button
                :class="[
                  'flex items-center justify-between w-full px-4 py-2 text-sm font-medium rounded-lg transition-colors',
                  hasActiveChild(item.children)
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                ]"
                @click="toggleSubmenu(item.name)"
              >
                <div class="flex items-center">
                  <UIcon :name="item.icon" class="w-5 h-5 mr-3" />
                  {{ item.name }}
                </div>
                <UIcon
                  :name="openSubmenus.includes(item.name) ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'"
                  class="w-4 h-4"
                />
              </button>

              <!-- 子菜单 -->
              <div
                v-show="openSubmenus.includes(item.name)"
                class="mt-2 ml-4 space-y-1"
              >
                <NuxtLink
                  v-for="child in item.children"
                  :key="child.name"
                  :to="child.path"
                  :class="[
                    'flex items-center px-4 py-2 text-sm rounded-lg transition-colors',
                    isActiveRoute(child.path)
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:bg-gray-100 hover:text-gray-700'
                  ]"
                >
                  <UIcon :name="child.icon" class="w-4 h-4 mr-3" />
                  {{ child.name }}
                </NuxtLink>
              </div>
            </div>
          </template>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="lg:pl-64 flex flex-col min-h-screen">
      <!-- 顶部导航栏 -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
          <!-- 移动端菜单按钮 -->
          <button
            class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100"
            @click="toggleSidebar"
          >
            <UIcon name="i-heroicons-bars-3" class="w-6 h-6" />
          </button>

          <!-- 面包屑导航 -->
          <nav class="hidden sm:flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
              <li v-for="(crumb, index) in breadcrumbs" :key="crumb.name">
                <div class="flex items-center">
                  <UIcon
                    v-if="index > 0"
                    name="i-heroicons-chevron-right"
                    class="w-5 h-5 text-gray-400 mr-4"
                  />
                  <NuxtLink
                    v-if="crumb.path && index < breadcrumbs.length - 1"
                    :to="crumb.path"
                    class="text-sm font-medium text-gray-500 hover:text-gray-700"
                  >
                    {{ crumb.name }}
                  </NuxtLink>
                  <span
                    v-else
                    class="text-sm font-medium text-gray-900"
                  >
                    {{ crumb.name }}
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          <!-- 用户菜单 -->
          <div class="flex items-center space-x-4">
            <!-- 通知 -->
            <button class="p-2 text-gray-400 hover:text-gray-500">
              <UIcon name="i-heroicons-bell" class="w-6 h-6" />
            </button>

            <!-- 用户下拉菜单 -->
            <UDropdown :items="userMenuItems">
              <button class="flex items-center space-x-3 text-sm">
                <img
                  :src="authStore.user?.avatar || '/default-avatar.png'"
                  :alt="authStore.user?.name"
                  class="w-8 h-8 rounded-full"
                >
                <span class="hidden md:block font-medium text-gray-700">
                  {{ authStore.user?.name }}
                </span>
                <UIcon name="i-heroicons-chevron-down" class="w-4 h-4 text-gray-400" />
              </button>
            </UDropdown>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="flex-1 px-6 py-6">
        <slot />
      </main>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="sidebarOpen"
      class="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
      @click="closeSidebar"
    />
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const route = useRoute()

// 侧边栏状态
const sidebarOpen = ref(false)
const openSubmenus = ref<string[]>(['系统管理'])

// 菜单项配置
const menuItems = computed(() => [
  {
    name: '仪表盘',
    path: '/',
    icon: 'i-heroicons-home'
  },
  {
    name: '客户管理',
    path: '/customers',
    icon: 'i-heroicons-users'
  },
  {
    name: '项目档案',
    path: '/projects',
    icon: 'i-heroicons-folder'
  },
  {
    name: '服务工单',
    path: '/services',
    icon: 'i-heroicons-ticket'
  },
  ...(authStore.hasPermission('admin:access') ? [{
    name: '系统管理',
    icon: 'i-heroicons-cog-6-tooth',
    children: [
      ...(authStore.hasPermission('user:read') ? [{
        name: '用户管理',
        path: '/admin/users',
        icon: 'i-heroicons-users'
      }] : []),
      ...(authStore.hasPermission('role:read') ? [{
        name: '角色权限',
        path: '/admin/roles',
        icon: 'i-heroicons-shield-check'
      }] : []),
      ...(authStore.hasPermission('audit:read') ? [{
        name: '审计日志',
        path: '/admin/audit-logs',
        icon: 'i-heroicons-document-text'
      }] : []),
      ...(authStore.hasPermission('configuration:read') ? [{
        name: '配置管理',
        path: '/admin/configurations',
        icon: 'i-heroicons-adjustments-horizontal'
      }] : []),
      ...(authStore.hasPermission('sla:read') ? [{
        name: 'SLA管理',
        path: '/admin/sla',
        icon: 'i-heroicons-clock'
      }] : []),
      ...(authStore.hasPermission('notification:read') ? [{
        name: '通知设置',
        path: '/admin/notifications',
        icon: 'i-heroicons-bell'
      }] : [])
    ]
  }] : [])
])

// 用户菜单项
const userMenuItems = [
  [{
    label: '个人资料',
    icon: 'i-heroicons-user',
    click: () => navigateTo('/profile')
  }],
  [{
    label: '退出登录',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: () => authStore.logout()
  }]
]

// 面包屑导航
const breadcrumbs = computed(() => {
  const segments = route.path.split('/').filter(Boolean)
  const crumbs = [{ name: '首页', path: '/' }]

  let currentPath = ''
  for (const segment of segments) {
    currentPath += `/${segment}`
    
    // 根据路径生成面包屑名称
    let name = segment
    switch (segment) {
      case 'customers':
        name = '客户管理'
        break
      case 'projects':
        name = '项目档案'
        break
      case 'services':
        name = '服务工单'
        break
      case 'admin':
        name = '系统管理'
        break
      case 'users':
        name = '用户管理'
        break
      case 'roles':
        name = '角色权限'
        break
      case 'audit-logs':
        name = '审计日志'
        break
      case 'configurations':
        name = '配置管理'
        break
      case 'sla':
        name = 'SLA管理'
        break
      case 'notifications':
        name = '通知设置'
        break
      case 'profile':
        name = '个人资料'
        break
    }

    crumbs.push({ name, path: currentPath })
  }

  return crumbs
})

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const toggleSubmenu = (menuName: string) => {
  const index = openSubmenus.value.indexOf(menuName)
  if (index > -1) {
    openSubmenus.value.splice(index, 1)
  } else {
    openSubmenus.value.push(menuName)
  }
}

const isActiveRoute = (path: string) => {
  if (!route?.path) return false
  return route.path === path || route.path.startsWith(`${path}/`)
}

const hasActiveChild = (children: any[]) => {
  if (!route?.path) return false
  return children.some(child => isActiveRoute(child.path))
}

// 监听路由变化，关闭移动端侧边栏
watch(() => route?.path, () => {
  sidebarOpen.value = false
})
</script>
