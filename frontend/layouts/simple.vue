<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 简单的顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <h1 class="text-xl font-semibold text-gray-900">运维服务管理系统</h1>
          </div>
          <nav class="flex space-x-8">
            <NuxtLink to="/" class="text-gray-600 hover:text-gray-900">首页</NuxtLink>
            <NuxtLink to="/simple-dashboard" class="text-gray-600 hover:text-gray-900">简化仪表盘</NuxtLink>
            <NuxtLink to="/layout-test" class="text-gray-600 hover:text-gray-900">布局测试</NuxtLink>
          </nav>
        </div>
      </div>
    </header>

    <!-- 页面内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <slot />
    </main>
  </div>
</template>

<script setup lang="ts">
// 简单布局，无复杂逻辑
</script>
