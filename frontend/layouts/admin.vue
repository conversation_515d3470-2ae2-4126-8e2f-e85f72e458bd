<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg">
      <!-- Logo -->
      <div class="flex items-center h-16 px-6 border-b border-gray-200">
        <UIcon name="i-heroicons-cog-6-tooth" class="h-8 w-8 text-blue-600" />
        <span class="ml-2 text-lg font-bold text-gray-900">系统管理</span>
      </div>

      <!-- 导航菜单 -->
      <nav class="mt-8 px-3">
        <div class="space-y-1">
          <!-- 总览 -->
          <NuxtLink
            to="/admin"
            class="nav-item"
            :class="{ active: $route.path === '/admin' }"
          >
            <UIcon name="i-heroicons-home" class="w-5 h-5" />
            <span>总览</span>
          </NuxtLink>

          <!-- 用户管理 -->
          <div v-if="authStore.hasPermission('user:read')">
            <h3 class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
              用户管理
            </h3>
            <NuxtLink
              to="/admin/users"
              class="nav-item"
              :class="{ active: $route.path.startsWith('/admin/users') }"
            >
              <UIcon name="i-heroicons-users" class="w-5 h-5" />
              <span>用户列表</span>
            </NuxtLink>
            <NuxtLink
              v-if="authStore.hasPermission('role:read')"
              to="/admin/roles"
              class="nav-item"
              :class="{ active: $route.path.startsWith('/admin/roles') }"
            >
              <UIcon name="i-heroicons-shield-check" class="w-5 h-5" />
              <span>角色权限</span>
            </NuxtLink>
          </div>

          <!-- 系统管理 -->
          <div v-if="authStore.hasPermission('admin:access')">
            <h3 class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
              系统管理
            </h3>
            <NuxtLink
              v-if="authStore.hasPermission('audit:read')"
              to="/admin/audit-logs"
              class="nav-item"
              :class="{ active: $route.path.startsWith('/admin/audit-logs') }"
            >
              <UIcon name="i-heroicons-document-text" class="w-5 h-5" />
              <span>审计日志</span>
            </NuxtLink>
            <NuxtLink
              v-if="authStore.hasPermission('configuration:read')"
              to="/admin/configurations"
              class="nav-item"
              :class="{ active: $route.path.startsWith('/admin/configurations') }"
            >
              <UIcon name="i-heroicons-cog-6-tooth" class="w-5 h-5" />
              <span>系统配置</span>
            </NuxtLink>
          </div>

          <!-- 返回主页 -->
          <div class="pt-4 border-t border-gray-200">
            <NuxtLink
              to="/"
              class="nav-item"
            >
              <UIcon name="i-heroicons-arrow-left" class="w-5 h-5" />
              <span>返回主页</span>
            </NuxtLink>
          </div>
        </div>
      </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="pl-64">
      <!-- 顶部导航 -->
      <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="px-6 py-4 flex items-center justify-between">
          <!-- 面包屑导航 -->
          <nav class="flex" aria-label="Breadcrumb">
            <ol class="flex items-center space-x-4">
              <li>
                <NuxtLink
                  to="/admin"
                  class="text-sm font-medium text-gray-500 hover:text-gray-700"
                >
                  系统管理
                </NuxtLink>
              </li>
              <li v-if="breadcrumbs.length > 0">
                <div class="flex items-center">
                  <UIcon name="i-heroicons-chevron-right" class="w-4 h-4 text-gray-400" />
                  <span class="ml-4 text-sm font-medium text-gray-700">
                    {{ breadcrumbs[breadcrumbs.length - 1] }}
                  </span>
                </div>
              </li>
            </ol>
          </nav>

          <!-- 用户菜单 -->
          <UDropdown>
            <template #default>
              <UButton
                variant="ghost"
                color="gray"
                :label="authStore.user?.fullName || authStore.user?.username"
              >
                <template #leading>
                  <UAvatar
                    size="xs"
                    :alt="authStore.user?.fullName"
                    :src="authStore.user?.avatar"
                  />
                </template>
                <template #trailing>
                  <UIcon name="i-heroicons-chevron-down" class="w-4 h-4" />
                </template>
              </UButton>
            </template>

            <template #panel>
              <div class="p-2">
                <UButton
                  variant="ghost"
                  block
                  icon="i-heroicons-user"
                  @click="navigateTo('/profile')"
                >
                  个人设置
                </UButton>
                <UButton
                  variant="ghost"
                  block
                  icon="i-heroicons-arrow-right-on-rectangle"
                  @click="authStore.logout()"
                >
                  退出登录
                </UButton>
              </div>
            </template>
          </UDropdown>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="p-6">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
const authStore = useAuthStore()
const route = useRoute()

// 计算面包屑导航
const breadcrumbs = computed(() => {
  const pathMap: Record<string, string> = {
    '/admin/users': '用户管理',
    '/admin/roles': '角色权限',
    '/admin/audit-logs': '审计日志',
    '/admin/configurations': '系统配置'
  }

  const breadcrumb = pathMap[route.path]
  return breadcrumb ? [breadcrumb] : []
})
</script>

<style scoped>
.nav-item {
  @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-md hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200;
}

.nav-item.active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-500;
}

.nav-item svg {
  @apply mr-3 flex-shrink-0;
}
</style>