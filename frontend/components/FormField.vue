<template>
  <!-- 网格组字段 -->
  <div v-if="field.type === 'group'" :class="field.gridClass">
    <FormField
      v-for="(subField, index) in field.fields"
      :key="('name' in subField ? subField.name : `field-${index}`) || `field-${index}`"
      :field="subField"
      :form-state="formState"
      :options="getSubFieldOptions(subField)"
    />
  </div>
  
  <!-- 网格布局字段 -->
  <div v-else-if="field.type === 'grid'" :class="field.gridClass || 'grid grid-cols-1 md:grid-cols-2 gap-4'">
    <FormField
      v-for="(gridField, index) in field.fields"
      :key="('name' in gridField ? gridField.name : `grid-field-${index}`) || `grid-field-${index}`"
      :field="gridField"
      :form-state="formState"
      :options="getSubFieldOptions(gridField)"
    />
  </div>
  
  <!-- 普通表单字段 -->
  <UFormGroup
    v-else-if="'name' in field"
    :label="field.label"
    :name="field.name"
    :required="field.required"
  >
    <!-- 文本输入 -->
    <UInput
      v-if="field.type === 'text'"
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 邮箱输入 -->
    <UInput
      v-else-if="field.type === 'email'"
      v-model="formState[field.name]"
      type="email"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 密码输入 -->
    <UInput
      v-else-if="field.type === 'password'"
      v-model="formState[field.name]"
      type="password"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 数字输入 -->
    <UInput
      v-else-if="field.type === 'number'"
      v-model="formState[field.name]"
      type="number"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 电话输入 -->
    <UInput
      v-else-if="field.type === 'tel'"
      v-model="formState[field.name]"
      type="tel"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- URL输入 -->
    <UInput
      v-else-if="field.type === 'url'"
      v-model="formState[field.name]"
      type="url"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 日期输入 -->
    <UInput
      v-else-if="field.type === 'date'"
      v-model="formState[field.name]"
      type="date"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />
    
    <!-- 日期时间输入 -->
    <UInput
      v-else-if="field.type === 'datetime'"
      v-model="formState[field.name]"
      type="datetime-local"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />
    
    <!-- 时间输入 -->
    <UInput
      v-else-if="field.type === 'time'"
      v-model="formState[field.name]"
      type="time"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />

    <!-- 选择框 -->
    <USelect
      v-else-if="field.type === 'select'"
      v-model="formState[field.name]"
      :options="options"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
      value-attribute="value"
      option-attribute="label"
    />

    <!-- 文本域 -->
    <UTextarea
      v-else-if="field.type === 'textarea'"
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
      :rows="field.rows || 3"
    />

    <!-- 复选框 -->
    <UCheckbox
      v-else-if="field.type === 'checkbox'"
      v-model="formState[field.name]"
      :label="field.placeholder"
      :disabled="field.disabled"
    />
    
    <!-- 单选按钮组 -->
    <URadioGroup
      v-else-if="field.type === 'radio'"
      v-model="formState[field.name]"
      :options="options"
      :disabled="field.disabled"
    />
    
    <!-- 文件上传 -->
    <UInput
      v-else-if="field.type === 'file'"
      v-model="formState[field.name]"
      type="file"
      :accept="field.accept"
      :multiple="field.multiple"
      :disabled="field.disabled"
    />

    <!-- 默认文本输入 -->
    <UInput
      v-else
      v-model="formState[field.name]"
      :placeholder="field.placeholder"
      :disabled="field.disabled"
    />
  </UFormGroup>
</template>

<script setup lang="ts">
import type { FormField } from '~/types/ui'

interface Props {
  field: FormField
  formState: Record<string, any>
  options?: Array<{ label: string; value: any }>
  allOptions?: Record<string, Array<{ label: string; value: any }>>
}

const props = defineProps<Props>()

const getSubFieldOptions = (subField: FormField) => {
  if ('name' in subField && props.allOptions?.[subField.name]) {
    return props.allOptions[subField.name]
  }
  return []
}
</script>
