<template>
  <UCard :class="cardClass">
    <div class="flex items-center">
      <div class="flex-shrink-0">
        <div :class="iconContainerClass">
          <UIcon :name="icon" :class="iconClass" />
        </div>
      </div>
      <div class="ml-5 w-0 flex-1">
        <dl>
          <dt class="text-sm font-medium text-gray-500 truncate">{{ title }}</dt>
          <dd :class="valueClass">
            <slot name="value" :value="value" :formatted-value="formattedValue">
              {{ formattedValue }}
            </slot>
          </dd>
          <dd v-if="subtitle" class="text-xs text-gray-400 mt-1">
            {{ subtitle }}
          </dd>
        </dl>
      </div>
      <div v-if="trend !== undefined" class="flex-shrink-0">
        <div :class="trendClass">
          <UIcon :name="trendIcon" class="w-4 h-4" />
          <span class="text-sm font-medium">{{ Math.abs(trend) }}%</span>
        </div>
      </div>
    </div>
    
    <!-- 进度条 -->
    <div v-if="showProgress && progress !== undefined" class="mt-4">
      <div class="flex justify-between text-sm text-gray-600 mb-1">
        <span>{{ progressLabel }}</span>
        <span>{{ progress }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div
          :class="progressBarClass"
          class="h-2 rounded-full transition-all"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>

    <!-- 自定义内容插槽 -->
    <div v-if="$slots.content" class="mt-4">
      <slot name="content" />
    </div>
  </UCard>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value: number | string
  icon: string
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'gray'
  subtitle?: string
  trend?: number // 正数表示上升，负数表示下降
  format?: 'number' | 'currency' | 'percentage' | 'custom'
  customFormat?: (value: number | string) => string
  showProgress?: boolean
  progress?: number
  progressLabel?: string
  clickable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  color: 'blue',
  format: 'number',
  showProgress: false,
  clickable: false
})

const emit = defineEmits<{
  click: []
}>()

// 计算属性
const cardClass = computed(() => [
  'transition-all duration-200',
  props.clickable ? 'cursor-pointer hover:shadow-md' : ''
])

const iconContainerClass = computed(() => {
  const colorClasses = {
    blue: 'w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center',
    green: 'w-8 h-8 bg-green-500 rounded-md flex items-center justify-center',
    yellow: 'w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center',
    red: 'w-8 h-8 bg-red-500 rounded-md flex items-center justify-center',
    purple: 'w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center',
    gray: 'w-8 h-8 bg-gray-500 rounded-md flex items-center justify-center'
  }
  return colorClasses[props.color]
})

const iconClass = computed(() => 'w-5 h-5 text-white')

const valueClass = computed(() => {
  const baseClass = 'text-lg font-medium'
  const colorClasses = {
    blue: 'text-blue-900',
    green: 'text-green-900',
    yellow: 'text-yellow-900',
    red: 'text-red-900',
    purple: 'text-purple-900',
    gray: 'text-gray-900'
  }
  return `${baseClass} ${colorClasses[props.color]}`
})

const trendClass = computed(() => {
  if (props.trend === undefined) return ''
  
  const baseClass = 'flex items-center space-x-1'
  return props.trend >= 0 
    ? `${baseClass} text-green-600`
    : `${baseClass} text-red-600`
})

const trendIcon = computed(() => {
  if (props.trend === undefined) return ''
  return props.trend >= 0 
    ? 'i-heroicons-arrow-trending-up'
    : 'i-heroicons-arrow-trending-down'
})

const progressBarClass = computed(() => {
  const colorClasses = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    yellow: 'bg-yellow-600',
    red: 'bg-red-600',
    purple: 'bg-purple-600',
    gray: 'bg-gray-600'
  }
  return colorClasses[props.color]
})

const formattedValue = computed(() => {
  if (props.customFormat) {
    return props.customFormat(props.value)
  }

  const numValue = typeof props.value === 'string' ? parseFloat(props.value) : props.value

  switch (props.format) {
    case 'currency':
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      }).format(numValue)
    
    case 'percentage':
      return `${numValue}%`
    
    case 'number':
      return new Intl.NumberFormat('zh-CN').format(numValue)
    
    default:
      return props.value.toString()
  }
})

// 事件处理
const handleClick = () => {
  if (props.clickable) {
    emit('click')
  }
}
</script>
