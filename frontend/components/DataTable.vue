<template>
  <div class="space-y-4">
    <!-- 搜索和筛选区域 -->
    <UCard v-if="showFilters">
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- 搜索框 -->
        <div class="flex-1" v-if="searchable">
          <UInput
            v-model="searchQuery"
            :placeholder="searchPlaceholder"
            icon="i-heroicons-magnifying-glass"
            @input="handleSearch"
          />
        </div>
        
        <!-- 筛选器 -->
        <div class="flex gap-2" v-if="filters.length > 0">
          <USelect
            v-for="filter in filters"
            :key="filter.key"
            v-model="filterValues[filter.key]"
            :options="filter.options"
            :placeholder="filter.placeholder"
            @change="handleFilterChange"
          />
        </div>

        <!-- 自定义操作按钮 -->
        <div class="flex gap-2" v-if="$slots.actions">
          <slot name="actions" />
        </div>
      </div>
    </UCard>

    <!-- 数据表格 -->
    <UCard>
      <div class="overflow-hidden">
        <UTable
          :rows="data"
          :columns="columns"
          :loading="loading"
          :empty-state="emptyState"
        >
          <!-- 动态插槽 -->
          <template
            v-for="column in columns"
            :key="`${column.key}-data`"
            #[`${column.key}-data`]="{ row }"
          >
            <slot
              :name="`${column.key}-data`"
              :row="row"
              :value="row[column.key]"
            >
              <!-- 默认渲染 -->
              <span v-if="column.type === 'text'">{{ row[column.key] }}</span>
              <UBadge
                v-else-if="column.type === 'badge'"
                :color="getBadgeColor(column.key, row[column.key])"
                variant="soft"
              >
                {{ getBadgeText(column.key, row[column.key]) }}
              </UBadge>
              <span v-else-if="column.type === 'date'">
                {{ formatDate(row[column.key]) }}
              </span>
              <span v-else-if="column.type === 'currency'">
                {{ formatCurrency(row[column.key]) }}
              </span>
              <div v-else-if="column.type === 'progress'" class="w-full">
                <div class="flex justify-between text-sm text-gray-600 mb-1">
                  <span>{{ row[column.key] || 0 }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                  <div
                    class="bg-primary-600 h-2 rounded-full transition-all"
                    :style="{ width: `${row[column.key] || 0}%` }"
                  ></div>
                </div>
              </div>
              <UDropdown
                v-else-if="column.type === 'actions'"
                :items="getActionItems(row)"
              >
                <UButton
                  color="gray"
                  variant="ghost"
                  icon="i-heroicons-ellipsis-horizontal"
                />
              </UDropdown>
              <span v-else>{{ row[column.key] }}</span>
            </slot>
          </template>
        </UTable>
      </div>

      <!-- 分页 -->
      <div v-if="pagination && data.length > 0" class="flex justify-between items-center mt-4">
        <div class="text-sm text-gray-500">
          共 {{ pagination.total }} 条记录，第 {{ pagination.page }} / {{ pagination.totalPages }} 页
        </div>
        <UPagination
          :model-value="pagination.page"
          :page-count="pagination.limit"
          :total="pagination.total"
          @update:model-value="handlePageChange"
        />
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { BadgeColor } from '~/types/ui'
interface Column {
  key: string
  label: string
  type?: 'text' | 'badge' | 'date' | 'currency' | 'progress' | 'actions'
  sortable?: boolean
}

interface Filter {
  key: string
  placeholder: string
  options: Array<{ label: string; value: string }>
}

interface Pagination {
  page: number
  limit: number
  total: number
  totalPages: number
}

interface EmptyState {
  icon: string
  label: string
  description?: string
}

interface Props {
  data: any[]
  columns: Column[]
  loading?: boolean
  searchable?: boolean
  searchPlaceholder?: string
  filters?: Filter[]
  pagination?: Pagination
  emptyState?: EmptyState
  showFilters?: boolean
  badgeConfig?: Record<string, Record<string, { color: BadgeColor; text: string }>>
  actionItems?: (row: any) => any[]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  searchable: true,
  searchPlaceholder: '搜索...',
  filters: () => [],
  showFilters: true,
  badgeConfig: () => ({}),
  emptyState: () => ({
    icon: 'i-heroicons-inbox',
    label: '暂无数据',
    description: '没有找到相关数据'
  })
})

const emit = defineEmits<{
  search: [query: string]
  filter: [filters: Record<string, string>]
  pageChange: [page: number]
  action: [action: string, row: any]
}>()

// 响应式数据
const searchQuery = ref('')
const filterValues = ref<Record<string, string>>({})

// 初始化筛选器值
onMounted(() => {
  props.filters.forEach(filter => {
    filterValues.value[filter.key] = ''
  })
})

// 防抖搜索
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func.apply(null, args), wait)
  }
}

const debouncedSearch = debounce((query: string) => {
  emit('search', query)
}, 300)

const handleSearch = () => {
  debouncedSearch(searchQuery.value)
}

const handleFilterChange = () => {
  emit('filter', { ...filterValues.value })
}

const handlePageChange = (page: number) => {
  emit('pageChange', page)
}

// 工具函数
const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatCurrency = (amount: number) => {
  if (!amount) return '未设置'
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(amount)
}

const getBadgeColor = (columnKey: string, value: string) => {
  return props.badgeConfig[columnKey]?.[value]?.color || 'gray'
}

const getBadgeText = (columnKey: string, value: string) => {
  return props.badgeConfig[columnKey]?.[value]?.text || value
}

const getActionItems = (row: any) => {
  if (props.actionItems) {
    return props.actionItems(row).map(item => ({
      ...item,
      click: () => emit('action', item.action, row)
    }))
  }
  return []
}
</script>
