<template>
  <UModal v-model="isOpen" :ui="modalConfig">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold">
          {{ title }}
        </h3>
      </template>

      <UForm
        :schema="schema"
        :state="formState"
        @submit="handleSubmit"
        class="space-y-4"
      >
        <!-- 动态表单字段 -->
        <template v-for="(field, index) in fields" :key="('name' in field ? field.name : `form-field-${index}`) || `form-field-${index}`">
          <!-- 网格布局容器 -->
          <div
            v-if="field.type === 'grid'"
            :class="field.gridClass || 'grid grid-cols-1 md:grid-cols-2 gap-4'"
          >
            <template v-for="(gridField, gIndex) in field.fields" :key="('name' in gridField ? gridField.name : `grid-field-${gIndex}`) || `grid-field-${gIndex}`">
              <FormField
                :field="gridField"
                :form-state="formState"
                :options="fieldOptions[('name' in gridField ? gridField.name : '') || '']"
                :all-options="fieldOptions"
              />
            </template>
          </div>
          
          <!-- 单个字段 -->
          <FormField
            v-else
            :field="field"
            :form-state="formState"
            :options="fieldOptions[('name' in field ? field.name : '') || '']"
            :all-options="fieldOptions"
          />
        </template>

        <!-- 自定义内容插槽 -->
        <slot name="form-content" :form-state="formState" />

        <!-- 操作按钮 -->
        <div class="flex justify-end space-x-3">
          <UButton
            color="gray"
            variant="soft"
            @click="handleCancel"
            :disabled="loading"
          >
            {{ cancelText }}
          </UButton>
          <UButton
            type="submit"
            :loading="loading"
            :disabled="loading"
          >
            {{ submitText }}
          </UButton>
        </div>
      </UForm>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import type { ZodSchema } from 'zod'

import type { FormField } from '~/types/ui'

interface Props {
  modelValue: boolean
  title: string
  fields: FormField[]
  schema: ZodSchema
  formState: Record<string, any>
  loading?: boolean
  submitText?: string
  cancelText?: string
  modalConfig?: Record<string, any>
  fieldOptions?: Record<string, Array<{ label: string; value: any }>>
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  submitText: '确定',
  cancelText: '取消',
  modalConfig: () => ({ width: 'sm:max-w-md' }),
  fieldOptions: () => ({})
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  submit: [data: any]
  cancel: []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const handleSubmit = (data: any) => {
  emit('submit', data)
}

const handleCancel = () => {
  emit('cancel')
  isOpen.value = false
}
</script>
