<template>
  <UModal v-model="isOpen">
    <UCard>
      <template #header>
        <h3 class="text-lg font-semibold" :class="titleClass">
          {{ title }}
        </h3>
      </template>

      <div class="space-y-4">
        <p class="text-gray-600">
          {{ message }}
        </p>
        <p v-if="warningMessage" :class="warningClass">
          {{ warningMessage }}
        </p>
        
        <!-- 自定义内容插槽 -->
        <slot name="content" />
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <UButton
            color="gray"
            variant="soft"
            @click="handleCancel"
            :disabled="loading"
          >
            {{ cancelText }}
          </UButton>
          <UButton
            :color="confirmColor"
            :loading="loading"
            :disabled="loading"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </UButton>
        </div>
      </template>
    </UCard>
  </UModal>
</template>

<script setup lang="ts">
import type { ButtonColor } from '~/types/ui'

interface Props {
  modelValue: boolean
  title: string
  message: string
  warningMessage?: string
  confirmText?: string
  cancelText?: string
  confirmColor?: ButtonColor
  type?: 'danger' | 'warning' | 'info' | 'success'
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: '确认',
  cancelText: '取消',
  confirmColor: 'red',
  type: 'danger',
  loading: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  confirm: []
  cancel: []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const titleClass = computed(() => {
  const classes = {
    danger: 'text-red-600',
    warning: 'text-yellow-600',
    info: 'text-blue-600',
    success: 'text-green-600'
  }
  return classes[props.type] || classes.danger
})

const warningClass = computed(() => {
  const classes = {
    danger: 'text-sm text-red-600',
    warning: 'text-sm text-yellow-600',
    info: 'text-sm text-blue-600',
    success: 'text-sm text-green-600'
  }
  return classes[props.type] || classes.danger
})

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  isOpen.value = false
}
</script>
