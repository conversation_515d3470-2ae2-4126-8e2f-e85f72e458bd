import { createConfigForNuxt } from '@nuxt/eslint-config/flat'

export default createConfigForNuxt({
  features: {
    // 启用 TypeScript 支持
    typescript: true,
    // 启用 Vue 支持
    vue: true,
    // 启用 Nuxt 特定规则
    nuxt: true
  }
}).append({
  rules: {
    // 关闭一些在 Nuxt 3 中不必要的规则
    '@typescript-eslint/no-unused-vars': ['error', { 
      argsIgnorePattern: '^_',
      varsIgnorePattern: '^_',
      ignoreRestSiblings: true
    }],
    
    // 允许 any 类型（在某些情况下是必要的）
    '@typescript-eslint/no-explicit-any': 'warn',
    
    // 允许空函数
    '@typescript-eslint/no-empty-function': 'off',
    
    // 允许非空断言
    '@typescript-eslint/no-non-null-assertion': 'warn',
    
    // Vue 相关规则
    'vue/multi-word-component-names': 'off',
    'vue/no-multiple-template-root': 'off',
    'vue/no-v-html': 'warn',
    
    // 允许 console.log（开发阶段）
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    
    // 导入相关
    'import/no-unresolved': 'off', // Nuxt 自动导入会导致误报
    'import/named': 'off',
    
    // 其他规则
    'prefer-const': 'error',
    'no-var': 'error',
    'object-shorthand': 'error',
    'prefer-template': 'error'
  },
  
  // 全局变量定义
  languageOptions: {
    globals: {
      // Nuxt 3 自动导入的全局函数
      definePageMeta: 'readonly',
      useHead: 'readonly',
      useRoute: 'readonly',
      useRouter: 'readonly',
      useNuxtApp: 'readonly',
      navigateTo: 'readonly',
      createError: 'readonly',
      useToast: 'readonly',
      useCookie: 'readonly',
      useState: 'readonly',
      useAsyncData: 'readonly',
      useFetch: 'readonly',
      useRuntimeConfig: 'readonly',
      
      // Vue 3 自动导入的组合函数
      ref: 'readonly',
      reactive: 'readonly',
      computed: 'readonly',
      watch: 'readonly',
      watchEffect: 'readonly',
      onMounted: 'readonly',
      onUnmounted: 'readonly',
      onUpdated: 'readonly',
      onBeforeMount: 'readonly',
      onBeforeUnmount: 'readonly',
      onBeforeUpdate: 'readonly',
      nextTick: 'readonly',
      toRef: 'readonly',
      toRefs: 'readonly',
      unref: 'readonly',
      isRef: 'readonly',
      
      // 项目自定义的组合函数
      useAuthStore: 'readonly',
      useCrud: 'readonly',
      useForm: 'readonly',
      useSearch: 'readonly',
      
      // Node.js 全局变量
      process: 'readonly',
      Buffer: 'readonly',
      global: 'readonly',
      
      // 浏览器全局变量
      window: 'readonly',
      document: 'readonly',
      navigator: 'readonly',
      console: 'readonly'
    }
  }
})
