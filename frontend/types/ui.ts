// UI 组件相关的类型定义

// Nuxt UI 兼容的颜色类型
export type BadgeColor = 'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'indigo' | 'purple' | 'pink'
export type ButtonColor = 'primary' | 'gray' | 'red' | 'yellow' | 'green' | 'blue' | 'indigo' | 'purple' | 'pink'
export type ButtonVariant = 'solid' | 'outline' | 'soft' | 'ghost' | 'link'
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl'
export type Size = 'xs' | 'sm' | 'md' | 'lg' | 'xl'

// 表格列定义 - 严格类型
export interface Column {
  key: string
  label: string
  type?: 'text' | 'date' | 'actions' | 'progress' | 'badge' | 'currency'
  sortable?: boolean
  width?: string
  align?: 'left' | 'center' | 'right'
}

// 表单字段定义 - 统一结构
export interface BaseFormField {
  name: string
  label: string
  required?: boolean
  placeholder?: string
  disabled?: boolean
  readonly?: boolean
  helpText?: string
}

export interface InputField extends BaseFormField {
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  minLength?: number
  maxLength?: number
  pattern?: string
}

export interface SelectField extends BaseFormField {
  type: 'select'
  options: Array<{ label: string; value: string | number }>
  multiple?: boolean
  searchable?: boolean
  clearable?: boolean
}

export interface TextareaField extends BaseFormField {
  type: 'textarea'
  rows?: number
  maxLength?: number
  resize?: boolean
}

export interface DateField extends BaseFormField {
  type: 'date' | 'datetime' | 'time'
  format?: string
  minDate?: string
  maxDate?: string
}

export interface CheckboxField extends BaseFormField {
  type: 'checkbox'
  checked?: boolean
}

export interface RadioField extends BaseFormField {
  type: 'radio'
  options: Array<{ label: string; value: string | number }>
}

export interface FileField extends BaseFormField {
  type: 'file'
  accept?: string
  multiple?: boolean
  maxSize?: number
  maxFiles?: number
}

export interface FormGroupField {
  type: 'group'
  gridClass: string
  fields: FormField[]
}

export type FormField = 
  | InputField 
  | SelectField 
  | TextareaField 
  | DateField 
  | CheckboxField 
  | RadioField 
  | FileField
  | FormGroupField
  | { type: 'grid'; gridClass?: string; fields: FormField[] }


// 操作按钮配置
export interface ActionItem {
  key: string
  label: string
  icon?: string
  color?: ButtonColor
  variant?: ButtonVariant
  size?: ButtonSize
  loading?: boolean
  disabled?: boolean
  show?: boolean
  permission?: string
}

// 兼容旧版本的Action类型
export type Action = ActionItem

// 下拉菜单项
export interface DropdownItem {
  label: string
  icon?: string
  click?: () => void | Promise<void>
  disabled?: boolean
  divider?: boolean
}

// 状态映射配置
export interface StatusMapping {
  [key: string]: {
    label: string
    color: BadgeColor
    icon?: string
  }
}

// 空状态配置
export interface EmptyStateConfig {
  icon: string
  title: string
  description?: string
  actions?: Array<{
    label: string
    icon?: string
    color?: ButtonColor
    click: () => void
  }>
}

// 确认对话框配置
export interface ConfirmConfig {
  title: string
  message: string
  type?: 'warning' | 'error' | 'info' | 'success'
  confirmText?: string
  cancelText?: string
  confirmColor?: ButtonColor
  dangerous?: boolean
}

// 分页配置
export interface PaginationConfig {
  page: number
  limit: number
  total: number
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  pageSizes?: number[]
}