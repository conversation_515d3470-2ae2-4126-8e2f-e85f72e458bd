// API 响应类型定义
export interface BaseApiResponse {
  success: boolean
  message?: string
}

export interface ApiSuccessResponse<T> extends BaseApiResponse {
  success: true
  data: T
}

export interface ApiErrorResponse extends BaseApiResponse {
  success: false
  message: string
  errors?: Record<string, string[]>
}

export type ApiResponse<T> = ApiSuccessResponse<T> | ApiErrorResponse

// 类型守卫函数
export function isSuccessResponse<T>(
  response: ApiResponse<T>
): response is ApiSuccessResponse<T> {
  return response.success === true
}

export function isErrorResponse<T>(
  response: ApiResponse<T>
): response is ApiErrorResponse {
  return response.success === false
}

// 分页响应类型
export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
}

export interface PaginatedResponse<T> {
  items: T[]
  pagination: PaginationMeta
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  fullName: string
  avatar?: string
  role: {
    id: string
    name: string
    description: string
    permissions: string[]
  }
  department?: string
  phone?: string
  isActive: boolean
  lastLoginAt?: string
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
  remember?: boolean
}

export interface LoginResponse {
  user: User
  token: string
}

export interface LoginResult {
  success: boolean
  message: string
}

// 服务工单相关类型
export interface Service {
  id: string
  ticketNumber: string
  title: string
  description: string
  customerId: string
  customer: {
    id: string
    name: string
  }
  projectId: string
  project: {
    id: string
    name: string
  }
  type: string
  priority: string
  status: string
  assignedTo?: string
  assignee?: {
    id: string
    fullName: string
  }
  urgent: boolean
  category: string
  responseDeadline?: string
  resolutionDeadline?: string
  actualResponseTime?: string
  actualResolutionTime?: string
  slaStatus: string
  expectedResolution?: string
  attachments: Array<{
    id: string
    filename: string
    originalName: string
    size: number
    mimeType: string
    url: string
  }>
  createdAt: string
  updatedAt: string
}

// 客户相关类型
export interface Customer {
  id: string
  name: string
  contactPerson: string
  email: string
  phone: string
  address?: string
  industry?: string
  description?: string
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

// 项目相关类型
export interface Project {
  id: string
  name: string
  description?: string
  customerId: string
  customer: {
    id: string
    name: string
  }
  type: string
  priority: string
  status: string
  budget?: number
  startDate?: string
  endDate?: string
  progress: number
  createdAt: string
  updatedAt: string
}

// 操作结果类型
export interface OperationResult {
  success: boolean
  message: string
  data?: any
}

// 角色相关类型
export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  userCount?: number
  isSystem?: boolean
  createdAt: string
  updatedAt: string
}

export interface Permission {
  key: string
  description: string
  category: string
}

export interface PermissionGroup {
  category: string
  permissions: Permission[]
}

export interface RoleTemplate {
  name: string
  description: string
  permissions: string[]
}

export interface CreateRoleRequest {
  name: string
  description: string
  permissions: string[]
}

export interface UpdateRoleRequest {
  name?: string
  description?: string
  permissions?: string[]
}

export interface DuplicateRoleRequest {
  newName: string
  newDescription?: string
}

export interface CreateRoleFromTemplateRequest {
  templateName: string
  customName?: string
  customDescription?: string
}

// 用户管理相关类型
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  fullName: string
  phone?: string
  roleId: string
  department?: string
  isActive?: boolean
}

export interface UpdateUserRequest {
  username?: string
  email?: string
  fullName?: string
  phone?: string
  roleId?: string
  department?: string
  isActive?: boolean
}

export interface ResetPasswordRequest {
  newPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface UserSearchParams {
  search?: string
  roleId?: string
  isActive?: boolean
  department?: string
  page?: number
  limit?: number
}

export interface RoleSearchParams {
  search?: string
  page?: number
  limit?: number
}

// 系统统计类型
export interface AdminStats {
  totalUsers: number
  activeUsers: number
  totalRoles: number
  todayLogins: number
  userGrowth: number
  activeGrowth: number
  loginGrowth: number
}

// 审计日志类型
export interface AuditLog {
  id: string
  userId: string
  userName: string
  action: string
  resource: string
  resourceId?: string
  description: string
  metadata?: any
  ipAddress?: string
  userAgent?: string
  createdAt: string
}

// 用户会话类型
export interface UserSession {
  sessionId: string
  userId: string
  userName: string
  userAgent: string
  ipAddress: string
  location?: string
  isActive: boolean
  lastActivity: string
  createdAt: string
}

// 文件上传类型
export interface UploadResponse {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  url: string
}