{"extends": "./.nuxt/tsconfig.json", "compilerOptions": {"strict": true, "noImplicitAny": false, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "jsxImportSource": "vue", "target": "ESNext", "module": "ESNext", "lib": ["ESNext", "DOM", "DOM.Iterable"], "types": ["node", "@types/node"], "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"], "~~/*": ["./*"], "@@/*": ["./*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.vue", ".nuxt/nuxt.d.ts"], "exclude": ["node_modules", "dist", ".output", ".nuxt/dist", "**/node_modules/**/*.vue", "node_modules/@nuxt/image/dist/runtime/components/NuxtImg.vue", "node_modules/@nuxt/image/dist/runtime/components/NuxtPicture.vue", "**/node_modules/@nuxt/image/**/*.vue"]}