import { defineStore } from 'pinia'
import { jwtDecode } from 'jwt-decode'
import type { 
  ApiResponse, 
  User, 
  LoginCredentials, 
  LoginResponse, 
  LoginResult
} from '~/types/api'

export interface JWTPayload {
  userId: string
  username: string
  email: string
  role: string
  permissions: string[]
  iat: number
  exp: number
}

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)

  // 计算属性
  const hasPermission = computed(() => (permission: string) => {
    if (!user.value) return false
    return user.value.role.permissions.includes(permission) || user.value.role.permissions.includes('admin:all')
  })

  const hasRole = computed(() => (roleName: string) => {
    if (!user.value) return false
    return user.value.role.name === roleName
  })

  const isAdmin = computed(() => user.value?.role.name === 'admin')

  // 初始化认证状态
  const initAuth = () => {
    const savedToken = useCookie('auth-token', {
      default: () => null,
      secure: true,
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7 // 7天
    })

    if (savedToken.value) {
      try {
        const decoded = jwtDecode<JWTPayload>(savedToken.value)
        
        // 检查token是否过期
        if (decoded.exp * 1000 > Date.now()) {
          token.value = savedToken.value
          isAuthenticated.value = true
          
          // 从token中恢复基本用户信息
          user.value = {
            id: decoded.userId,
            username: decoded.username,
            email: decoded.email || '',
            fullName: decoded.username,
            role: {
              id: '',
              name: decoded.role || 'user',
              description: '',
              permissions: decoded.permissions || []
            },
            department: undefined,
            phone: undefined,
            isActive: true,
            lastLoginAt: undefined,
            createdAt: '',
            updatedAt: ''
          }
          
          // 获取完整用户信息
          fetchUserProfile()
        } else {
          // token过期，清除认证状态
          clearAuth()
        }
      } catch (error) {
        console.error('Token decode error:', error)
        clearAuth()
      }
    }
  }

  // 登录
  const login = async (credentials: LoginCredentials): Promise<LoginResult> => {
    loading.value = true
    
    try {
      const response = await $fetch<ApiResponse<LoginResponse>>('/auth/login', {
        method: 'POST',
        body: credentials,
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (response.success) {
        // 保存认证信息
        token.value = response.data.token
        user.value = response.data.user
        isAuthenticated.value = true

        // 保存token到cookie
        const tokenCookie = useCookie('auth-token', {
          secure: true,
          sameSite: 'strict',
          maxAge: credentials.remember ? 60 * 60 * 24 * 30 : 60 * 60 * 24 * 7 // 记住我30天，否则7天
        })
        tokenCookie.value = response.data.token

        return { success: true, message: '登录成功' }
      } else {
        return { success: false, message: response.message || '登录失败' }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return { 
        success: false, 
        message: error.data?.message || '登录失败，请检查网络连接' 
      }
    } finally {
      loading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await $fetch('/auth/logout', {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token.value}`
          },
          baseURL: useRuntimeConfig().public.apiBase
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      clearAuth()
      await navigateTo('/login')
    }
  }

  // 清除认证状态
  const clearAuth = () => {
    user.value = null
    token.value = null
    isAuthenticated.value = false
    
    // 清除cookie
    const tokenCookie = useCookie('auth-token')
    tokenCookie.value = null
  }

  // 获取用户资料
  const fetchUserProfile = async () => {
    if (!token.value) return

    try {
      const response = await $fetch<ApiResponse<User>>('/auth/me', {
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (response.success) {
        user.value = response.data
      }
    } catch (error) {
      console.error('Fetch user profile error:', error)
      // 如果获取用户信息失败，可能是token无效
      clearAuth()
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: Partial<User>) => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    
    try {
      const response = await $fetch<ApiResponse<User>>('/auth/profile', {
        method: 'PUT',
        body: profileData,
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (response.success) {
        user.value = response.data
        return { success: true, message: response.message || '更新成功' }
      } else {
        return { success: false, message: response.message || '更新失败' }
      }
    } catch (error: any) {
      console.error('Update profile error:', error)
      return { 
        success: false, 
        message: error.data?.message || '更新失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (passwordData: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    if (!token.value) return { success: false, message: '未登录' }

    loading.value = true
    
    try {
      const response = await $fetch<ApiResponse<Record<string, never>>>('/auth/change-password', {
        method: 'POST',
        body: passwordData,
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      return { success: response.success, message: response.message || '密码修改成功' }
    } catch (error: any) {
      console.error('Change password error:', error)
      return { 
        success: false, 
        message: error.data?.message || '密码修改失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 刷新token
  const refreshToken = async () => {
    if (!token.value) return false

    try {
      const response = await $fetch<ApiResponse<{ token: string }>>('/auth/refresh', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token.value}`
        },
        baseURL: useRuntimeConfig().public.apiBase
      })

      if (response.success) {
        token.value = response.data.token
        
        // 更新cookie
        const tokenCookie = useCookie('auth-token')
        tokenCookie.value = response.data.token
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('Refresh token error:', error)
      clearAuth()
      return false
    }
  }

  return {
    // 状态
    user: readonly(user),
    token: readonly(token),
    isAuthenticated: readonly(isAuthenticated),
    loading: readonly(loading),
    
    // 计算属性
    hasPermission,
    hasRole,
    isAdmin,
    
    // 方法
    initAuth,
    login,
    logout,
    clearAuth,
    fetchUserProfile,
    updateProfile,
    changePassword,
    refreshToken
  }
})
