# 用户权限管理模块

本模块为运维管理系统提供了完整的用户权限管理功能，包括用户管理、角色权限管理和审计日志等核心功能。

## 📋 功能特性

### 🏠 管理员仪表盘 (`/admin`)
- **统计概览**: 用户总数、活跃用户、角色数量、今日登录等统计信息
- **快捷操作**: 新增用户、新增角色、查看报表等快捷入口
- **最近活动**: 显示最近注册用户和系统日志
- **权限控制**: 基于用户权限显示相应的功能模块

### 👥 用户管理 (`/admin/users`)
- **用户列表**: 分页显示所有用户，支持搜索和筛选
- **用户操作**: 
  - 新增用户（用户名、邮箱、姓名、角色等）
  - 编辑用户信息
  - 重置密码
  - 启用/禁用用户
  - 删除用户
- **高级筛选**: 按角色、状态、部门等条件筛选
- **用户详情**: 查看用户详细信息、权限列表、活动统计等

### 🛡️ 角色权限管理 (`/admin/roles`)
- **角色管理**: 
  - 卡片式角色展示
  - 新建角色（权限分组选择）
  - 编辑角色权限
  - 复制角色
  - 删除角色（无用户关联时）
- **权限模板**: 预定义角色模板快速创建
- **权限分组**: 按功能模块分组管理权限
- **系统角色保护**: 系统内置角色不可编辑删除

### 📝 审计日志 (`/admin/audit-logs`)
- **操作记录**: 记录所有用户操作和系统事件
- **详细信息**: 用户、操作类型、资源、时间、IP地址等
- **筛选功能**: 按操作类型、用户、时间范围筛选
- **日志详情**: 查看操作的详细元数据信息

## 🎨 界面设计

### 专用管理布局
- **独立布局**: 专门的管理员界面布局 (`layouts/admin.vue`)
- **侧边导航**: 功能模块清晰分类
- **面包屑导航**: 当前位置导航指示
- **用户菜单**: 个人设置和退出功能

### 响应式设计
- **移动适配**: 支持移动设备访问
- **灵活布局**: 网格布局自适应不同屏幕
- **交互优化**: 模态框、下拉菜单等用户友好交互

## 🔐 权限控制

### 权限系统
- **细粒度权限**: 操作级别的权限控制
- **动态菜单**: 根据用户权限动态显示菜单
- **路由保护**: 中间件验证页面访问权限
- **功能控制**: 按钮和操作的权限控制

### 权限列表
```typescript
// 用户管理权限
'user:read'    // 查看用户
'user:create'  // 创建用户
'user:update'  // 编辑用户
'user:delete'  // 删除用户

// 角色管理权限
'role:read'    // 查看角色
'role:create'  // 创建角色
'role:update'  // 编辑角色
'role:delete'  // 删除角色

// 审计日志权限
'audit:read'   // 查看日志

// 系统管理权限
'admin:access' // 访问管理后台
```

## 🗂️ 文件结构

```
frontend/
├── pages/admin/                    # 管理员页面
│   ├── index.vue                  # 管理仪表盘
│   ├── users/
│   │   ├── index.vue             # 用户列表
│   │   └── [id].vue              # 用户详情
│   ├── roles/
│   │   └── index.vue             # 角色管理
│   └── audit-logs/
│       └── index.vue             # 审计日志
├── layouts/
│   └── admin.vue                 # 管理员布局
├── middleware/
│   └── permission.ts             # 权限中间件
└── types/
    └── api.ts                    # API类型定义
```

## 🚀 API 接口

### 用户管理接口
```typescript
GET    /api/v1/users              // 获取用户列表
GET    /api/v1/users/:id          // 获取用户详情
POST   /api/v1/users              // 创建用户
PUT    /api/v1/users/:id          // 更新用户
DELETE /api/v1/users/:id          // 删除用户
POST   /api/v1/users/:id/reset-password  // 重置密码
POST   /api/v1/users/:id/toggle-status   // 切换状态
```

### 角色管理接口
```typescript
GET    /api/v1/roles              // 获取角色列表
GET    /api/v1/roles/all          // 获取所有角色（下拉选择）
GET    /api/v1/roles/:id          // 获取角色详情
POST   /api/v1/roles              // 创建角色
PUT    /api/v1/roles/:id          // 更新角色
DELETE /api/v1/roles/:id          // 删除角色
GET    /api/v1/roles/permissions  // 获取系统权限
GET    /api/v1/roles/templates    // 获取角色模板
POST   /api/v1/roles/from-template // 从模板创建角色
```

### 审计日志接口
```typescript
GET    /api/v1/audit-logs         // 获取审计日志
```

## 🎯 使用指南

### 访问管理后台
1. 使用具有 `admin:access` 权限的账户登录
2. 在主导航菜单中点击"系统管理"
3. 根据权限访问相应的管理功能

### 用户管理流程
1. **创建用户**: 点击"新增用户" → 填写用户信息 → 分配角色 → 提交
2. **编辑用户**: 在用户列表中点击"编辑" → 修改信息 → 保存
3. **重置密码**: 点击"重置密码" → 输入新密码 → 确认
4. **管理状态**: 使用"启用/禁用"功能管理用户状态

### 角色权限配置
1. **创建角色**: 点击"新建角色" → 输入角色信息 → 选择权限 → 创建
2. **权限配置**: 按功能模块选择权限，支持全选/取消全选
3. **使用模板**: 选择预定义模板快速创建角色
4. **复制角色**: 基于现有角色快速创建相似角色

## 🔍 开发说明

### 组件依赖
- **UI框架**: Nuxt UI (基于 Tailwind CSS)
- **图标**: Heroicons
- **状态管理**: Pinia (useAuthStore)
- **API客户端**: $apiClient (Nuxt插件)

### 类型安全
- 完整的 TypeScript 类型定义
- API 响应类型检查
- 表单数据类型验证

### 性能优化
- 防抖搜索避免频繁API调用
- 分页加载处理大量数据
- 权限缓存减少重复检查

这个用户权限管理模块提供了完整的企业级用户管理功能，具有良好的扩展性和维护性。