# TypeScript 错误修复指南

## 主要错误类型

### 1. API 响应类型错误
**问题**: `Property 'data' does not exist on type 'ApiResponse<unknown>'`
**原因**: API 响应可能是成功或失败，需要类型守卫
**解决方案**: 使用类型守卫函数检查响应状态

```typescript
// 修复前
const response = await $apiClient('/users')
const users = response.data.users

// 修复后
const response = await $apiClient('/users')
if (isSuccessResponse(response)) {
  const users = response.data.users
} else {
  // 处理错误
  console.error(response.message)
}
```

### 2. 数组类型推断错误
**问题**: `Argument of type '{...}' is not assignable to parameter of type 'never'`
**原因**: TypeScript 无法推断空数组的类型
**解决方案**: 明确指定数组类型

```typescript
// 修复前
const items = []

// 修复后
const items: Array<{
  action: string
  label: string
  icon: string
  color?: string
}> = []
```

### 3. 组件导入错误
**问题**: `找不到模块"#components"或其相应的类型声明`
**原因**: 组件未正确注册或类型定义缺失
**解决方案**: 使用全局组件或明确导入

```typescript
// 修复前
import { DataTable } from '#components'

// 修复后
// 使用全局组件，不需要导入
```

### 4. 表单字段类型错误
**问题**: `Type is not assignable to type 'FormField'`
**原因**: 表单字段类型定义不完整
**解决方案**: 更新 FormField 类型定义

```typescript
// 在 types/ui.ts 中添加
export interface FormGroupField {
  type: 'group'
  gridClass: string
  fields: FormField[]
}

export type FormField = 
  | InputField 
  | SelectField 
  | TextareaField 
  | DateField 
  | CheckboxField 
  | RadioField 
  | FileField
  | FormGroupField  // 添加这一行
```

### 5. 表格列类型错误
**问题**: `Type 'string' is not assignable to type 'Column'`
**原因**: 列定义缺少必要的类型信息
**解决方案**: 明确指定列类型

```typescript
// 修复前
const columns = [
  { key: 'name', label: '名称', type: 'text' }
]

// 修复后
const columns: Column[] = [
  { key: 'name', label: '名称', type: 'text' }
]
```

## 修复优先级

### 高优先级 (影响编译)
1. API 响应类型错误
2. 数组类型推断错误
3. 函数声明顺序错误

### 中优先级 (影响类型安全)
1. 组件类型错误
2. 表单字段类型错误
3. 表格列类型错误

### 低优先级 (警告级别)
1. 未使用的变量
2. console 语句
3. any 类型使用

## 快速修复步骤

1. **修复 API 响应类型**
   - 在所有 API 调用处添加类型守卫
   - 使用 `isSuccessResponse()` 和 `isErrorResponse()` 函数

2. **修复数组类型**
   - 为所有空数组添加明确的类型注解
   - 特别是操作项数组和选项数组

3. **修复函数声明顺序**
   - 将函数定义移到使用之前
   - 或使用函数声明而不是函数表达式

4. **更新类型定义**
   - 完善 `FormField` 类型
   - 添加缺失的接口定义

## 示例修复

### 用户管理页面
```typescript
// 修复 getActionItems 函数
const getActionItems = (row: User) => {
  const items: Array<{
    action: string
    label: string
    icon: string
    color?: string
  }> = []
  
  // ... 添加操作项
  
  return items
}

// 修复 API 调用
const loadUsers = async () => {
  const response = await $apiClient('/users', { query: params })
  if (isSuccessResponse(response)) {
    users.value = response.data.users || response.data.items
  }
}
```

### 客户管理页面
```typescript
// 修复表格列定义
const columns: Column[] = [
  { key: 'name', label: '客户名称', type: 'text' },
  { key: 'status', label: '状态', type: 'badge' }
]

// 修复表单字段
const customerFields: FormField[] = [
  { name: 'name', label: '客户名称', type: 'text', required: true }
]
```

## 注意事项

1. **渐进式修复**: 不要一次性修复所有错误，按模块逐步修复
2. **测试验证**: 每次修复后运行 `npx vue-tsc --noEmit` 验证
3. **保持兼容**: 确保修复不影响现有功能
4. **文档更新**: 更新相关的类型定义文档

## 常见陷阱

1. **过度类型化**: 不要为了类型安全而过度复杂化代码
2. **忽略运行时错误**: 类型修复不能解决运行时错误
3. **破坏性更改**: 确保类型更改不会破坏现有 API
4. **性能影响**: 类型守卫可能影响性能，需要权衡 