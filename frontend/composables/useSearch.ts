import { debounce } from 'lodash-es'

export interface SearchOptions {
  debounceMs?: number
  defaultFilters?: Record<string, any>
}

export interface FilterConfig {
  key: string
  placeholder: string
  options: Array<{ label: string; value: string }>
}

export const useSearch = (
  loadFunction: (params?: any) => Promise<void>,
  options: SearchOptions = {}
) => {
  const { debounceMs = 300, defaultFilters = {} } = options

  // 搜索和筛选状态
  const searchQuery = ref('')
  const filters = ref({ ...defaultFilters })
  const isSearching = ref(false)

  // 防抖搜索函数
  const debouncedSearch = debounce(async (query: string, currentFilters: Record<string, any>) => {
    isSearching.value = true
    
    try {
      const params = {
        search: query,
        ...currentFilters
      }
      
      // 移除空值
      Object.keys(params).forEach(key => {
        if (params[key] === '' || params[key] === null || params[key] === undefined) {
          delete params[key]
        }
      })
      
      await loadFunction(params)
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      isSearching.value = false
    }
  }, debounceMs)

  // 搜索处理函数
  const handleSearch = (query: string) => {
    searchQuery.value = query
    debouncedSearch(query, filters.value)
  }

  // 筛选处理函数
  const handleFilter = (filterKey: string, filterValue: any) => {
    filters.value[filterKey] = filterValue
    debouncedSearch(searchQuery.value, filters.value)
  }

  // 批量设置筛选器
  const setFilters = (newFilters: Record<string, any>) => {
    filters.value = { ...filters.value, ...newFilters }
    debouncedSearch(searchQuery.value, filters.value)
  }

  // 重置搜索和筛选
  const resetSearch = () => {
    searchQuery.value = ''
    filters.value = { ...defaultFilters }
    loadFunction()
  }

  // 清除特定筛选器
  const clearFilter = (filterKey: string) => {
    delete filters.value[filterKey]
    debouncedSearch(searchQuery.value, filters.value)
  }

  // 获取当前搜索参数
  const getSearchParams = () => {
    const params = {
      search: searchQuery.value,
      ...filters.value
    }
    
    // 移除空值
    Object.keys(params).forEach(key => {
      if (params[key] === '' || params[key] === null || params[key] === undefined) {
        delete params[key]
      }
    })
    
    return params
  }

  // 检查是否有活动的搜索或筛选
  const hasActiveSearch = computed(() => {
    return searchQuery.value !== '' || Object.keys(filters.value).some(key => 
      filters.value[key] !== '' && filters.value[key] !== null && filters.value[key] !== undefined
    )
  })

  // 获取活动筛选器数量
  const activeFiltersCount = computed(() => {
    return Object.keys(filters.value).filter(key => 
      filters.value[key] !== '' && filters.value[key] !== null && filters.value[key] !== undefined
    ).length
  })

  return {
    // 状态
    searchQuery: readonly(searchQuery),
    filters: readonly(filters),
    isSearching: readonly(isSearching),
    hasActiveSearch,
    activeFiltersCount,
    
    // 方法
    handleSearch,
    handleFilter,
    setFilters,
    resetSearch,
    clearFilter,
    getSearchParams
  }
}

// 高级搜索组合函数
export const useAdvancedSearch = (
  loadFunction: (params?: any) => Promise<void>,
  options: SearchOptions = {}
) => {
  const basicSearch = useSearch(loadFunction, options)
  
  // 高级搜索状态
  const showAdvancedSearch = ref(false)
  const advancedFilters = ref<Record<string, any>>({})
  const savedSearches = ref<Array<{ name: string; params: any }>>([])

  // 应用高级筛选
  const applyAdvancedFilters = (filters: Record<string, any>) => {
    advancedFilters.value = { ...filters }
    const allFilters = { ...basicSearch.filters.value, ...filters }
    basicSearch.setFilters(allFilters)
  }

  // 保存搜索条件
  const saveSearch = (name: string) => {
    const params = {
      search: basicSearch.searchQuery.value,
      ...basicSearch.filters.value,
      ...advancedFilters.value
    }
    
    savedSearches.value.push({ name, params })
    
    // 保存到本地存储
    localStorage.setItem('saved-searches', JSON.stringify(savedSearches.value))
  }

  // 加载保存的搜索
  const loadSavedSearch = (searchName: string) => {
    const saved = savedSearches.value.find(s => s.name === searchName)
    if (saved) {
      const { search, ...filters } = saved.params
      basicSearch.handleSearch(search || '')
      basicSearch.setFilters(filters)
    }
  }

  // 删除保存的搜索
  const deleteSavedSearch = (searchName: string) => {
    savedSearches.value = savedSearches.value.filter(s => s.name !== searchName)
    localStorage.setItem('saved-searches', JSON.stringify(savedSearches.value))
  }

  // 从本地存储加载保存的搜索
  const loadSavedSearches = () => {
    try {
      const saved = localStorage.getItem('saved-searches')
      if (saved) {
        savedSearches.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('Load saved searches error:', error)
    }
  }

  // 重置高级搜索
  const resetAdvancedSearch = () => {
    advancedFilters.value = {}
    showAdvancedSearch.value = false
    basicSearch.resetSearch()
  }

  // 初始化时加载保存的搜索
  onMounted(() => {
    loadSavedSearches()
  })

  return {
    // 基础搜索功能
    ...basicSearch,
    
    // 高级搜索状态
    showAdvancedSearch,
    advancedFilters: readonly(advancedFilters),
    savedSearches: readonly(savedSearches),
    
    // 高级搜索方法
    applyAdvancedFilters,
    saveSearch,
    loadSavedSearch,
    deleteSavedSearch,
    resetAdvancedSearch
  }
}

// 搜索历史组合函数
export const useSearchHistory = (maxHistory = 10) => {
  const searchHistory = ref<string[]>([])
  
  // 添加搜索历史
  const addToHistory = (query: string) => {
    if (!query.trim()) return
    
    // 移除重复项
    const filtered = searchHistory.value.filter(item => item !== query)
    
    // 添加到开头
    searchHistory.value = [query, ...filtered].slice(0, maxHistory)
    
    // 保存到本地存储
    localStorage.setItem('search-history', JSON.stringify(searchHistory.value))
  }

  // 清除搜索历史
  const clearHistory = () => {
    searchHistory.value = []
    localStorage.removeItem('search-history')
  }

  // 删除特定历史记录
  const removeFromHistory = (query: string) => {
    searchHistory.value = searchHistory.value.filter(item => item !== query)
    localStorage.setItem('search-history', JSON.stringify(searchHistory.value))
  }

  // 从本地存储加载历史记录
  const loadHistory = () => {
    try {
      const history = localStorage.getItem('search-history')
      if (history) {
        searchHistory.value = JSON.parse(history)
      }
    } catch (error) {
      console.error('Load search history error:', error)
    }
  }

  // 初始化时加载历史记录
  onMounted(() => {
    loadHistory()
  })

  return {
    searchHistory: readonly(searchHistory),
    addToHistory,
    clearHistory,
    removeFromHistory
  }
}

// 搜索建议组合函数
export const useSearchSuggestions = (
  getSuggestions: (query: string) => Promise<string[]>,
  options: { debounceMs?: number; minLength?: number } = {}
) => {
  const { debounceMs = 300, minLength = 2 } = options
  
  const suggestions = ref<string[]>([])
  const loadingSuggestions = ref(false)
  const showSuggestions = ref(false)

  // 防抖获取建议
  const debouncedGetSuggestions = debounce(async (query: string) => {
    if (query.length < minLength) {
      suggestions.value = []
      showSuggestions.value = false
      return
    }

    loadingSuggestions.value = true
    
    try {
      const results = await getSuggestions(query)
      suggestions.value = results
      showSuggestions.value = results.length > 0
    } catch (error) {
      console.error('Get suggestions error:', error)
      suggestions.value = []
      showSuggestions.value = false
    } finally {
      loadingSuggestions.value = false
    }
  }, debounceMs)

  // 处理搜索输入
  const handleSearchInput = (query: string) => {
    debouncedGetSuggestions(query)
  }

  // 隐藏建议
  const hideSuggestions = () => {
    showSuggestions.value = false
  }

  // 选择建议
  const selectSuggestion = (suggestion: string) => {
    hideSuggestions()
    return suggestion
  }

  return {
    suggestions: readonly(suggestions),
    loadingSuggestions: readonly(loadingSuggestions),
    showSuggestions: readonly(showSuggestions),
    handleSearchInput,
    hideSuggestions,
    selectSuggestion
  }
}
