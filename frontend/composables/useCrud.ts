import { ref, reactive, computed } from 'vue'
import type { Ref } from 'vue'

interface CrudOptions {
  endpoint: string
  defaultPageSize?: number
  defaultSort?: string
  transformData?: (data: any) => any
  onSuccess?: (action: string, data?: any) => void
  onError?: (action: string, error: any) => void
}

interface PaginationState {
  page: number
  limit: number
  total: number
  totalPages: number
}

interface CrudState<T = any> {
  items: Ref<T[]>
  pagination: Ref<PaginationState>
  loading: Ref<boolean>
  creating: Ref<boolean>
  updating: Ref<boolean>
  deleting: Ref<boolean>
  selectedItem: Ref<T | null>
}

interface CrudActions {
  loadItems: (params?: Record<string, any>) => Promise<void>
  createItem: (data: any) => Promise<void>
  updateItem: (id: string | number, data: any) => Promise<void>
  deleteItem: (id: string | number) => Promise<void>
  setSelectedItem: (item: any) => void
  resetPagination: () => void
}

export function useCrud<T = any>(options: CrudOptions): CrudState<T> & CrudActions {
  const toast = useToast()
  const { $apiClient } = useNuxtApp()

  // 状态管理
  const items: Ref<T[]> = ref([])
  const pagination = ref<PaginationState>({
    page: 1,
    limit: options.defaultPageSize || 10,
    total: 0,
    totalPages: 0
  })

  const loading = ref(false)
  const creating = ref(false)
  const updating = ref(false)
  const deleting = ref(false)
  const selectedItem: Ref<T | null> = ref(null)
  
  // 加载数据
  const loadItems = async (params: Record<string, any> = {}) => {
    loading.value = true
    
    try {
      const queryParams = {
        page: pagination.value.page,
        limit: pagination.value.limit,
        sort: options.defaultSort,
        ...params
      }
      
      const response = await $apiClient(options.endpoint, { query: queryParams })
      
      let data = response.data.items
      if (options.transformData) {
        data = data.map(options.transformData)
      }
      
      items.value = data
      pagination.value = {
        page: response.data.page,
        limit: response.data.limit,
        total: response.data.total,
        totalPages: response.data.totalPages
      }
      
      options.onSuccess?.('load', data)
    } catch (error: any) {
      console.error(`Load ${options.endpoint} error:`, error)
      
      toast.add({
        title: '加载失败',
        description: '获取数据失败，请稍后重试',
        color: 'red'
      })
      
      options.onError?.('load', error)
    } finally {
      loading.value = false
    }
  }
  
  // 创建数据
  const createItem = async (data: any) => {
    creating.value = true
    
    try {
      const response = await $apiClient(options.endpoint, { method: 'POST', body: data })
      
      let newItem = response.data
      if (options.transformData) {
        newItem = options.transformData(newItem)
      }
      
      // 重新加载数据或直接添加到列表
      await loadItems()
      
      toast.add({
        title: '创建成功',
        description: '数据已创建',
        color: 'green'
      })
      
      options.onSuccess?.('create', newItem)
    } catch (error: any) {
      console.error(`Create ${options.endpoint} error:`, error)
      
      toast.add({
        title: '创建失败',
        description: error.message || '创建失败，请稍后重试',
        color: 'red'
      })
      
      options.onError?.('create', error)
      throw error
    } finally {
      creating.value = false
    }
  }
  
  // 更新数据
  const updateItem = async (id: string | number, data: any) => {
    updating.value = true
    
    try {
      const response = await $apiClient(`${options.endpoint}/${id}`, { method: 'PUT', body: data })
      
      let updatedItem = response.data
      if (options.transformData) {
        updatedItem = options.transformData(updatedItem)
      }
      
      // 更新本地数据
      const index = items.value.findIndex((item: any) => item.id === id)
      if (index !== -1) {
        items.value[index] = updatedItem
      }
      
      toast.add({
        title: '更新成功',
        description: '数据已更新',
        color: 'green'
      })
      
      options.onSuccess?.('update', updatedItem)
    } catch (error: any) {
      console.error(`Update ${options.endpoint} error:`, error)
      
      toast.add({
        title: '更新失败',
        description: error.message || '更新失败，请稍后重试',
        color: 'red'
      })
      
      options.onError?.('update', error)
      throw error
    } finally {
      updating.value = false
    }
  }
  
  // 删除数据
  const deleteItem = async (id: string | number) => {
    deleting.value = true
    
    try {
      await $apiClient(`${options.endpoint}/${id}`, { method: 'DELETE' })
      
      // 从本地数据中移除
      items.value = items.value.filter((item: any) => item.id !== id)
      
      // 如果当前页没有数据了，回到上一页
      if (items.value.length === 0 && pagination.value.page > 1) {
        pagination.value.page--
        await loadItems()
      }
      
      toast.add({
        title: '删除成功',
        description: '数据已删除',
        color: 'green'
      })
      
      options.onSuccess?.('delete', { id })
    } catch (error: any) {
      console.error(`Delete ${options.endpoint} error:`, error)
      
      toast.add({
        title: '删除失败',
        description: error.message || '删除失败，请稍后重试',
        color: 'red'
      })
      
      options.onError?.('delete', error)
      throw error
    } finally {
      deleting.value = false
    }
  }
  
  // 设置选中项
  const setSelectedItem = (item: T | null) => {
    selectedItem.value = item
  }
  
  // 重置分页
  const resetPagination = () => {
    pagination.value.page = 1
  }
  
  return {
    // 状态
    items,
    pagination,
    loading,
    creating,
    updating,
    deleting,
    selectedItem,
    
    // 操作
    loadItems,
    createItem,
    updateItem,
    deleteItem,
    setSelectedItem,
    resetPagination
  }
}

// 搜索和筛选组合函数
export function useCrudSearch(loadFunction: (params?: any) => Promise<void>) {
  const searchQuery = ref('')
  const filters = ref<Record<string, any>>({})
  
  // 防抖搜索
  const debounce = (func: Function, wait: number) => {
    let timeout: NodeJS.Timeout
    return (...args: any[]) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  }
  
  const debouncedSearch = debounce((query: string) => {
    loadFunction({ search: query, ...filters.value })
  }, 300)
  
  const handleSearch = (query: string) => {
    searchQuery.value = query
    debouncedSearch(query)
  }
  
  const handleFilter = (newFilters: Record<string, any>) => {
    filters.value = { ...filters.value, ...newFilters }
    loadFunction({ search: searchQuery.value, ...filters.value })
  }
  
  const resetFilters = () => {
    searchQuery.value = ''
    filters.value = {}
    loadFunction()
  }
  
  return {
    searchQuery,
    filters,
    handleSearch,
    handleFilter,
    resetFilters
  }
}

// 表单管理组合函数
export function useCrudForm<T extends object = any>(initialState: T) {
  const formState = reactive<T>({ ...initialState })
  const errors = ref<Record<string, string>>({})
  
  const resetForm = () => {
    Object.assign(formState, initialState)
    errors.value = {}
  }
  
  const setFormData = (data: Partial<T>) => {
    Object.assign(formState, data)
  }
  
  const validateField = (field: string, value: any, rules: any[]) => {
    for (const rule of rules) {
      const result = rule(value)
      if (result !== true) {
        errors.value[field] = result
        return false
      }
    }
    delete errors.value[field]
    return true
  }
  
  const hasErrors = computed(() => Object.keys(errors.value).length > 0)
  
  return {
    formState,
    errors,
    hasErrors,
    resetForm,
    setFormData,
    validateField
  }
}
