# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# 2 space indentation for most files
[*.{js,ts,vue,json,yml,yaml,css,scss,html,md}]
indent_style = space
indent_size = 2

# 4 space indentation for Python files
[*.py]
indent_style = space
indent_size = 4

# Tab indentation for Makefiles
[Makefile]
indent_style = tab

# No trailing whitespace for markdown files
[*.md]
trim_trailing_whitespace = false
