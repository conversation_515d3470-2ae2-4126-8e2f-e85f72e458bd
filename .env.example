# 应用配置
NODE_ENV=development
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001

# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/ops_management"

# Redis配置
REDIS_URL="redis://localhost:6379"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-at-least-32-characters-long"
JWT_EXPIRES_IN="15m"
REFRESH_TOKEN_SECRET="your-refresh-token-secret-key-at-least-32-characters"
REFRESH_TOKEN_EXPIRES_IN="7d"

# 加密配置
CRYPTO_SECRET="your-crypto-secret-key-must-be-32-chars-long"

# 邮件配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="运维服务管理系统"

# 短信配置（阿里云）
ALI_SMS_ACCESS_KEY_ID="your-access-key-id"
ALI_SMS_ACCESS_KEY_SECRET="your-access-key-secret"
ALI_SMS_REGION="cn-hangzhou"
ALI_SMS_SIGN_NAME="your-sign-name"

# 文件上传配置
UPLOAD_DIR="uploads"
MAX_FILE_SIZE="10485760"

# 限流配置
RATE_LIMIT_WINDOW_MS="900000"
RATE_LIMIT_MAX="100"

# 日志配置
LOG_LEVEL="info"
LOG_DIR="logs"