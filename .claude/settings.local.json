{"permissions": {"allow": ["Bash(npm install)", "Bash(cp:*)", "Bash(npx prisma generate:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "Bash(npm run lint)", "Bash(npx prisma migrate dev:*)", "Bash(timeout 10s npm run dev)", "<PERSON><PERSON>(timeout 5s npm run dev)", "Bash(npm run type-check:*)", "Bash(grep:*)", "Bash(timeout 60s npm run build)", "<PERSON><PERSON>(timeout 15s npm run dev)", "Bash(npx tsc:*)", "Bash(npx vue-tsc:*)", "Bash(rm:*)", "Bash(timeout 60s npm run type-check)"], "deny": []}}