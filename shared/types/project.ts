import { ArchiveStatus, Priority, ConfigType } from './common'

// 项目档案基础信息 (简化后的项目管理)
export interface ProjectArchive {
  id: string
  customerId: string
  name: string
  description?: string
  technology?: string // 技术栈
  environment?: string // 部署环境
  version?: string // 当前版本
  deploymentDate?: string // 部署日期
  status: ArchiveStatus
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 项目档案详细信息
export interface ProjectArchiveDetail extends ProjectArchive {
  customer: {
    id: string
    name: string
    company?: string
    level: string
  }
  createdByUser: {
    id: string
    username: string
    fullName?: string
  }
  configurations: ProjectConfiguration[]
  services: ArchiveService[]
  statistics?: ArchiveStatistics
}

// 运维配置信息
export interface ProjectConfiguration {
  id: string
  archiveId: string
  configType: ConfigType
  title: string // 配置项标题
  configData: any
  encryptedFields?: string[]
  description?: string
  isActive: boolean
  lastUpdated?: string
  createdAt: string
  updatedAt: string
}

// 档案服务摘要
export interface ArchiveService {
  id: string
  ticketNumber: string
  title: string
  category: string
  priority: Priority
  status: string
  assignedTo?: string
  startTime?: string
  endTime?: string
  createdAt: string
}

// 档案统计信息
export interface ArchiveStatistics {
  totalServices: number
  pendingServices: number
  inProgressServices: number
  completedServices: number
  avgResponseTime?: number
  avgResolutionTime?: number
  avgSatisfaction?: number
  lastServiceDate?: string
}

// 创建项目档案请求
export interface CreateArchiveRequest {
  customerId: string
  name: string
  description?: string
  technology?: string
  environment?: string
  version?: string
  deploymentDate?: string
}

// 更新项目档案请求
export interface UpdateArchiveRequest {
  name?: string
  description?: string
  technology?: string
  environment?: string
  version?: string
  deploymentDate?: string
  status?: ArchiveStatus
}

// 配置项请求
export interface ConfigurationRequest {
  configType: ConfigType
  title: string
  configData: any
  encryptedFields?: string[]
  description?: string
}

// 更新配置请求
export interface UpdateConfigurationRequest {
  title?: string
  configData?: any
  encryptedFields?: string[]
  description?: string
  isActive?: boolean
}

// 档案搜索参数
export interface ArchiveSearchParams {
  search?: string
  customerId?: string
  status?: ArchiveStatus
  technology?: string
  environment?: string
  createdBy?: string
  deploymentDate?: string
  sortBy?: 'name' | 'status' | 'deploymentDate' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

// 档案活动事件
export interface ArchiveActivityEvent {
  id: string
  archiveId: string
  type: 'CREATED' | 'UPDATED' | 'STATUS_CHANGED' | 'SERVICE_ADDED' | 'CONFIG_UPDATED' | 'VERSION_UPDATED'
  title: string
  description?: string
  metadata?: any
  userId: string
  userName: string
  timestamp: string
}

// 档案文档
export interface ArchiveDocument {
  id: string
  archiveId: string
  title: string
  description?: string
  type: 'MANUAL' | 'GUIDE' | 'REPORT' | 'CONFIG' | 'OTHER'
  filePath: string
  fileSize: number
  mimeType: string
  version: string
  uploadedBy: string
  uploadedAt: string
  tags?: string[]
}

// 档案服务报告
export interface ArchiveServiceReport {
  archiveId: string
  period: {
    startDate: string
    endDate: string
  }
  summary: {
    totalServices: number
    completedServices: number
    avgResponseTime: number
    avgResolutionTime: number
    customerSatisfaction?: number
    totalWorkHours: number
  }
  servicesByStatus: {
    status: string
    count: number
  }[]
  servicesByCategory: {
    category: string
    count: number
  }[]
  servicesByPriority: {
    priority: string
    count: number
  }[]
  monthlyTrend: {
    month: string
    serviceCount: number
    avgResponseTime: number
    satisfaction: number
  }[]
}