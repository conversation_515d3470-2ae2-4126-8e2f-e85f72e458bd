import { CustomerLevel } from './common'

// 客户基础信息
export interface Customer {
  id: string
  name: string
  company?: string
  industry?: string
  level: CustomerLevel
  contactPerson?: string
  email?: string
  phone?: string
  address?: string
  description?: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 客户详细信息（包含联系人和创建者）
export interface CustomerDetail extends Customer {
  contacts: CustomerContact[]
  createdByUser: {
    id: string
    username: string
    fullName?: string
  }
  projects?: CustomerProject[]
  statistics?: CustomerStatistics
}

// 客户联系人
export interface CustomerContact {
  id: string
  customerId: string
  name: string
  position?: string
  email?: string
  phone?: string
  isPrimary: boolean
  createdAt: string
  updatedAt: string
}

// 客户项目摘要
export interface CustomerProject {
  id: string
  name: string
  type: string
  status: string
  startDate?: string
  endDate?: string
}

// 客户统计信息
export interface CustomerStatistics {
  totalProjects: number
  activeProjects: number
  completedProjects: number
  totalServices: number
  avgResponseTime?: number
  avgResolutionTime?: number
  satisfactionScore?: number
  revenueTotal?: number
  lastActivityDate?: string
}

// 创建客户请求
export interface CreateCustomerRequest {
  name: string
  company?: string
  industry?: string
  level: CustomerLevel
  contactPerson?: string
  email?: string
  phone?: string
  address?: string
  description?: string
  contacts?: CreateCustomerContactRequest[]
}

// 更新客户请求
export interface UpdateCustomerRequest {
  name?: string
  company?: string
  industry?: string
  level?: CustomerLevel
  contactPerson?: string
  email?: string
  phone?: string
  address?: string
  description?: string
}

// 创建客户联系人请求
export interface CreateCustomerContactRequest {
  name: string
  position?: string
  email?: string
  phone?: string
  isPrimary?: boolean
}

// 更新客户联系人请求
export interface UpdateCustomerContactRequest {
  name?: string
  position?: string
  email?: string
  phone?: string
  isPrimary?: boolean
}

// 客户搜索参数
export interface CustomerSearchParams {
  search?: string
  level?: CustomerLevel
  industry?: string
  company?: string
  createdBy?: string
  startDate?: string
  endDate?: string
  sortBy?: 'name' | 'company' | 'level' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

// 客户导入数据
export interface CustomerImportData {
  name: string
  company?: string
  industry?: string
  level?: string
  contactPerson?: string
  email?: string
  phone?: string
  address?: string
  description?: string
}

// 客户导入结果
export interface CustomerImportResult {
  total: number
  success: number
  failed: number
  errors: {
    row: number
    errors: string[]
  }[]
  successIds: string[]
}

// 客户服务历史
export interface CustomerServiceHistory {
  id: string
  customerId: string
  projectId: string
  serviceId: string
  title: string
  description: string
  priority: string
  status: string
  assignedTo?: string
  startTime?: string
  endTime?: string
  responseTime?: number
  resolutionTime?: number
  createdAt: string
}

// 客户满意度评价
export interface CustomerSatisfaction {
  id: string
  customerId: string
  serviceId: string
  rating: number // 1-5分
  feedback?: string
  categories: {
    responseTime: number
    serviceQuality: number
    communication: number
    resolution: number
  }
  createdAt: string
}

// 客户合同信息
export interface CustomerContract {
  id: string
  customerId: string
  title: string
  type: 'MAINTENANCE' | 'DEVELOPMENT' | 'CONSULTING' | 'SUPPORT'
  startDate: string
  endDate: string
  value: number
  currency: string
  status: 'ACTIVE' | 'EXPIRED' | 'TERMINATED' | 'PENDING'
  terms?: string
  attachments?: string[]
  createdAt: string
  updatedAt: string
}

// 客户分析数据
export interface CustomerAnalytics {
  customerId: string
  period: 'WEEK' | 'MONTH' | 'QUARTER' | 'YEAR'
  metrics: {
    serviceCount: number
    avgResponseTime: number
    avgResolutionTime: number
    satisfactionScore: number
    projectCount: number
    revenue: number
  }
  trends: {
    serviceVolume: { date: string; count: number }[]
    responseTime: { date: string; time: number }[]
    satisfaction: { date: string; score: number }[]
  }
}