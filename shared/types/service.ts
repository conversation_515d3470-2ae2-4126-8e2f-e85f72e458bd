import { Priority, ServiceStatus, ServiceCategory, WorkCategory } from './common'

// SLA模板
export interface SlaTemplate {
  id: string
  name: string
  description?: string
  responseTime: number // 响应时间(分钟)
  resolutionTime: number // 解决时间(小时)
  availability: number // 可用性百分比
  createdAt: string
  updatedAt: string
}

// 运维服务基础信息
export interface Service {
  id: string
  archiveId: string // 关联项目档案
  slaTemplateId?: string
  ticketNumber: string // 工单号
  title: string
  description: string
  category: ServiceCategory // 服务类别
  priority: Priority
  status: ServiceStatus
  assignedTo?: string
  customerContact?: string // 客户联系人
  startTime?: string
  endTime?: string
  actualResponseTime?: number // 实际响应时间(分钟)
  actualResolutionTime?: number // 实际解决时间(小时)
  estimatedHours?: number // 预估工时
  actualHours?: number // 实际工时
  resolution?: string // 解决方案
  customerFeedback?: string // 客户反馈
  satisfaction?: number // 客户满意度(1-5)
  tags?: string[] // 标签
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 服务详细信息
export interface ServiceDetail extends Service {
  archive: {
    id: string
    name: string
    technology?: string
    environment?: string
    customer: {
      id: string
      name: string
      company?: string
    }
  }
  slaTemplate?: SlaTemplate
  assignedUser?: {
    id: string
    username: string
    fullName?: string
    email: string
    avatar?: string
  }
  createdByUser: {
    id: string
    username: string
    fullName?: string
  }
  timeline: ServiceTimelineEvent[]
  attachments: ServiceAttachment[]
  comments: ServiceComment[]
  metrics?: ServiceMetrics
}

// 服务时间线事件
export interface ServiceTimelineEvent {
  id: string
  serviceId: string
  type: 'CREATED' | 'ASSIGNED' | 'STATUS_CHANGED' | 'COMMENT_ADDED' | 'ATTACHMENT_ADDED' | 'SLA_BREACH'
  title: string
  description?: string
  metadata?: any
  userId: string
  userName: string
  timestamp: string
}

// 服务附件
export interface ServiceAttachment {
  id: string
  serviceId: string
  filename: string
  originalName: string
  filePath: string
  fileSize: number
  mimeType: string
  uploadedBy: string
  uploadedAt: string
}

// 服务评论
export interface ServiceComment {
  id: string
  serviceId: string
  content: string
  isInternal: boolean // 是否为内部评论（客户看不到）
  authorId: string
  authorName: string
  authorAvatar?: string
  createdAt: string
  updatedAt: string
}

// 服务指标
export interface ServiceMetrics {
  slaCompliance: {
    responseTime: {
      target: number
      actual: number
      isCompliant: boolean
    }
    resolutionTime: {
      target: number
      actual: number
      isCompliant: boolean
    }
  }
  customerSatisfaction?: {
    rating: number
    feedback?: string
  }
  timeSpent: {
    analysis: number
    implementation: number
    testing: number
    documentation: number
    total: number
  }
}

// 创建服务请求
export interface CreateServiceRequest {
  archiveId: string
  slaTemplateId?: string
  title: string
  description: string
  category: ServiceCategory
  priority: Priority
  assignedTo?: string
  customerContact?: string
  estimatedHours?: number
  tags?: string[]
}

// 更新服务请求
export interface UpdateServiceRequest {
  title?: string
  description?: string
  category?: ServiceCategory
  priority?: Priority
  status?: ServiceStatus
  assignedTo?: string
  customerContact?: string
  slaTemplateId?: string
  estimatedHours?: number
  actualHours?: number
  resolution?: string
  customerFeedback?: string
  satisfaction?: number
  tags?: string[]
}

// 服务状态更新请求
export interface UpdateServiceStatusRequest {
  status: ServiceStatus
  comment?: string
  timeSpent?: number
  attachments?: string[]
}

// 服务分配请求
export interface AssignServiceRequest {
  assignedTo: string
  comment?: string
  notifyAssignee?: boolean
}

// 服务搜索参数
export interface ServiceSearchParams {
  search?: string
  archiveId?: string
  customerId?: string
  category?: ServiceCategory
  priority?: Priority
  status?: ServiceStatus
  assignedTo?: string
  createdBy?: string
  slaTemplateId?: string
  startDate?: string
  endDate?: string
  slaBreached?: boolean
  tags?: string[]
  satisfaction?: number
  sortBy?: 'ticketNumber' | 'title' | 'category' | 'priority' | 'status' | 'createdAt' | 'updatedAt' | 'startTime' | 'endTime'
  sortOrder?: 'asc' | 'desc'
}

// 服务批量操作
export interface ServiceBatchOperation {
  serviceIds: string[]
  operation: 'assign' | 'updateStatus' | 'updatePriority' | 'addComment' | 'export'
  data?: {
    assignedTo?: string
    status?: ServiceStatus
    priority?: Priority
    comment?: string
    format?: 'xlsx' | 'csv' | 'pdf'
  }
}

// 服务统计
export interface ServiceStatistics {
  total: number
  byStatus: {
    status: ServiceStatus
    count: number
    percentage: number
  }[]
  byPriority: {
    priority: Priority
    count: number
    percentage: number
  }[]
  slaCompliance: {
    total: number
    compliant: number
    breached: number
    rate: number
  }
  avgMetrics: {
    responseTime: number
    resolutionTime: number
    customerSatisfaction?: number
  }
  trends: {
    period: string
    created: number
    completed: number
    breached: number
  }[]
}

// 服务模板
export interface ServiceTemplate {
  id: string
  name: string
  description?: string
  category: string
  priority: Priority
  estimatedTime: number // 预估时间(小时)
  slaTemplateId?: string
  checklist: ServiceChecklistItem[]
  tags: string[]
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 服务检查清单项
export interface ServiceChecklistItem {
  id: string
  title: string
  description?: string
  isRequired: boolean
  order: number
}

// 服务工作日志
export interface ServiceWorkLog {
  id: string
  serviceId: string
  userId: string
  userName: string
  description: string
  workHours: number // 工作时长(小时)
  workDate: string
  category: WorkCategory
  createdAt: string
}

// 服务知识库文章
export interface ServiceKnowledgeBase {
  id: string
  title: string
  content: string
  category: string
  tags: string[]
  relatedServices: string[]
  isPublic: boolean
  authorId: string
  authorName: string
  views: number
  createdAt: string
  updatedAt: string
}