import { UserStatus } from './common'

// 用户基础信息
export interface User {
  id: string
  username: string
  email: string
  fullName?: string
  phone?: string
  avatar?: string
  status: UserStatus
  createdAt: string
  updatedAt: string
}

// 用户详细信息（包含角色）
export interface UserDetail extends User {
  roles: UserRole[]
}

// 用户角色关联
export interface UserRole {
  userId: string
  roleId: string
  role: {
    id: string
    name: string
    description?: string
    permissions: string[]
  }
  assignedAt: string
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  fullName?: string
  phone?: string
  roleIds?: string[]
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string
  email?: string
  fullName?: string
  phone?: string
  avatar?: string
  status?: UserStatus
}

// 更新密码请求
export interface UpdatePasswordRequest {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

// 用户登录请求
export interface LoginRequest {
  username: string
  password: string
  rememberMe?: boolean
}

// 登录响应
export interface LoginResponse {
  user: User
  accessToken: string
  refreshToken: string
  expiresIn: number
}

// 刷新令牌请求
export interface RefreshTokenRequest {
  refreshToken: string
}

// 用户配置
export interface UserProfile {
  id: string
  userId: string
  theme: 'light' | 'dark' | 'auto'
  language: string
  timezone: string
  notifications: {
    email: boolean
    sms: boolean
    system: boolean
  }
  preferences: {
    pageSize: number
    defaultView: string
    autoRefresh: boolean
    [key: string]: any
  }
}

// 用户统计信息
export interface UserStatistics {
  totalProjects: number
  activeProjects: number
  completedServices: number
  pendingServices: number
  weeklyActivity: {
    date: string
    count: number
  }[]
}

// 用户权限信息
export interface UserPermissions {
  userId: string
  roles: string[]
  permissions: string[]
  canAccess: (resource: string, action: string) => boolean
}

// 用户搜索参数
export interface UserSearchParams {
  search?: string
  status?: UserStatus
  role?: string
  department?: string
  sortBy?: 'username' | 'email' | 'createdAt' | 'updatedAt'
  sortOrder?: 'asc' | 'desc'
}

// 用户批量操作
export interface UserBatchOperation {
  userIds: string[]
  operation: 'activate' | 'deactivate' | 'delete' | 'assignRole' | 'removeRole'
  data?: {
    roleId?: string
    reason?: string
  }
}

// 用户会话信息
export interface UserSession {
  sessionId: string
  userId: string
  userAgent: string
  ipAddress: string
  location?: string
  isActive: boolean
  lastActivity: string
  createdAt: string
}

// 用户活动日志
export interface UserActivity {
  id: string
  userId: string
  action: string
  resource: string
  resourceId?: string
  description: string
  metadata?: any
  ipAddress?: string
  userAgent?: string
  timestamp: string
}