import { NotificationType, NotificationStatus } from './common'

// 通知模板
export interface NotificationTemplate {
  id: string
  name: string
  type: NotificationType
  subject: string
  content: string
  variables?: { [key: string]: string } // 可用变量及其描述
  createdAt: string
  updatedAt: string
}

// 通知记录
export interface Notification {
  id: string
  templateId?: string
  recipient: string // 接收者邮箱或手机号
  type: NotificationType
  subject: string
  content: string
  status: NotificationStatus
  sentAt?: string
  createdAt: string
  metadata?: {
    retryCount?: number
    errorMessage?: string
    deliveredAt?: string
    readAt?: string
    clickedAt?: string
  }
}

// 发送通知请求
export interface SendNotificationRequest {
  templateId?: string
  recipients: string[]
  type: NotificationType
  subject?: string
  content?: string
  variables?: { [key: string]: any }
  priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
  scheduleAt?: string // 定时发送
}

// 批量发送通知请求
export interface BatchNotificationRequest {
  templateId: string
  recipients: {
    email?: string
    phone?: string
    variables?: { [key: string]: any }
  }[]
  type: NotificationType
  priority?: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'
}

// 通知偏好设置
export interface NotificationPreferences {
  userId: string
  email: {
    enabled: boolean
    types: NotificationEventType[]
  }
  sms: {
    enabled: boolean
    types: NotificationEventType[]
  }
  system: {
    enabled: boolean
    types: NotificationEventType[]
  }
  frequency: 'IMMEDIATE' | 'HOURLY' | 'DAILY' | 'WEEKLY'
  quietHours: {
    enabled: boolean
    startTime: string // HH:mm
    endTime: string // HH:mm
    timezone: string
  }
}

// 通知事件类型
export enum NotificationEventType {
  SERVICE_CREATED = 'SERVICE_CREATED',
  SERVICE_ASSIGNED = 'SERVICE_ASSIGNED',
  SERVICE_STATUS_CHANGED = 'SERVICE_STATUS_CHANGED',
  SERVICE_COMPLETED = 'SERVICE_COMPLETED',
  SLA_BREACH_WARNING = 'SLA_BREACH_WARNING',
  SLA_BREACH = 'SLA_BREACH',
  PROJECT_CREATED = 'PROJECT_CREATED',
  PROJECT_UPDATED = 'PROJECT_UPDATED',
  PROJECT_COMPLETED = 'PROJECT_COMPLETED',
  CUSTOMER_CREATED = 'CUSTOMER_CREATED',
  USER_ASSIGNED_TO_PROJECT = 'USER_ASSIGNED_TO_PROJECT',
  SYSTEM_MAINTENANCE = 'SYSTEM_MAINTENANCE',
  SECURITY_ALERT = 'SECURITY_ALERT',
  WEEKLY_REPORT = 'WEEKLY_REPORT',
  MONTHLY_REPORT = 'MONTHLY_REPORT'
}

// 通知事件
export interface NotificationEvent {
  type: NotificationEventType
  data: any
  triggeredBy?: string
  timestamp: string
}

// 自动通知规则
export interface NotificationRule {
  id: string
  name: string
  description?: string
  eventType: NotificationEventType
  conditions: NotificationCondition[]
  actions: NotificationAction[]
  isActive: boolean
  createdBy: string
  createdAt: string
  updatedAt: string
}

// 通知条件
export interface NotificationCondition {
  field: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than' | 'in' | 'not_in'
  value: any
  logicalOperator?: 'AND' | 'OR'
}

// 通知动作
export interface NotificationAction {
  type: 'SEND_EMAIL' | 'SEND_SMS' | 'SEND_SYSTEM' | 'WEBHOOK' | 'SLACK'
  templateId?: string
  recipients: NotificationRecipient[]
  delay?: number // 延迟发送(分钟)
  config?: { [key: string]: any } // 额外配置
}

// 通知接收者
export interface NotificationRecipient {
  type: 'USER' | 'ROLE' | 'EMAIL' | 'PHONE' | 'WEBHOOK'
  value: string
  variables?: { [key: string]: any }
}

// 通知统计
export interface NotificationStatistics {
  total: number
  sent: number
  pending: number
  failed: number
  byType: {
    type: NotificationType
    count: number
    successRate: number
  }[]
  byTemplate: {
    templateId: string
    templateName: string
    count: number
    successRate: number
  }[]
  trends: {
    date: string
    sent: number
    failed: number
  }[]
  recentFailures: {
    id: string
    recipient: string
    type: NotificationType
    error: string
    timestamp: string
  }[]
}

// 通知队列项
export interface NotificationQueueItem {
  id: string
  type: NotificationType
  recipient: string
  subject: string
  content: string
  priority: number
  maxRetries: number
  currentRetries: number
  scheduleAt: string
  createdAt: string
  processingAt?: string
  metadata?: any
}

// 通知webhook配置
export interface NotificationWebhook {
  id: string
  name: string
  url: string
  secret?: string
  events: NotificationEventType[]
  headers?: { [key: string]: string }
  isActive: boolean
  retryPolicy: {
    maxRetries: number
    retryDelay: number // 秒
    backoffMultiplier: number
  }
  createdAt: string
  updatedAt: string
}

// 通知搜索参数
export interface NotificationSearchParams {
  search?: string
  type?: NotificationType
  status?: NotificationStatus
  recipient?: string
  templateId?: string
  startDate?: string
  endDate?: string
  sortBy?: 'createdAt' | 'sentAt' | 'recipient' | 'subject'
  sortOrder?: 'asc' | 'desc'
}