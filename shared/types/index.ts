// 导出所有类型定义
export * from './common'
export * from './user'
export * from './customer'
export * from './project'
export * from './service'
export * from './notification'

// 导出常用工具类型
export type ID = string
export type Timestamp = string
export type JSONValue = string | number | boolean | null | JSONObject | JSONArray
export interface JSONObject {
  [key: string]: JSONValue
}
export interface JSONArray extends Array<JSONValue> {}

// API相关类型
export interface RequestConfig {
  timeout?: number
  retries?: number
  cache?: boolean
  cacheTime?: number
}

export interface ApiRequestOptions extends RequestConfig {
  headers?: Record<string, string>
  params?: Record<string, any>
  signal?: AbortSignal
}

// 表单验证相关
export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  min?: number
  max?: number
  email?: boolean
  phone?: boolean
  url?: boolean
  custom?: (value: any) => boolean | string
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url' | 'textarea' | 'select' | 'checkbox' | 'radio' | 'date' | 'datetime' | 'file'
  placeholder?: string
  defaultValue?: any
  options?: { label: string; value: any }[]
  rules?: ValidationRule[]
  disabled?: boolean
  readonly?: boolean
  hidden?: boolean
}

export interface FormConfig {
  fields: FormField[]
  submitLabel?: string
  resetLabel?: string
  layout?: 'vertical' | 'horizontal' | 'inline'
  size?: 'small' | 'medium' | 'large'
}

// 表格相关
export interface TableColumn {
  key: string
  title: string
  dataIndex?: string
  width?: number | string
  align?: 'left' | 'center' | 'right'
  sortable?: boolean
  filterable?: boolean
  fixed?: 'left' | 'right'
  render?: (value: any, record: any, index: number) => any
  sorter?: (a: any, b: any) => number
  filters?: { text: string; value: any }[]
}

export interface TableConfig {
  columns: TableColumn[]
  rowKey?: string
  size?: 'small' | 'medium' | 'large'
  bordered?: boolean
  striped?: boolean
  loading?: boolean
  showHeader?: boolean
  showPagination?: boolean
  pageSize?: number
  total?: number
  current?: number
}

// 菜单相关
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permission?: string
  badge?: string | number
  disabled?: boolean
  hidden?: boolean
}

// 路由相关
export interface RouteConfig {
  path: string
  name: string
  component?: any
  meta?: {
    title?: string
    description?: string
    keywords?: string
    auth?: boolean
    roles?: string[]
    permissions?: string[]
    layout?: string
    cache?: boolean
    breadcrumb?: boolean
  }
  children?: RouteConfig[]
}

// 主题相关
export interface ThemeConfig {
  primaryColor: string
  secondaryColor: string
  successColor: string
  warningColor: string
  errorColor: string
  infoColor: string
  backgroundColor: string
  surfaceColor: string
  textColor: string
  borderColor: string
  shadowColor: string
  borderRadius: string
  fontSize: {
    small: string
    medium: string
    large: string
    xlarge: string
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
}

// 应用配置
export interface AppConfig {
  name: string
  version: string
  description: string
  author: string
  homepage: string
  apiUrl: string
  cdnUrl?: string
  locale: string
  theme: 'light' | 'dark' | 'auto'
  features: {
    [key: string]: boolean
  }
  limits: {
    maxFileSize: number
    maxFiles: number
    pageSize: number
    cacheTTL: number
  }
}